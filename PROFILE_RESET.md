# Profile Reset Functionality

## Overview

The Profile Reset feature allows users to completely reset their learning progress while preserving their account data and premium access. This is useful for users who want to start fresh with their learning journey.

## What Gets Reset

### ✅ **Deleted Data:**
- **All Level Progress**: All completed and in-progress levels
- **All Achievements**: All earned badges and achievements
- **All Scores**: All points and scoring data
- **All Code Submissions**: All submitted code and attempts
- **User Progress Records**: All progress tracking data

### 🛡️ **Preserved Data:**
- **Account Information**: Username, email, password
- **Premium Access**: All premium course access permissions
- **Individual Course Access**: Manually granted course permissions
- **Admin Privileges**: Admin rights and permissions
- **Account Settings**: Language preferences and other settings

## User Interface

### Location
The reset functionality is located in the user's profile page (`/profile`) in a dedicated "Danger Zone" section.

### Visual Design
- **Red-themed warning section** with clear visual indicators
- **Detailed explanation** of what will be deleted vs. preserved
- **Multiple confirmation steps** to prevent accidental resets

### Safety Features
1. **First Confirmation**: Standard browser confirm dialog
2. **Type Confirmation**: User must type "RESET" exactly
3. **Final Warning**: Last chance to cancel with clear warning
4. **Loading State**: Button shows spinner during reset process

## Technical Implementation

### Backend Route
```javascript
POST /profile/reset-progress
```

### Database Operations
The reset process executes the following SQL operations:
1. `DELETE FROM user_progress WHERE user_id = ?`
2. `DELETE FROM user_achievements WHERE user_id = ?`
3. `DELETE FROM code_submissions WHERE user_id = ?`
4. `DELETE FROM user_level_completions WHERE user_id = ?`
5. `DELETE FROM user_submissions WHERE user_id = ?`

### Error Handling
- Graceful handling of missing tables
- Detailed logging of affected rows
- Rollback protection for partial failures
- User-friendly error messages

## Security Considerations

### Authentication Required
- Only authenticated users can reset their own progress
- No admin override - users must reset their own accounts
- Session validation ensures user identity

### Audit Trail
- All reset operations are logged with timestamps
- User ID and affected row counts are recorded
- Server logs provide complete audit trail

### Data Protection
- Premium access and course permissions are never affected
- Account credentials remain unchanged
- Admin privileges are preserved

## User Experience

### Confirmation Flow
1. User clicks "Reset Progress" button
2. First confirmation: "Are you sure?"
3. Type confirmation: Must type "RESET" exactly
4. Final warning: Last chance to cancel
5. Processing: Loading spinner with status
6. Success: Confirmation message and page reload

### Multilingual Support
All text and messages are fully translated in:
- English (en)
- German (de)
- Czech (cs)
- French (fr)

### Responsive Design
- Works on all device sizes
- Touch-friendly buttons and interactions
- Clear visual hierarchy and spacing

## Translation Keys

### Main Interface
- `profile.danger_zone` - Section title
- `profile.reset_progress` - Feature title
- `profile.reset_progress_desc` - Description text
- `profile.reset_progress_btn` - Button text

### Data Categories
- `profile.will_be_deleted` - Deleted data section
- `profile.will_be_kept` - Preserved data section
- `profile.all_level_progress` - Progress data
- `profile.all_achievements` - Achievement data
- `profile.all_scores` - Score data
- `profile.all_submissions` - Submission data

### Confirmation Messages
- `profile.reset_confirm_1` - First confirmation
- `profile.reset_confirm_2` - Type confirmation prompt
- `profile.reset_confirm_3` - Final warning
- `profile.reset_cancelled` - Cancellation message
- `profile.resetting` - Loading state text
- `profile.reset_success` - Success message
- `profile.reset_error` - Error message

## Testing

### Manual Testing Steps
1. Create user account with progress data
2. Navigate to profile page
3. Scroll to "Danger Zone" section
4. Click "Reset Progress" button
5. Test all confirmation steps
6. Verify data deletion and preservation
7. Check server logs for proper logging

### Database Verification
```sql
-- Check user progress before reset
SELECT COUNT(*) FROM user_progress WHERE user_id = ?;
SELECT COUNT(*) FROM user_achievements WHERE user_id = ?;
SELECT COUNT(*) FROM code_submissions WHERE user_id = ?;

-- Verify account data preservation
SELECT username, email, is_admin FROM users WHERE id = ?;
SELECT * FROM user_course_access WHERE user_id = ?;
```

## Troubleshooting

### Common Issues
1. **Button not responding**: Check browser console for JavaScript errors
2. **Reset not working**: Verify user authentication and database connection
3. **Partial reset**: Check server logs for database errors

### Server Logs
Reset operations produce detailed logs:
```
User 1 requested progress reset
Successfully reset progress for user 1:
  - Query 1: 5 rows affected
  - Query 2: 3 rows affected
  - Query 3: 8 rows affected
```

### Recovery
**Important**: There is no undo functionality. Once reset, data cannot be recovered. Users should be clearly warned about this permanent action.

## Future Enhancements

### Potential Improvements
- Export progress data before reset
- Selective reset (choose what to reset)
- Backup and restore functionality
- Admin-initiated resets with user consent
- Progress reset history tracking

### Database Optimizations
- Batch operations for better performance
- Transaction support for atomic operations
- Soft delete with recovery period
- Automated cleanup of orphaned data
