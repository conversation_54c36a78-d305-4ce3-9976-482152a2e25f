const Database = require('./database/db');

console.log('🔍 Checking All Courses and Levels...');

const db = new Database();

db.getAllCourses((err, courses) => {
    if (err) {
        console.error('Error:', err);
        return;
    }
    
    console.log(`\n📊 Found ${courses.length} courses:`);
    
    let totalChecked = 0;
    
    courses.forEach((course, index) => {
        db.getLevelsByCourse(course.id, (err, levels) => {
            if (err) {
                console.error(`Error for course ${course.id}:`, err);
            } else {
                const bossLevels = levels.filter(l => l.title.includes('BOSS')).length;
                console.log(`\n${course.icon} ${course.name} (ID: ${course.id})`);
                console.log(`  📚 Levels: ${levels.length}/40`);
                console.log(`  🏆 Boss Levels: ${bossLevels}`);
                console.log(`  📝 Slug: ${course.slug}`);
                
                if (levels.length < 40) {
                    console.log(`  ❌ MISSING: ${40 - levels.length} levels`);
                } else {
                    console.log(`  ✅ COMPLETE`);
                }
                
                // Show first few levels
                if (levels.length > 0) {
                    console.log(`  First levels:`);
                    levels.slice(0, 3).forEach(level => {
                        const icon = level.title.includes('BOSS') ? '🏆' : '📝';
                        console.log(`    ${icon} Level ${level.level_number}: ${level.title}`);
                    });
                    if (levels.length > 3) {
                        console.log(`    ... and ${levels.length - 3} more`);
                    }
                }
            }
            
            totalChecked++;
            if (totalChecked === courses.length) {
                console.log('\n🎯 Summary:');
                console.log('  - Check which courses are accessible in the frontend');
                console.log('  - Verify course slugs match frontend routing');
                console.log('  - Ensure all courses have 40 levels with Boss Levels every 10');
                
                db.close();
            }
        });
    });
});
