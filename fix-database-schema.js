const sqlite3 = require('sqlite3').verbose();
const { dbPath } = require('./database/init');

console.log('🔧 Fixing Database Schema...');

const directDb = new sqlite3.Database(dbPath);

directDb.serialize(() => {
    // Check current schema
    directDb.all("PRAGMA table_info(levels)", (err, columns) => {
        if (err) {
            console.error('Error checking schema:', err);
            return;
        }
        
        console.log('Current levels table columns:');
        columns.forEach(col => {
            console.log(`  - ${col.name}: ${col.type}`);
        });
        
        // Check if points column exists
        const hasPoints = columns.some(col => col.name === 'points');
        const hasExerciseType = columns.some(col => col.name === 'exercise_type');
        
        if (!hasPoints) {
            console.log('Adding points column...');
            directDb.run('ALTER TABLE levels ADD COLUMN points INTEGER DEFAULT 10', (err) => {
                if (err) {
                    console.error('Error adding points column:', err);
                } else {
                    console.log('✅ Points column added!');
                }
            });
        }
        
        if (!hasExerciseType) {
            console.log('Adding exercise_type column...');
            directDb.run('ALTER TABLE levels ADD COLUMN exercise_type TEXT DEFAULT "code_example"', (err) => {
                if (err) {
                    console.error('Error adding exercise_type column:', err);
                } else {
                    console.log('✅ Exercise_type column added!');
                }
            });
        }
        
        // Check courses table
        directDb.all("PRAGMA table_info(courses)", (err, courseColumns) => {
            if (err) {
                console.error('Error checking courses schema:', err);
                return;
            }
            
            console.log('\nCurrent courses table columns:');
            courseColumns.forEach(col => {
                console.log(`  - ${col.name}: ${col.type}`);
            });
            
            const hasIcon = courseColumns.some(col => col.name === 'icon');
            const hasColor = courseColumns.some(col => col.name === 'color');
            
            if (!hasIcon) {
                console.log('Adding icon column to courses...');
                directDb.run('ALTER TABLE courses ADD COLUMN icon TEXT DEFAULT "📚"', (err) => {
                    if (err) {
                        console.error('Error adding icon column:', err);
                    } else {
                        console.log('✅ Icon column added to courses!');
                    }
                });
            }
            
            if (!hasColor) {
                console.log('Adding color column to courses...');
                directDb.run('ALTER TABLE courses ADD COLUMN color TEXT DEFAULT "#007bff"', (err) => {
                    if (err) {
                        console.error('Error adding color column:', err);
                    } else {
                        console.log('✅ Color column added to courses!');
                    }
                });
            }
            
            // Check achievements table
            directDb.all("PRAGMA table_info(achievements)", (err, achievementColumns) => {
                if (err) {
                    console.error('Error checking achievements schema:', err);
                    return;
                }
                
                console.log('\nCurrent achievements table columns:');
                achievementColumns.forEach(col => {
                    console.log(`  - ${col.name}: ${col.type}`);
                });
                
                const hasAchievementPoints = achievementColumns.some(col => col.name === 'points');
                const hasAchievementIcon = achievementColumns.some(col => col.name === 'icon');
                const hasLanguage = achievementColumns.some(col => col.name === 'language');
                
                if (!hasAchievementPoints) {
                    console.log('Adding points column to achievements...');
                    directDb.run('ALTER TABLE achievements ADD COLUMN points INTEGER DEFAULT 10', (err) => {
                        if (err) {
                            console.error('Error adding points column to achievements:', err);
                        } else {
                            console.log('✅ Points column added to achievements!');
                        }
                    });
                }
                
                if (!hasAchievementIcon) {
                    console.log('Adding icon column to achievements...');
                    directDb.run('ALTER TABLE achievements ADD COLUMN icon TEXT DEFAULT "🏆"', (err) => {
                        if (err) {
                            console.error('Error adding icon column to achievements:', err);
                        } else {
                            console.log('✅ Icon column added to achievements!');
                        }
                    });
                }
                
                if (!hasLanguage) {
                    console.log('Adding language column to achievements...');
                    directDb.run('ALTER TABLE achievements ADD COLUMN language TEXT DEFAULT "general"', (err) => {
                        if (err) {
                            console.error('Error adding language column to achievements:', err);
                        } else {
                            console.log('✅ Language column added to achievements!');
                        }
                    });
                }
                
                setTimeout(() => {
                    directDb.close();
                    console.log('\n🎉 Database schema fixed! You can now run the level creation scripts.');
                }, 1000);
            });
        });
    });
});
