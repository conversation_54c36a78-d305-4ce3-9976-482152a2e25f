const Database = require('../database/db');
const db = new Database();

// Middleware to check if user is authenticated
function requireAuth(req, res, next) {
    if (!req.session.userId) {
        return res.redirect('/login');
    }
    next();
}

// Middleware to check if user is admin
function requireAdmin(req, res, next) {
    console.log('requireAdmin: checking session userId:', req.session.userId);
    if (!req.session.userId) {
        console.log('requireAdmin: No userId, redirecting to login');
        return res.redirect('/login');
    }

    // Check if user is admin
    db.getUserById(req.session.userId, (err, user) => {
        if (err) {
            console.error('Error checking admin status:', err);
            return res.status(500).send('Internal server error');
        }

        if (!user || !user.is_admin) {
            console.log('requireAdmin: User not admin or not found:', user ? user.username : 'null', 'is_admin:', user ? user.is_admin : 'null');
            return res.status(403).render('error', {
                title: 'Access Denied',
                message: 'You do not have permission to access this area.',
                error: { status: 403 },
                username: req.session.username,
                currentLanguage: req.session.language || 'en'
            });
        }

        // Check if user is banned
        if (user.is_banned) {
            req.session.destroy();
            return res.redirect('/login?error=account_banned');
        }

        console.log('requireAdmin: Admin access granted for user:', user.username);
        req.user = user;
        next();
    });
}

// Middleware to check if user is banned (for regular routes)
function checkBanned(req, res, next) {
    // Check if session exists and has userId
    if (!req.session || !req.session.userId) {
        return next();
    }

    db.getUserById(req.session.userId, (err, user) => {
        if (err) {
            console.error('Error checking ban status:', err);
            return next();
        }

        if (user && user.is_banned) {
            req.session.destroy();
            return res.redirect('/login?error=account_banned');
        }

        next();
    });
}

// Function to check course access
function checkCourseAccess(req, res, next) {
    console.log('checkCourseAccess: checking session userId:', req.session.userId);
    if (!req.session.userId) {
        return res.redirect('/login');
    }

    const courseSlug = req.params.courseSlug || req.params.slug;
    console.log('checkCourseAccess: checking access for course:', courseSlug);

    // Get course information
    db.getCourseBySlug(courseSlug, (err, course) => {
        if (err || !course) {
            console.log('checkCourseAccess: Course not found:', courseSlug);
            return res.status(404).render('error', {
                title: 'Course Not Found',
                message: 'The requested course could not be found.',
                error: { status: 404 },
                username: req.session.username,
                currentLanguage: req.session.language || 'en'
            });
        }

        // Free courses (HTML, CSS/JS, Python)
        const freeCourses = ['html-css-js', 'javascript', 'python'];
        
        if (freeCourses.includes(course.slug)) {
            req.course = course;
            return next();
        }

        // Premium courses - check global setting and individual access
        db.getAdminSetting('global_premium_enabled', (err, setting) => {
            if (err) {
                console.error('Error checking premium setting:', err);
                return res.status(500).send('Internal server error');
            }

            const globalPremiumEnabled = setting && setting.setting_value === 'true';

            if (globalPremiumEnabled) {
                req.course = course;
                return next();
            }

            // Check individual course access
            db.getUserCourseAccess(req.session.userId, (err, accessList) => {
                if (err) {
                    console.error('Error checking course access:', err);
                    return res.status(500).send('Internal server error');
                }

                const hasAccess = accessList.some(access => access.course_id === course.id);

                if (hasAccess) {
                    req.course = course;
                    return next();
                }

                // No access to premium course
                console.log('checkCourseAccess: No access to premium course:', course.slug);
                return res.status(403).render('error', {
                    title: 'Premium Course',
                    message: 'This is a premium course. Please contact an administrator for access.',
                    error: { status: 403 },
                    username: req.session.username,
                    currentLanguage: req.session.language || 'en'
                });
            });
        });
    });
}

module.exports = {
    requireAuth,
    requireAdmin,
    checkBanned,
    checkCourseAccess
};
