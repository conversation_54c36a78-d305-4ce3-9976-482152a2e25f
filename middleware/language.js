const geoip = require('geoip-lite');

// Language detection based on IP and user preference
function detectLanguage(req, res, next) {
    let language = 'en'; // Default to English
    
    // Check if user has set a language preference in session
    if (req.session && req.session.language) {
        language = req.session.language;
        console.log('detectLanguage: User has language preference:', language);
        
        // Store detected language in session
        if (req.session) {
            req.session.language = language;
        }
    }
    
    req.language = language;
    next();
}

// Translation middleware
function loadTranslations(db) {
    return (req, res, next) => {
        const language = req.language || 'en';
        
        db.getTranslations(language, (err, translations) => {
            if (err) {
                console.error('Error loading translations:', err);
                req.translations = {};
            } else {
                req.translations = translations || {};
            }
            
            // Helper function for templates
            res.locals.t = (key, defaultValue = key) => {
                return req.translations[key] || defaultValue;
            };
            
            // Make language available in templates
            res.locals.currentLanguage = language;
            res.locals.translations = req.translations;
            
            next();
        });
    };
}

// Language switcher route handler
function createLanguageSwitcher(db) {
    return (req, res) => {
        const newLanguage = req.params.language;
        const supportedLanguages = ['en', 'de', 'cs', 'fr'];
        
        if (supportedLanguages.includes(newLanguage)) {
            req.session.language = newLanguage;
            res.json({ success: true, language: newLanguage });
        } else {
            res.status(400).json({ success: false, error: 'Unsupported language' });
        }
    };
}

module.exports = {
    detectLanguage,
    loadTranslations,
    createLanguageSwitcher
};
