# Email-Features für CodeWave

Diese Dokumentation beschreibt die neuen Email-Features, die in CodeWave implementiert wurden.

## 🚀 Neue Features

### 1. Email-Verifikation bei Registrierung
- **Beschreibung**: Neue Benutzer müssen ihre Email-Adresse bestätigen, bevor sie sich anmelden können
- **Funktionalität**: 
  - Bei der Registrierung wird eine Bestätigungs-Email gesendet
  - Der Benutzer muss auf den Link in der Email klicken
  - Erst nach der Bestätigung ist die Anmeldung möglich
- **Sicherheit**: 24-Stunden-Gültigkeit für Verifikations-Token

### 2. Passwort-Reset-Funktion
- **Beschreibung**: Benutzer können ihr Passwort zurücksetzen, wenn sie es vergessen haben
- **Funktionalität**:
  - "Passwort vergessen?" Link auf der Login-Seite
  - Email mit Reset-Link wird gesendet
  - Sicherer Token-basierter Reset-Prozess
- **Sicherheit**: 1-Stunden-Gültigkeit für Reset-Token, einmalige Verwendung

### 3. Admin Email-Versand
- **Beschreibung**: Administratoren können Emails von @codewave.online Adressen versenden
- **Funktionalität**:
  - Neuer Bereich im Admin Panel
  - Auswahl der Absender-Adresse (@codewave.online)
  - Versand an beliebige Email-Adressen
  - Schöne HTML-Templates für alle Emails

## 📋 Technische Details

### Verwendete Technologien
- **Email-Service**: Resend (https://resend.com)
- **Template-Engine**: Eigene HTML-Templates mit Inline-CSS
- **Token-Generierung**: Node.js crypto.randomBytes()
- **Datenbank**: SQLite mit neuen Tabellen für Tokens

### Neue Datenbankstrukturen

#### Email Verification Tokens
```sql
CREATE TABLE email_verification_tokens (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    token VARCHAR(255) UNIQUE NOT NULL,
    expires_at DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### Password Reset Tokens
```sql
CREATE TABLE password_reset_tokens (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    token VARCHAR(255) UNIQUE NOT NULL,
    expires_at DATETIME NOT NULL,
    used BOOLEAN DEFAULT FALSE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

#### Users Tabelle Erweiterung
```sql
-- Neue Spalte hinzugefügt:
email_verified BOOLEAN DEFAULT FALSE
```

## ⚙️ Konfiguration

### Umgebungsvariablen
Erstelle eine `.env` Datei basierend auf `.env.example`:

```bash
# Email Configuration
RESEND_API_KEY=your_resend_api_key_here
FROM_EMAIL=<EMAIL>
BASE_URL=http://localhost:3000

# Server Configuration
PORT=3000
HTTPS_PORT=3443

# Session Configuration
SESSION_SECRET=your-secret-key-change-in-production
```

### Resend Setup
1. **Account erstellen**: Registriere dich bei https://resend.com
2. **Domain verifizieren**: Füge codewave.online zu deinem Account hinzu
3. **API Key generieren**: Erstelle einen API Key in den Einstellungen
4. **DNS konfigurieren**: Setze die erforderlichen DNS-Records für die Domain

### Absender-Adressen konfigurieren
In Resend müssen folgende Adressen konfiguriert werden:
- `<EMAIL>` (Standard für System-Emails)
- `<EMAIL>` (für Support-Emails)
- `<EMAIL>` (für Admin-Emails)
- `<EMAIL>` (für Informations-Emails)

## 🎯 Verwendung

### Für Benutzer

#### Registrierung
1. Registrierungsformular ausfüllen
2. Bestätigungs-Email erhalten und Link klicken
3. Anmeldung ist erst nach Bestätigung möglich

#### Passwort vergessen
1. "Passwort vergessen?" Link auf Login-Seite klicken
2. Email-Adresse eingeben
3. Reset-Email erhalten und Link klicken
4. Neues Passwort eingeben

### Für Administratoren

#### Email versenden
1. Admin Panel öffnen
2. "Email Management" auswählen
3. Absender-Adresse wählen (nur @codewave.online)
4. Empfänger, Betreff und Nachricht eingeben
5. Email senden

## 🔒 Sicherheitsfeatures

### Token-Sicherheit
- **Kryptographisch sichere Tokens**: Verwendung von crypto.randomBytes()
- **Zeitbasierte Gültigkeit**: Automatisches Ablaufen der Tokens
- **Einmalige Verwendung**: Reset-Tokens werden nach Verwendung markiert
- **Sichere Speicherung**: Tokens werden gehashed in der Datenbank gespeichert

### Email-Validierung
- **Domain-Beschränkung**: Admin-Emails nur von @codewave.online
- **Input-Validierung**: Alle Email-Adressen werden validiert
- **Rate-Limiting**: Schutz vor Spam (kann erweitert werden)

### Audit-Logging
- **Admin-Aktionen**: Alle Email-Versendungen werden geloggt
- **Benutzer-Aktionen**: Registrierungen und Passwort-Resets werden verfolgt

## 🚨 Troubleshooting

### Häufige Probleme

#### Emails kommen nicht an
1. **API Key prüfen**: Ist RESEND_API_KEY korrekt gesetzt?
2. **Domain-Verifikation**: Ist codewave.online in Resend verifiziert?
3. **DNS-Records**: Sind alle erforderlichen DNS-Records gesetzt?
4. **Spam-Ordner**: Emails könnten im Spam-Ordner landen

#### Tokens funktionieren nicht
1. **Zeitzone**: Server-Zeit und Datenbank-Zeit synchron?
2. **Token-Format**: Werden Tokens korrekt generiert und gespeichert?
3. **URL-Encoding**: Werden Tokens in URLs korrekt übertragen?

#### Admin-Email-Versand schlägt fehl
1. **Absender-Adresse**: Ist die @codewave.online Adresse konfiguriert?
2. **Berechtigungen**: Hat der Admin die erforderlichen Rechte?
3. **Input-Validierung**: Sind alle Felder korrekt ausgefüllt?

### Logs überprüfen
```bash
# Server-Logs anzeigen
tail -f server.log

# Datenbank-Logs prüfen
sqlite3 database/learning_platform.db "SELECT * FROM email_verification_tokens ORDER BY created_at DESC LIMIT 10;"
```

## 📈 Erweiterungsmöglichkeiten

### Zukünftige Features
- **Email-Templates**: Erweiterte Template-Verwaltung
- **Bulk-Email**: Versand an mehrere Empfänger gleichzeitig
- **Email-Statistiken**: Tracking von Öffnungsraten und Klicks
- **Automatische Emails**: Trigger-basierte Email-Versendung
- **Email-Vorlagen**: Vordefinierte Templates für verschiedene Anlässe

### Performance-Optimierungen
- **Queue-System**: Asynchroner Email-Versand
- **Rate-Limiting**: Schutz vor Missbrauch
- **Caching**: Template-Caching für bessere Performance
- **Monitoring**: Überwachung der Email-Delivery-Raten

## 📞 Support

Bei Problemen oder Fragen zu den Email-Features:
1. Überprüfe diese Dokumentation
2. Schaue in die Server-Logs
3. Teste die Konfiguration mit den bereitgestellten Tools
4. Kontaktiere den Support bei anhaltenden Problemen
