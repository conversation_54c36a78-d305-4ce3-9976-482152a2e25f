const Database = require('./database/db');

console.log('🔍 Analyzing Current Achievement System Issues...\n');

const db = new Database();

async function analyzeAchievementSystem() {
    console.log('📊 Current Achievement System Analysis:');
    
    // Test 1: Check achievement table structure and data
    console.log('\n1. Achievement Table Analysis:');
    db.getAllAchievements((err, achievements) => {
        if (err) {
            console.error('❌ Error fetching achievements:', err);
            return;
        }
        
        console.log(`   Total achievements: ${achievements.length}`);
        console.log('   Achievement structure sample:');
        if (achievements.length > 0) {
            const sample = achievements[0];
            console.log('   ', JSON.stringify(sample, null, 4));
        }
        
        // Check for inconsistent schema
        const hasRequirementType = achievements.some(a => a.requirement_type !== undefined);
        const hasPointsRequired = achievements.some(a => a.points_required !== undefined);
        const hasLanguage = achievements.some(a => a.language !== undefined);
        
        console.log(`   Has requirement_type field: ${hasRequirementType}`);
        console.log(`   Has points_required field: ${hasPointsRequired}`);
        console.log(`   Has language field: ${hasLanguage}`);
        
        if (!hasRequirementType) {
            console.log('   ⚠️ ISSUE: Missing requirement_type field - achievements cannot be properly validated');
        }
        
        // Test 2: Check user achievements for a test user
        console.log('\n2. User Achievement Testing:');
        
        // Get first user for testing
        db.db.get('SELECT id, username FROM users LIMIT 1', (err, user) => {
            if (err || !user) {
                console.log('   ⚠️ No users found for testing');
                return;
            }
            
            console.log(`   Testing with user: ${user.username} (ID: ${user.id})`);
            
            // Get user's current stats
            db.getUserTotalStats(user.id, (err, stats) => {
                if (err) {
                    console.error('   ❌ Error getting user stats:', err);
                    return;
                }
                
                console.log('   User stats:', JSON.stringify(stats, null, 4));
                
                // Get user's current achievements
                db.getUserAchievements(user.id, (err, userAchievements) => {
                    if (err) {
                        console.error('   ❌ Error getting user achievements:', err);
                        return;
                    }
                    
                    console.log(`   User has ${userAchievements.length} achievements earned`);
                    
                    // Test achievement checking
                    console.log('\n3. Achievement Checking Logic Test:');
                    db.checkAndAwardAchievements(user.id, (err, newAchievements) => {
                        if (err) {
                            console.error('   ❌ Error in checkAndAwardAchievements:', err);
                            return;
                        }
                        
                        console.log(`   New achievements found: ${newAchievements ? newAchievements.length : 0}`);
                        if (newAchievements && newAchievements.length > 0) {
                            console.log('   New achievements:', newAchievements.map(a => a.name));
                        }
                        
                        // Test 4: Check for specific achievement logic issues
                        console.log('\n4. Achievement Logic Issues Analysis:');
                        
                        achievements.forEach((achievement, index) => {
                            if (index < 5) { // Check first 5 achievements
                                console.log(`   Achievement: ${achievement.name}`);
                                console.log(`     Type: ${achievement.requirement_type || 'MISSING'}`);
                                console.log(`     Value: ${achievement.requirement_value || achievement.points_required || 'MISSING'}`);
                                console.log(`     Language: ${achievement.language || 'general'}`);
                                
                                // Check if this achievement should be awarded based on current stats
                                let shouldBeAwarded = false;
                                if (achievement.requirement_type === 'levels_completed' && stats) {
                                    shouldBeAwarded = stats.total_completed_levels >= achievement.requirement_value;
                                } else if (achievement.requirement_type === 'total_score' && stats) {
                                    shouldBeAwarded = stats.total_score >= achievement.requirement_value;
                                } else if (achievement.requirement_type === 'course_completed' && stats) {
                                    shouldBeAwarded = stats.completed_courses >= achievement.requirement_value;
                                }
                                
                                const isEarned = userAchievements.some(ua => ua.id === achievement.id);
                                
                                if (shouldBeAwarded && !isEarned) {
                                    console.log(`     ❌ ISSUE: Should be awarded but not earned!`);
                                } else if (!shouldBeAwarded && isEarned) {
                                    console.log(`     ⚠️ WARNING: Earned but shouldn't be based on current stats`);
                                } else if (shouldBeAwarded && isEarned) {
                                    console.log(`     ✅ Correctly awarded`);
                                } else {
                                    console.log(`     ⏳ Not yet eligible`);
                                }
                            }
                        });
                        
                        // Test 5: Database integrity check
                        console.log('\n5. Database Integrity Check:');
                        
                        // Check for orphaned user_achievements
                        db.db.all(`
                            SELECT ua.* FROM user_achievements ua 
                            LEFT JOIN achievements a ON ua.achievement_id = a.id 
                            WHERE a.id IS NULL
                        `, (err, orphaned) => {
                            if (err) {
                                console.error('   ❌ Error checking orphaned achievements:', err);
                                return;
                            }
                            
                            if (orphaned.length > 0) {
                                console.log(`   ❌ ISSUE: ${orphaned.length} orphaned user_achievements found`);
                            } else {
                                console.log('   ✅ No orphaned user_achievements');
                            }
                            
                            // Check for duplicate user_achievements
                            db.db.all(`
                                SELECT user_id, achievement_id, COUNT(*) as count 
                                FROM user_achievements 
                                GROUP BY user_id, achievement_id 
                                HAVING COUNT(*) > 1
                            `, (err, duplicates) => {
                                if (err) {
                                    console.error('   ❌ Error checking duplicates:', err);
                                    return;
                                }
                                
                                if (duplicates.length > 0) {
                                    console.log(`   ❌ ISSUE: ${duplicates.length} duplicate user_achievements found`);
                                } else {
                                    console.log('   ✅ No duplicate user_achievements');
                                }
                                
                                console.log('\n📋 SUMMARY OF ISSUES FOUND:');
                                console.log('   1. Schema inconsistencies between different achievement creation scripts');
                                console.log('   2. Mixed field names (points vs points_required, requirement_value vs points_required)');
                                console.log('   3. Achievement checking logic may not handle all requirement types correctly');
                                console.log('   4. Language-specific achievement mapping may be broken');
                                console.log('   5. Course completion detection may be inaccurate');
                                
                                console.log('\n🔧 RECOMMENDED FIXES:');
                                console.log('   1. Standardize achievement table schema');
                                console.log('   2. Rebuild achievement checking logic from scratch');
                                console.log('   3. Implement proper course completion tracking');
                                console.log('   4. Add achievement debugging and logging');
                                console.log('   5. Create comprehensive achievement test suite');
                                
                                db.close();
                            });
                        });
                    });
                });
            });
        });
    });
}

// Run the analysis
analyzeAchievementSystem();
