/* Custom Styles for CodeLearning Platform */

:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, #0056b3 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
}

/* Welcome Section */
.welcome-section {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%) !important;
    border: none !important;
}

.bg-gradient {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
}

/* Feature Cards */
.feature-icon {
    transition: transform 0.3s ease;
}

.card:hover .feature-icon {
    transform: scale(1.1);
}

/* Navigation */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* Buttons */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Cards */
.card {
    border-radius: 12px;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Progress Bars */
.progress {
    height: 8px;
    border-radius: 4px;
}

.progress-bar {
    border-radius: 4px;
}

/* Code Editor Styles */
.code-editor {
    background-color: #2d3748;
    color: #e2e8f0;
    border-radius: 8px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
}

.code-editor textarea {
    background: transparent;
    border: none;
    color: inherit;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    resize: vertical;
    min-height: 200px;
}

.code-editor textarea:focus {
    outline: none;
    box-shadow: none;
}

/* Code Output */
.code-output {
    background-color: #1a202c;
    color: #e2e8f0;
    border-radius: 8px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
    min-height: 100px;
    padding: 1rem;
    white-space: pre-wrap;
}

/* Level Navigation */
.level-nav {
    background-color: var(--light-color);
    border-radius: 8px;
    padding: 1rem;
}

.level-item {
    display: flex;
    align-items: center;
    padding: 0.5rem;
    border-radius: 6px;
    text-decoration: none;
    color: var(--dark-color);
    transition: all 0.3s ease;
}

.level-item:hover {
    background-color: rgba(0, 123, 255, 0.1);
    color: var(--primary-color);
}

.level-item.active {
    background-color: var(--primary-color);
    color: white;
}

.level-item.completed {
    background-color: var(--success-color);
    color: white;
}

.level-item.locked {
    opacity: 0.5;
    pointer-events: none;
}

/* Exercise Types */
.exercise-container {
    background-color: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.fill-blank {
    display: inline-block;
    min-width: 100px;
    padding: 0.25rem 0.5rem;
    border: 2px dashed var(--primary-color);
    border-radius: 4px;
    background-color: rgba(0, 123, 255, 0.1);
    margin: 0 0.25rem;
}

.fill-blank input {
    border: none;
    background: transparent;
    text-align: center;
    font-weight: bold;
    color: var(--primary-color);
}

.fill-blank input:focus {
    outline: none;
}

.fill-blank.correct {
    border-color: var(--success-color);
    background-color: rgba(40, 167, 69, 0.1);
}

.fill-blank.incorrect {
    border-color: var(--danger-color);
    background-color: rgba(220, 53, 69, 0.1);
}

/* Syntax Highlighting */
.syntax-highlight {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 1rem;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 14px;
    line-height: 1.5;
    overflow-x: auto;
}

.keyword { color: #d73a49; font-weight: bold; }
.string { color: #032f62; }
.comment { color: #6a737d; font-style: italic; }
.number { color: #005cc5; }
.function { color: #6f42c1; }
.variable { color: #e36209; }

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        text-align: center;
        padding: 2rem 0;
    }
    
    .hero-section .display-4 {
        font-size: 2.5rem;
    }
    
    .code-editor textarea {
        min-height: 150px;
    }
}

/* Loading Spinner */
.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Success/Error Messages */
.alert {
    border-radius: 8px;
    border: none;
}

.alert-success {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
    border-left: 4px solid var(--success-color);
}

.alert-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
    border-left: 4px solid var(--danger-color);
}

.alert-info {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
    border-left: 4px solid var(--info-color);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Level Cards */
.level-card {
    transition: all 0.3s ease;
    border-radius: 12px;
}

.level-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.level-card.completed {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
}

.level-card.unlocked {
    background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
}

.level-card.locked {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

/* Course Progress */
.course-progress {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Interactive Elements */
.interactive-element {
    transition: all 0.2s ease;
}

.interactive-element:hover {
    transform: scale(1.02);
}

/* Code Editor Enhancements */
.code-editor {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.code-editor::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 30px;
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    border-bottom: 1px solid #4a5568;
}

.code-editor textarea {
    padding-top: 40px;
}

/* Notification Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.notification {
    animation: slideInRight 0.3s ease-out;
}

/* Loading States */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

/* Responsive Improvements */
@media (max-width: 768px) {
    .hero-section .display-4 {
        font-size: 2rem;
    }

    .course-progress {
        padding: 1rem;
    }

    .level-card {
        margin-bottom: 1rem;
    }

    .code-editor textarea {
        font-size: 12px;
        min-height: 120px;
    }
}

/* Profile Page Styles */
.profile-avatar .avatar-circle {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    font-size: 2rem;
}

.achievement {
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    transition: all 0.3s ease;
}

.achievement.earned {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    transform: scale(1.05);
}

.achievement.locked {
    background-color: #f8f9fa;
    color: #6c757d;
    opacity: 0.6;
}

.achievement i {
    display: block;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .card {
        background-color: #2d3748;
        color: #e2e8f0;
        border-color: #4a5568;
    }

    .bg-light {
        background-color: #1a202c !important;
    }

    .text-muted {
        color: #a0aec0 !important;
    }
}
