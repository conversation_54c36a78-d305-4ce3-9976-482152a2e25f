// Level-specific JavaScript
let courseId, levelId, courseSlug, levelNumber;

// Initialize level with data from server
function initializeLevel(courseIdParam, levelIdParam, courseSlugParam, levelNumberParam) {
    // Set global variables
    courseId = courseIdParam;
    levelId = levelIdParam;
    courseSlug = courseSlugParam;
    levelNumber = levelNumberParam;

    // Initialize auto-save
    initializeAutoSave();

    // Initialize tab completion
    initializeTabCompletion();

    // Initialize keyboard shortcuts
    initializeKeyboardShortcuts();
}

function initializeAutoSave() {
    const codeInput = document.getElementById('codeInput');
    const storageKey = `level_${levelId}_code`;

    // Load saved code
    const savedCode = localStorage.getItem(storageKey);
    if (savedCode && !codeInput.value.trim()) {
        codeInput.value = savedCode;
    }

    // Save code on input
    codeInput.addEventListener('input', function() {
        localStorage.setItem(storageKey, this.value);
    });
}

function initializeTabCompletion() {
    const codeInput = document.getElementById('codeInput');

    // Hide completion when clicking outside
    document.addEventListener('click', function(e) {
        if (tabCompletionDiv && !tabCompletionDiv.contains(e.target) && e.target !== codeInput) {
            hideTabCompletion();
        }
    });
}

function initializeKeyboardShortcuts() {
    const codeInput = document.getElementById('codeInput');

    // Keyboard shortcuts and tab completion
    codeInput.addEventListener('keydown', function(e) {
        // Tab completion
        if (e.key === 'Tab') {
            e.preventDefault();
            handleTabCompletion();
            return;
        }

        // Navigate tab completion
        if (tabCompletionDiv && tabCompletionDiv.style.display !== 'none') {
            if (e.key === 'ArrowDown') {
                e.preventDefault();
                selectedIndex = Math.min(selectedIndex + 1, completions.length - 1);
                updateTabCompletionSelection();
                return;
            }
            if (e.key === 'ArrowUp') {
                e.preventDefault();
                selectedIndex = Math.max(selectedIndex - 1, 0);
                updateTabCompletionSelection();
                return;
            }
            if (e.key === 'Enter') {
                e.preventDefault();
                if (selectedIndex >= 0) {
                    insertCompletion(completions[selectedIndex]);
                }
                hideTabCompletion();
                return;
            }
            if (e.key === 'Escape') {
                hideTabCompletion();
                return;
            }
        }

        // Hide completion on other keys
        if (tabCompletionDiv) {
            hideTabCompletion();
        }

        // Ctrl/Cmd + Enter to run code
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            runCode();
        }
        // Ctrl/Cmd + S to submit code
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            submitCode();
        }
    });
}

// Tab completion variables
let tabCompletionDiv = null;
let selectedIndex = -1;
let completions = [];

// Language-specific completions
const languageCompletions = {
    'javascript-advanced': [
        'console.log()', 'function ', 'const ', 'let ', 'var ', 'if (', 'for (', 'while (', 
        'return ', 'async ', 'await ', 'Promise', 'setTimeout(', 'setInterval(',
        'document.', 'window.', 'Array.', 'Object.', 'JSON.', 'Math.'
    ],
    'python': [
        'print()', 'def ', 'class ', 'if ', 'elif ', 'else:', 'for ', 'while ', 
        'import ', 'from ', 'return ', 'try:', 'except:', 'with ', 'lambda ',
        'len()', 'range()', 'str()', 'int()', 'float()', 'list()', 'dict()'
    ],
    'php': [
        'echo ', '<?php', '?>', 'function ', 'class ', 'if (', 'else', 'elseif (', 
        'for (', 'foreach (', 'while (', 'return ', 'include ', 'require ',
        '$_GET', '$_POST', '$_SESSION', 'mysqli_', 'PDO'
    ],
    'go': [
        'fmt.Println()', 'func ', 'var ', 'const ', 'if ', 'for ', 'range ', 
        'return ', 'package ', 'import ', 'type ', 'struct', 'interface',
        'go ', 'defer ', 'make()', 'append()', 'len()', 'cap()'
    ],
    'java': [
        'System.out.println()', 'public ', 'private ', 'protected ', 'class ', 
        'interface ', 'if (', 'else', 'for (', 'while (', 'return ', 'new ',
        'String', 'int', 'boolean', 'double', 'float', 'char', 'void'
    ],
    'html-css-js': [
        '<html>', '<head>', '<body>', '<div>', '<span>', '<p>', '<h1>', '<h2>', 
        '<script>', '<style>', 'class="', 'id="', 'src="', 'href="',
        'console.log()', 'function()', 'document.', 'window.'
    ]
};

function initializeLevel(cId, lId, cSlug, lNumber) {
    courseId = cId;
    levelId = lId;
    courseSlug = cSlug;
    levelNumber = lNumber;
    
    setupEventListeners();
    setupAutoSave();
}

function setupEventListeners() {
    const codeInput = document.getElementById('codeInput');
    if (!codeInput) return;
    
    // Keyboard shortcuts and tab completion
    codeInput.addEventListener('keydown', function(e) {
        // Tab handling - prioritize indentation over completion
        if (e.key === 'Tab') {
            e.preventDefault();

            // Check if we should show tab completion (only if there's a partial word)
            const cursorPos = codeInput.selectionStart;
            const textBeforeCursor = codeInput.value.substring(0, cursorPos);
            const currentLine = textBeforeCursor.split('\n').pop();
            const lastWord = currentLine.split(/\s+/).pop();

            // Show tab completion only if:
            // 1. There's a partial word (at least 2 characters)
            // 2. The word contains only letters/numbers (no special chars)
            // 3. We're not at the beginning of a line
            const shouldShowCompletion = lastWord.length >= 2 &&
                                       /^[a-zA-Z][a-zA-Z0-9]*$/.test(lastWord) &&
                                       currentLine.trim() !== lastWord;

            if (shouldShowCompletion) {
                handleTabCompletion();
                return;
            }

            // Otherwise, handle indentation
            handleTabIndentation(e.shiftKey);
            return;
        }
        
        // Navigate tab completion
        if (tabCompletionDiv && tabCompletionDiv.style.display !== 'none') {
            if (e.key === 'ArrowDown') {
                e.preventDefault();
                selectedIndex = Math.min(selectedIndex + 1, completions.length - 1);
                updateTabCompletionSelection();
                return;
            }
            if (e.key === 'ArrowUp') {
                e.preventDefault();
                selectedIndex = Math.max(selectedIndex - 1, 0);
                updateTabCompletionSelection();
                return;
            }
            if (e.key === 'Enter') {
                e.preventDefault();
                if (selectedIndex >= 0) {
                    insertCompletion(completions[selectedIndex]);
                }
                hideTabCompletion();
                return;
            }
            if (e.key === 'Escape') {
                hideTabCompletion();
                return;
            }
        }
        
        // Hide completion on other keys
        if (tabCompletionDiv) {
            hideTabCompletion();
        }
        
        // Ctrl/Cmd + Enter to run code
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            e.preventDefault();
            runCode();
        }
        // Ctrl/Cmd + S to submit code
        if ((e.ctrlKey || e.metaKey) && e.key === 's') {
            e.preventDefault();
            submitCode();
        }
    });
    
    // Hide completion when clicking outside
    document.addEventListener('click', function(e) {
        if (tabCompletionDiv && !tabCompletionDiv.contains(e.target) && e.target !== codeInput) {
            hideTabCompletion();
        }
    });
}

function setupAutoSave() {
    const codeInput = document.getElementById('codeInput');
    if (!codeInput) return;
    
    const storageKey = `level_${levelId}_code`;
    
    // Load saved code
    const savedCode = localStorage.getItem(storageKey);
    if (savedCode && !codeInput.value.trim()) {
        codeInput.value = savedCode;
    }
    
    // Save code on input
    codeInput.addEventListener('input', function() {
        localStorage.setItem(storageKey, this.value);
    });
}

function runCode() {
    const code = document.getElementById('codeInput').value;
    const codeOutput = document.getElementById('codeOutput');
    const htmlPreview = document.getElementById('htmlPreview');

    if (!code.trim()) {
        codeOutput.textContent = 'Schreibe hier deinen Code...';
        return;
    }

    // Hide preview by default
    htmlPreview.classList.add('hidden');
    codeOutput.classList.remove('hidden');

    // Determine code execution based on course
    switch(courseSlug) {
        case 'html-css-js':
            // HTML/CSS/JS execution in iframe
            htmlPreview.classList.remove('hidden');
            codeOutput.classList.add('hidden');
            htmlPreview.srcdoc = code;
            break;
            
        case 'javascript-advanced':
            // JavaScript execution
            try {
                let result = '';
                const originalLog = console.log;
                console.log = function(...args) {
                    result += args.join(' ') + '\n';
                };

                eval(code);
                console.log = originalLog;

                codeOutput.textContent = result || 'Code ausgeführt (keine Ausgabe)';
                codeOutput.style.color = '#10b981';
            } catch (error) {
                codeOutput.textContent = `Fehler: ${error.message}`;
                codeOutput.style.color = '#ef4444';
            }
            break;
            
        case 'python':
            // Python simulation
            codeOutput.innerHTML = simulatePythonExecution(code);
            codeOutput.style.color = '#10b981';
            break;
            
        case 'php':
            // PHP simulation
            codeOutput.innerHTML = simulatePHPExecution(code);
            codeOutput.style.color = '#10b981';
            break;
            
        case 'go':
            // Go simulation
            codeOutput.innerHTML = simulateGoExecution(code);
            codeOutput.style.color = '#10b981';
            break;
            
        case 'java':
            // Java simulation
            codeOutput.innerHTML = simulateJavaExecution(code);
            codeOutput.style.color = '#10b981';
            break;
            
        default:
            codeOutput.textContent = 'Nicht unterstützte Programmiersprache';
            codeOutput.style.color = '#ef4444';
    }
}

function submitCode() {
    const code = document.getElementById('codeInput').value;
    
    if (!code.trim()) {
        showFeedback('Schreibe hier deinen Code', 'warning');
        return;
    }

    const submitBtn = event.target;
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Wird eingereicht...';
    submitBtn.disabled = true;

    fetch(`/course/${courseSlug}/level/${levelNumber}/submit`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code: code })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.passed) {
                showFeedback(`🎉 Richtig! ${data.message}`, 'success');
                showNotification('Level abgeschlossen!', 'success');

                // Enable next level button if available
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                showFeedback(`❌ ${data.message}`, 'danger');
                showNotification('Versuche es nochmal!', 'warning');
            }
        } else {
            showFeedback(data.message || 'Fehler beim Einreichen', 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showFeedback('Fehler beim Einreichen des Codes', 'danger');
    })
    .finally(() => {
        // Restore button state
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

function clearCode() {
    if (confirm('Möchtest du wirklich den gesamten Code löschen?')) {
        document.getElementById('codeInput').value = '';
        document.getElementById('codeOutput').textContent = 'Führe deinen Code aus, um die Ausgabe zu sehen...';
        hideFeedback();
    }
}

// Language simulation functions
function simulatePythonExecution(code) {
    try {
        let output = '';
        const lines = code.split('\n');

        for (let line of lines) {
            line = line.trim();
            if (line.startsWith('print(')) {
                // Enhanced print() parsing
                const match = line.match(/print\((.*)\)/);
                if (match) {
                    let content = match[1];
                    // Handle different quote types and expressions
                    if (content.match(/^["'].*["']$/)) {
                        // String literal
                        content = content.replace(/^["']|["']$/g, '');
                    } else if (content.includes('+')) {
                        // String concatenation
                        content = content.replace(/["']/g, '').replace(/\s*\+\s*/g, '');
                    }
                    // Handle escape sequences
                    content = content.replace(/\\n/g, '\n').replace(/\\t/g, '\t');
                    output += content + '\n';
                }
            } else if (line.includes('=') && !line.startsWith('#')) {
                // Variable assignment (just acknowledge it)
                continue;
            }
        }

        return output || 'Code ausgeführt (keine Ausgabe)';
    } catch (error) {
        return `Fehler: ${error.message}`;
    }
}

function simulatePHPExecution(code) {
    try {
        let output = '';
        const lines = code.split('\n');
        let inPhpBlock = false;

        for (let line of lines) {
            line = line.trim();

            // Handle PHP opening/closing tags
            if (line.includes('<?php')) {
                inPhpBlock = true;
                continue;
            }
            if (line.includes('?>')) {
                inPhpBlock = false;
                continue;
            }

            // Only process PHP code inside PHP blocks or if no tags are used
            if (inPhpBlock || !code.includes('<?php')) {
                if (line.includes('echo ')) {
                    // Enhanced echo parsing
                    const echoMatch = line.match(/echo\s+["']([^"']*)["'];?/) ||
                                     line.match(/echo\s+([^;]+);?/);
                    if (echoMatch) {
                        let content = echoMatch[1];
                        // Remove quotes if present
                        content = content.replace(/^["']|["']$/g, '');
                        // Handle escape sequences
                        content = content.replace(/\\n/g, '\n').replace(/\\t/g, '\t');
                        output += content;
                    }
                }
            }
        }

        return output || 'Code ausgeführt (keine Ausgabe)';
    } catch (error) {
        return `Fehler: ${error.message}`;
    }
}

function simulateGoExecution(code) {
    try {
        let output = '';
        const lines = code.split('\n');
        let hasPackage = false;
        let hasFmtImport = false;

        // Check for required Go structure
        for (let line of lines) {
            if (line.trim().startsWith('package ')) hasPackage = true;
            if (line.trim().includes('import') && line.includes('fmt')) hasFmtImport = true;
        }

        // Add implicit imports if missing (for simulation)
        if (!hasFmtImport && code.includes('fmt.')) {
            // Simulate that fmt is available
            hasFmtImport = true;
        }

        for (let line of lines) {
            line = line.trim();
            if (line.includes('fmt.Println(')) {
                const match = line.match(/fmt\.Println\(["']([^"']*)["']\)/) ||
                             line.match(/fmt\.Println\(([^)]+)\)/);
                if (match) {
                    let content = match[1];
                    // Remove quotes if present
                    content = content.replace(/^["']|["']$/g, '');
                    output += content + '\n';
                }
            } else if (line.includes('fmt.Print(')) {
                const match = line.match(/fmt\.Print\(["']([^"']*)["']\)/) ||
                             line.match(/fmt\.Print\(([^)]+)\)/);
                if (match) {
                    let content = match[1];
                    content = content.replace(/^["']|["']$/g, '');
                    output += content;
                }
            }
        }

        return output || 'Code ausgeführt (keine Ausgabe)';
    } catch (error) {
        return `Fehler: ${error.message}`;
    }
}

function simulateJavaExecution(code) {
    try {
        let output = '';
        const lines = code.split('\n');
        let inMainMethod = false;
        let braceCount = 0;

        for (let line of lines) {
            line = line.trim();

            // Track if we're inside the main method
            if (line.includes('public static void main')) {
                inMainMethod = true;
            }

            // Count braces to track scope
            braceCount += (line.match(/\{/g) || []).length;
            braceCount -= (line.match(/\}/g) || []).length;

            // Process output statements (allow execution outside main for simulation)
            if (line.includes('System.out.println(')) {
                const match = line.match(/System\.out\.println\(["']([^"']*)["']\)/) ||
                             line.match(/System\.out\.println\(([^)]+)\)/);
                if (match) {
                    let content = match[1];
                    // Remove quotes if present
                    content = content.replace(/^["']|["']$/g, '');
                    output += content + '\n';
                }
            } else if (line.includes('System.out.print(')) {
                const match = line.match(/System\.out\.print\(["']([^"']*)["']\)/) ||
                             line.match(/System\.out\.print\(([^)]+)\)/);
                if (match) {
                    let content = match[1];
                    content = content.replace(/^["']|["']$/g, '');
                    output += content;
                }
            }
        }

        return output || 'Code ausgeführt (keine Ausgabe)';
    } catch (error) {
        return `Fehler: ${error.message}`;
    }
}

// Tab indentation function
function handleTabIndentation(isShiftPressed) {
    const codeInput = document.getElementById('codeInput');
    const start = codeInput.selectionStart;
    const end = codeInput.selectionEnd;
    const value = codeInput.value;
    const tabSize = 4; // 4 spaces for indentation
    const tabString = ' '.repeat(tabSize);

    if (start === end) {
        // No selection - handle single cursor
        if (isShiftPressed) {
            // Shift+Tab: Remove indentation
            const lineStart = value.lastIndexOf('\n', start - 1) + 1;
            const lineEnd = value.indexOf('\n', start);
            const currentLine = value.substring(lineStart, lineEnd === -1 ? value.length : lineEnd);

            // Check if line starts with spaces that can be removed
            const leadingSpaces = currentLine.match(/^( +)/);
            if (leadingSpaces) {
                const spacesToRemove = Math.min(leadingSpaces[1].length, tabSize);
                const newValue = value.substring(0, lineStart) +
                               currentLine.substring(spacesToRemove) +
                               value.substring(lineEnd === -1 ? value.length : lineEnd);

                codeInput.value = newValue;
                codeInput.selectionStart = codeInput.selectionEnd = start - spacesToRemove;
            }
        } else {
            // Tab: Add indentation
            const newValue = value.substring(0, start) + tabString + value.substring(end);
            codeInput.value = newValue;
            codeInput.selectionStart = codeInput.selectionEnd = start + tabSize;
        }
    } else {
        // Text is selected - indent/unindent multiple lines
        const selectedText = value.substring(start, end);
        const lines = selectedText.split('\n');

        if (isShiftPressed) {
            // Shift+Tab: Remove indentation from each line
            const unindentedLines = lines.map(line => {
                const leadingSpaces = line.match(/^( +)/);
                if (leadingSpaces) {
                    const spacesToRemove = Math.min(leadingSpaces[1].length, tabSize);
                    return line.substring(spacesToRemove);
                }
                return line;
            });

            const newSelectedText = unindentedLines.join('\n');
            const newValue = value.substring(0, start) + newSelectedText + value.substring(end);

            codeInput.value = newValue;
            codeInput.selectionStart = start;
            codeInput.selectionEnd = start + newSelectedText.length;
        } else {
            // Tab: Add indentation to each line
            const indentedLines = lines.map(line => tabString + line);
            const newSelectedText = indentedLines.join('\n');
            const newValue = value.substring(0, start) + newSelectedText + value.substring(end);

            codeInput.value = newValue;
            codeInput.selectionStart = start;
            codeInput.selectionEnd = start + newSelectedText.length;
        }
    }

    // Trigger input event to update any listeners (like auto-save)
    codeInput.dispatchEvent(new Event('input', { bubbles: true }));
}

// Tab completion functions
function handleTabCompletion() {
    const codeInput = document.getElementById('codeInput');
    const cursorPos = codeInput.selectionStart;
    const textBeforeCursor = codeInput.value.substring(0, cursorPos);
    const words = textBeforeCursor.split(/\s+/);
    const currentWord = words[words.length - 1];

    const availableCompletions = languageCompletions[courseSlug] || [];
    completions = availableCompletions.filter(completion =>
        completion.toLowerCase().startsWith(currentWord.toLowerCase())
    );

    if (completions.length > 0) {
        showTabCompletion();
    }
}

function showTabCompletion() {
    hideTabCompletion();

    tabCompletionDiv = document.createElement('div');
    tabCompletionDiv.className = 'tab-completion';

    completions.forEach((completion, index) => {
        const item = document.createElement('div');
        item.className = 'tab-completion-item';
        item.textContent = completion;
        item.addEventListener('click', () => {
            insertCompletion(completion);
            hideTabCompletion();
        });
        tabCompletionDiv.appendChild(item);
    });

    // Position the completion div
    const codeInput = document.getElementById('codeInput');
    const rect = codeInput.getBoundingClientRect();
    tabCompletionDiv.style.position = 'absolute';
    tabCompletionDiv.style.left = rect.left + 'px';
    tabCompletionDiv.style.top = (rect.bottom + 5) + 'px';
    tabCompletionDiv.style.minWidth = '200px';

    document.body.appendChild(tabCompletionDiv);
    selectedIndex = 0;
    updateTabCompletionSelection();
}

function updateTabCompletionSelection() {
    const items = tabCompletionDiv.querySelectorAll('.tab-completion-item');
    items.forEach((item, index) => {
        item.classList.toggle('selected', index === selectedIndex);
    });
}

function insertCompletion(completion) {
    const codeInput = document.getElementById('codeInput');
    const cursorPos = codeInput.selectionStart;
    const textBeforeCursor = codeInput.value.substring(0, cursorPos);
    const textAfterCursor = codeInput.value.substring(cursorPos);
    const words = textBeforeCursor.split(/\s+/);
    const currentWord = words[words.length - 1];

    const newTextBefore = textBeforeCursor.substring(0, textBeforeCursor.length - currentWord.length) + completion;
    codeInput.value = newTextBefore + textAfterCursor;
    codeInput.selectionStart = codeInput.selectionEnd = newTextBefore.length;
    codeInput.focus();
}

function hideTabCompletion() {
    if (tabCompletionDiv) {
        tabCompletionDiv.remove();
        tabCompletionDiv = null;
    }
    selectedIndex = -1;
}

// Feedback and notification functions
function showFeedback(message, type) {
    const feedback = document.getElementById('feedback');
    const feedbackContent = document.getElementById('feedbackContent');

    if (!feedback || !feedbackContent) return;

    feedback.classList.remove('hidden');
    feedbackContent.className = `p-4 rounded-lg border ${
        type === 'success' ? 'bg-green-900 border-green-700 text-green-300' :
        type === 'warning' ? 'bg-yellow-900 border-yellow-700 text-yellow-300' :
        'bg-red-900 border-red-700 text-red-300'
    }`;
    feedbackContent.innerHTML = message;
}

function hideFeedback() {
    const feedback = document.getElementById('feedback');
    if (feedback) {
        feedback.classList.add('hidden');
    }
}

function showNotification(message, type) {
    // Simple notification system
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-600 text-white' :
        type === 'warning' ? 'bg-yellow-600 text-white' :
        'bg-red-600 text-white'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 3000);
}
