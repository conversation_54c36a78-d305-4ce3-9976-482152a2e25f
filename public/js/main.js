// Main JavaScript for CodeLearning Platform

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Auto-resize textareas
    const textareas = document.querySelectorAll('textarea');
    textareas.forEach(textarea => {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });
    });

    // Code editor functionality
    const codeEditors = document.querySelectorAll('.code-editor textarea');
    codeEditors.forEach(editor => {
        // Add line numbers (simplified)
        editor.addEventListener('input', function() {
            updateLineNumbers(this);
        });
        
        // Tab key support (only for non-level editors)
        if (!editor.id || editor.id !== 'codeInput') {
            editor.addEventListener('keydown', function(e) {
                if (e.key === 'Tab') {
                    e.preventDefault();
                    const start = this.selectionStart;
                    const end = this.selectionEnd;

                    if (e.shiftKey) {
                        // Shift+Tab: Remove indentation
                        const value = this.value;
                        const lineStart = value.lastIndexOf('\n', start - 1) + 1;
                        const lineEnd = value.indexOf('\n', start);
                        const currentLine = value.substring(lineStart, lineEnd === -1 ? value.length : lineEnd);

                        const leadingSpaces = currentLine.match(/^( {1,4})/);
                        if (leadingSpaces) {
                            const spacesToRemove = leadingSpaces[1].length;
                            const newValue = value.substring(0, lineStart) +
                                           currentLine.substring(spacesToRemove) +
                                           value.substring(lineEnd === -1 ? value.length : lineEnd);

                            this.value = newValue;
                            this.selectionStart = this.selectionEnd = start - spacesToRemove;
                        }
                    } else {
                        // Tab: Add indentation
                        this.value = this.value.substring(0, start) + '    ' + this.value.substring(end);
                        this.selectionStart = this.selectionEnd = start + 4;
                    }
                }
            });
        }
    });

    // Fill-in-the-blank functionality
    const fillBlanks = document.querySelectorAll('.fill-blank input');
    fillBlanks.forEach(input => {
        input.addEventListener('input', function() {
            checkFillBlank(this);
        });
    });

    // Form validation
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
            }
        });
    });
});

// Update line numbers for code editor
function updateLineNumbers(textarea) {
    const lineNumbersContainer = textarea.parentElement.querySelector('.line-numbers');
    if (!lineNumbersContainer) return;
    
    const lines = textarea.value.split('\n');
    const lineNumbers = lines.map((_, index) => index + 1).join('\n');
    lineNumbersContainer.textContent = lineNumbers;
}

// Check fill-in-the-blank answers
function checkFillBlank(input) {
    const correctAnswer = input.dataset.answer;
    const userAnswer = input.value.trim().toLowerCase();
    const correctAnswerLower = correctAnswer.toLowerCase();
    
    const fillBlank = input.parentElement;
    fillBlank.classList.remove('correct', 'incorrect');
    
    if (userAnswer === correctAnswerLower) {
        fillBlank.classList.add('correct');
        input.style.color = '#28a745';
    } else if (userAnswer !== '') {
        fillBlank.classList.add('incorrect');
        input.style.color = '#dc3545';
    } else {
        input.style.color = '#007bff';
    }
}

// Form validation
function validateForm(form) {
    let isValid = true;
    const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            showFieldError(input, 'Dieses Feld ist erforderlich');
            isValid = false;
        } else {
            clearFieldError(input);
        }
        
        // Email validation
        if (input.type === 'email' && input.value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(input.value)) {
                showFieldError(input, 'Bitte geben Sie eine gültige E-Mail-Adresse ein');
                isValid = false;
            }
        }
        
        // Password confirmation
        if (input.name === 'confirmPassword') {
            const password = form.querySelector('input[name="password"]');
            if (password && input.value !== password.value) {
                showFieldError(input, 'Passwörter stimmen nicht überein');
                isValid = false;
            }
        }
    });
    
    return isValid;
}

// Show field error
function showFieldError(input, message) {
    clearFieldError(input);
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'invalid-feedback d-block';
    errorDiv.textContent = message;
    
    input.classList.add('is-invalid');
    input.parentElement.appendChild(errorDiv);
}

// Clear field error
function clearFieldError(input) {
    input.classList.remove('is-invalid');
    const errorDiv = input.parentElement.querySelector('.invalid-feedback');
    if (errorDiv) {
        errorDiv.remove();
    }
}

// Code execution functionality
async function runCode(code, language = 'javascript') {
    const outputElement = document.querySelector('.code-output');
    if (!outputElement) return;
    
    outputElement.textContent = 'Code wird ausgeführt...';
    
    try {
        // This is a simplified version - in production, you'd send this to a backend service
        if (language === 'javascript') {
            const result = executeJavaScript(code);
            outputElement.textContent = result;
        } else {
            outputElement.textContent = 'Sprache wird noch nicht unterstützt';
        }
    } catch (error) {
        outputElement.textContent = `Fehler: ${error.message}`;
        outputElement.style.color = '#dc3545';
    }
}

// Simple JavaScript execution (for demo purposes)
function executeJavaScript(code) {
    let output = '';
    const originalConsoleLog = console.log;
    
    // Capture console.log output
    console.log = function(...args) {
        output += args.join(' ') + '\n';
    };
    
    try {
        // Execute the code
        eval(code);
        
        if (output === '') {
            output = 'Code ausgeführt (keine Ausgabe)';
        }
    } catch (error) {
        output = `Fehler: ${error.message}`;
    } finally {
        // Restore original console.log
        console.log = originalConsoleLog;
    }
    
    return output.trim();
}

// Progress tracking
function updateProgress(courseId, levelId, completed = false, score = 0) {
    fetch('/api/progress', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            courseId: courseId,
            levelId: levelId,
            completed: completed,
            score: score
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Fortschritt gespeichert!', 'success');
        }
    })
    .catch(error => {
        console.error('Error updating progress:', error);
        showNotification('Fehler beim Speichern des Fortschritts', 'error');
    });
}

// Show notification with improved styling
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed notification`;
    notification.style.top = '20px';
    notification.style.right = '20px';
    notification.style.zIndex = '9999';
    notification.style.minWidth = '300px';
    notification.style.borderRadius = '8px';
    notification.style.boxShadow = '0 4px 15px rgba(0, 0, 0, 0.2)';

    const icon = type === 'success' ? 'fas fa-check-circle' :
                 type === 'error' ? 'fas fa-exclamation-triangle' :
                 'fas fa-info-circle';

    notification.innerHTML = `
        <i class="${icon} me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.classList.remove('show');
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 300);
        }
    }, 5000);
}

// Enhanced progress tracking with visual feedback
function updateProgressWithFeedback(courseId, levelId, completed = false, score = 0) {
    const button = event.target;
    const originalText = button.innerHTML;

    // Show loading state
    button.classList.add('loading');
    button.disabled = true;
    button.innerHTML = '<span class="spinner"></span> Speichere...';

    fetch('/api/progress', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            courseId: courseId,
            levelId: levelId,
            completed: completed,
            score: score
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Fortschritt gespeichert!', 'success');

            // Update UI elements
            updateProgressUI(completed, score);
        } else {
            showNotification('Fehler beim Speichern', 'error');
        }
    })
    .catch(error => {
        console.error('Error updating progress:', error);
        showNotification('Fehler beim Speichern des Fortschritts', 'error');
    })
    .finally(() => {
        // Restore button state
        button.classList.remove('loading');
        button.disabled = false;
        button.innerHTML = originalText;
    });
}

// Update progress UI elements
function updateProgressUI(completed, score) {
    // Update progress bars
    const progressBars = document.querySelectorAll('.progress-bar');
    progressBars.forEach(bar => {
        const current = parseInt(bar.getAttribute('aria-valuenow')) || 0;
        const newValue = completed ? current + 1 : current;
        bar.style.width = `${newValue}%`;
        bar.setAttribute('aria-valuenow', newValue);
    });

    // Update score displays
    const scoreElements = document.querySelectorAll('.score-display');
    scoreElements.forEach(element => {
        const currentScore = parseInt(element.textContent) || 0;
        element.textContent = currentScore + score;
    });

    // Add completion animation
    if (completed) {
        const levelCard = document.querySelector('.level-card.active');
        if (levelCard) {
            levelCard.classList.add('completed');
            levelCard.classList.remove('unlocked');

            // Confetti effect (simple)
            createConfetti();
        }
    }
}

// Simple confetti effect
function createConfetti() {
    const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'];

    for (let i = 0; i < 50; i++) {
        const confetti = document.createElement('div');
        confetti.style.position = 'fixed';
        confetti.style.width = '10px';
        confetti.style.height = '10px';
        confetti.style.backgroundColor = colors[Math.floor(Math.random() * colors.length)];
        confetti.style.left = Math.random() * window.innerWidth + 'px';
        confetti.style.top = '-10px';
        confetti.style.zIndex = '10000';
        confetti.style.borderRadius = '50%';
        confetti.style.pointerEvents = 'none';

        document.body.appendChild(confetti);

        // Animate confetti falling
        const animation = confetti.animate([
            { transform: 'translateY(0) rotate(0deg)', opacity: 1 },
            { transform: `translateY(${window.innerHeight + 20}px) rotate(360deg)`, opacity: 0 }
        ], {
            duration: Math.random() * 2000 + 1000,
            easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)'
        });

        animation.onfinish = () => confetti.remove();
    }
}

// Utility functions
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}
