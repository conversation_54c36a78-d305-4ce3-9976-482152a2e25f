# Production Deployment Guide

## Overview

This guide covers deploying CodeWave to production with Let's Encrypt certificates for `lxnd.cloud`.

## Prerequisites

### Server Requirements
- Ubuntu/Debian server with root access
- Node.js 16+ installed
- Domain `lxnd.cloud` pointing to your server
- Let's Encrypt certificates already set up

### Let's Encrypt Setup (if not done)
```bash
# Install certbot
sudo apt-get update
sudo apt-get install certbot

# Generate certificate for lxnd.cloud
sudo certbot certonly --standalone -d lxnd.cloud

# Verify certificates
sudo ls -la /etc/letsencrypt/live/lxnd.cloud/
```

## Quick Deployment

### Option 1: Automated Deployment Script
```bash
# Make script executable
chmod +x deploy-production.sh

# Run deployment script
sudo ./deploy-production.sh
```

### Option 2: Manual Deployment
```bash
# Install dependencies
npm install

# Start with Let's Encrypt certificates
sudo node index.js
# Choose 'Y' for HTTPS when prompted
```

## Certificate Integration

### Automatic Detection
The application automatically detects Let's Encrypt certificates:

**Certificate Paths:**
- Private Key: `/etc/letsencrypt/live/lxnd.cloud/privkey.pem`
- Full Chain: `/etc/letsencrypt/live/lxnd.cloud/fullchain.pem`

**Fallback Behavior:**
- If Let's Encrypt certificates are found → Uses port 443 (production)
- If not found → Falls back to local certificates on port 3443 (development)

### Server Output Examples

**With Let's Encrypt:**
```
🔒 Starting HTTPS server...
📋 Using Let's Encrypt certificates for lxnd.cloud

✅ HTTPS Server successfully started!
🌐 Server running on: https://lxnd.cloud:443
🔒 SSL/TLS encryption enabled with Let's Encrypt
📁 Certificates loaded from: /etc/letsencrypt/live/lxnd.cloud/
🌍 Production-ready with trusted certificates
📋 Certificate source: Let's Encrypt (lxnd.cloud)
```

**Fallback to Local:**
```
🔒 Starting HTTPS server...
📋 Let's Encrypt certificates not found, using local certificates

✅ HTTPS Server successfully started!
🌐 Server running on: https://localhost:3443
🔒 SSL/TLS encryption enabled
📁 Certificates loaded from: ./certs/
🔒 Browser will show security warning for self-signed certificate
📋 Certificate source: Local development certificates
```

## Systemd Service Setup

### Create Service File
```bash
sudo nano /etc/systemd/system/codewave.service
```

```ini
[Unit]
Description=CodeWave Learning Platform
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/path/to/codewave
ExecStart=/usr/bin/node index.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=HTTPS_PORT=443
StandardInput=null

[Install]
WantedBy=multi-user.target
```

### Enable and Start Service
```bash
# Reload systemd
sudo systemctl daemon-reload

# Enable service
sudo systemctl enable codewave

# Start service
sudo systemctl start codewave

# Check status
sudo systemctl status codewave
```

## Environment Variables

### Production Configuration
```bash
# Set production environment
export NODE_ENV=production

# Force HTTPS port (optional)
export HTTPS_PORT=443

# Start application
sudo node index.js
```

### Automatic HTTPS Selection
To skip the Y/N prompt and automatically use HTTPS:
```bash
echo "Y" | sudo node index.js
```

## Firewall Configuration

### UFW (Ubuntu Firewall)
```bash
# Allow HTTP and HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Enable firewall
sudo ufw enable
```

### iptables
```bash
# Allow HTTP
sudo iptables -A INPUT -p tcp --dport 80 -j ACCEPT

# Allow HTTPS
sudo iptables -A INPUT -p tcp --dport 443 -j ACCEPT

# Save rules
sudo iptables-save > /etc/iptables/rules.v4
```

## Certificate Management

### Automatic Renewal
Set up automatic certificate renewal:
```bash
# Test renewal
sudo certbot renew --dry-run

# Add to crontab for automatic renewal
sudo crontab -e

# Add this line:
0 12 * * * /usr/bin/certbot renew --quiet
```

### Manual Renewal
```bash
# Renew certificates
sudo certbot renew

# Restart application
sudo systemctl restart codewave
```

## Monitoring and Logs

### View Application Logs
```bash
# Real-time logs
sudo journalctl -u codewave -f

# Recent logs
sudo journalctl -u codewave --since "1 hour ago"

# All logs
sudo journalctl -u codewave
```

### Check Certificate Status
```bash
# Certificate details
sudo openssl x509 -in /etc/letsencrypt/live/lxnd.cloud/fullchain.pem -text -noout

# Expiration date
sudo openssl x509 -in /etc/letsencrypt/live/lxnd.cloud/fullchain.pem -noout -enddate
```

## Troubleshooting

### Certificate Permission Issues
```bash
# Check certificate permissions
sudo ls -la /etc/letsencrypt/live/lxnd.cloud/

# Fix permissions if needed
sudo chmod 644 /etc/letsencrypt/live/lxnd.cloud/*.pem
```

### Service Won't Start
```bash
# Check service status
sudo systemctl status codewave

# View detailed logs
sudo journalctl -u codewave --no-pager

# Test manual start
sudo node index.js
```

### Port Already in Use
```bash
# Check what's using port 443
sudo netstat -tlnp | grep :443

# Kill process if needed
sudo kill -9 <PID>
```

## Security Considerations

### File Permissions
- Let's Encrypt certificates: `644` permissions
- Application files: Appropriate user permissions
- Database: Restricted access

### Network Security
- Only expose necessary ports (80, 443)
- Use firewall to restrict access
- Regular security updates

### Application Security
- Run with minimal required privileges
- Regular dependency updates
- Monitor logs for suspicious activity

## Backup and Recovery

### Database Backup
```bash
# Backup database
cp database/learning_platform.db database/backup_$(date +%Y%m%d_%H%M%S).db

# Automated backup script
#!/bin/bash
BACKUP_DIR="/var/backups/codewave"
mkdir -p $BACKUP_DIR
cp database/learning_platform.db $BACKUP_DIR/db_$(date +%Y%m%d_%H%M%S).db
find $BACKUP_DIR -name "db_*" -mtime +7 -delete
```

### Certificate Backup
```bash
# Backup Let's Encrypt certificates
sudo tar -czf letsencrypt_backup_$(date +%Y%m%d).tar.gz /etc/letsencrypt/
```

## Performance Optimization

### PM2 Process Manager (Alternative to systemd)
```bash
# Install PM2
npm install -g pm2

# Start application
pm2 start index.js --name codewave

# Save PM2 configuration
pm2 save

# Setup startup script
pm2 startup
```

### Nginx Reverse Proxy (Optional)
For additional features like load balancing or static file serving:
```nginx
server {
    listen 80;
    server_name lxnd.cloud;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name lxnd.cloud;
    
    ssl_certificate /etc/letsencrypt/live/lxnd.cloud/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/lxnd.cloud/privkey.pem;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```
