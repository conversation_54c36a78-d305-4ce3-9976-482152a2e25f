const sqlite3 = require('sqlite3').verbose();
const { dbPath } = require('./database/init');

console.log('🏆 Creating Achievements System...');

const directDb = new sqlite3.Database(dbPath);

directDb.serialize(() => {
    const achievementStmt = directDb.prepare(`INSERT INTO achievements (name, description, requirement_type, requirement_value, points, icon, language) VALUES (?, ?, ?, ?, ?, ?, ?)`);
    
    const achievements = [
        // General achievements
        { name: '<PERSON>rste Schritte', description: 'Erstes Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 1, points: 10, icon: '🎯', language: 'general' },
        { name: '<PERSON><PERSON><PERSON>', description: '5 Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 5, points: 25, icon: '📚', language: 'general' },
        { name: 'Boss Killer', description: 'Erstes Boss Level abgeschlossen', requirement_type: 'boss_levels_completed', requirement_value: 1, points: 100, icon: '🏆', language: 'general' },
        { name: '<PERSON><PERSON><PERSON>rit<PERSON><PERSON>', description: '10 Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 10, points: 50, icon: '🚀', language: 'general' },
        { name: 'Experte', description: '25 Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 25, points: 100, icon: '🏆', language: 'general' },
        { name: 'Meister', description: '50 Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 50, points: 200, icon: '👑', language: 'general' },
        { name: 'Legende', description: '100 Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 100, points: 500, icon: '⭐', language: 'general' },
        
        // Score achievements
        { name: 'Punktesammler', description: '500 Punkte erreicht', requirement_type: 'total_score', requirement_value: 500, points: 50, icon: '💎', language: 'general' },
        { name: 'Highscore', description: '1000 Punkte erreicht', requirement_type: 'total_score', requirement_value: 1000, points: 100, icon: '⭐', language: 'general' },
        { name: 'Punktekönig', description: '2500 Punkte erreicht', requirement_type: 'total_score', requirement_value: 2500, points: 250, icon: '👑', language: 'general' },
        { name: 'Unaufhaltsam', description: '5000 Punkte erreicht', requirement_type: 'total_score', requirement_value: 5000, points: 500, icon: '🔥', language: 'general' },
        
        // Boss Level achievements
        { name: 'Boss Bezwinger', description: '3 Boss Level abgeschlossen', requirement_type: 'boss_levels_completed', requirement_value: 3, points: 200, icon: '⚔️', language: 'general' },
        { name: 'Boss Master', description: '5 Boss Level abgeschlossen', requirement_type: 'boss_levels_completed', requirement_value: 5, points: 300, icon: '🛡️', language: 'general' },
        { name: 'Final Boss Killer', description: 'Final Boss Level abgeschlossen', requirement_type: 'final_boss_completed', requirement_value: 1, points: 1000, icon: '👑', language: 'general' },
        
        // Language-specific achievements - HTML/CSS/JS
        { name: 'HTML Grundlagen', description: '5 HTML/CSS/JS Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 5, points: 30, icon: '🌐', language: 'html-css-js' },
        { name: 'CSS Styler', description: '10 HTML/CSS/JS Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 10, points: 50, icon: '🎨', language: 'html-css-js' },
        { name: 'JavaScript Ninja', description: '20 HTML/CSS/JS Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 20, points: 100, icon: '⚡', language: 'html-css-js' },
        { name: 'Frontend Master', description: '30 HTML/CSS/JS Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 30, points: 150, icon: '💻', language: 'html-css-js' },
        { name: 'Web Entwickler', description: 'Alle 40 HTML/CSS/JS Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 40, points: 200, icon: '🚀', language: 'html-css-js' },
        
        // Language-specific achievements - JavaScript+PHP
        { name: 'JS Anfänger', description: '5 JavaScript+PHP Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 5, points: 30, icon: '⚡', language: 'javascript-php' },
        { name: 'Fullstack Beginner', description: '10 JavaScript+PHP Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 10, points: 75, icon: '🔧', language: 'javascript-php' },
        { name: 'Fullstack Developer', description: '20 JavaScript+PHP Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 20, points: 150, icon: '🌟', language: 'javascript-php' },
        { name: 'Fullstack Master', description: 'Alle 40 JavaScript+PHP Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 40, points: 200, icon: '👑', language: 'javascript-php' },
        
        // Language-specific achievements - Python
        { name: 'Python Anfänger', description: '5 Python Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 5, points: 30, icon: '🐍', language: 'python' },
        { name: 'Python Entwickler', description: '10 Python Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 10, points: 75, icon: '🐍', language: 'python' },
        { name: 'Python Expert', description: '20 Python Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 20, points: 150, icon: '🐍', language: 'python' },
        { name: 'Python Master', description: 'Alle 40 Python Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 40, points: 200, icon: '🐍', language: 'python' },
        
        // Language-specific achievements - Go
        { name: 'Go Anfänger', description: '5 Go Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 5, points: 30, icon: '🦫', language: 'go' },
        { name: 'Go Entwickler', description: '10 Go Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 10, points: 75, icon: '🦫', language: 'go' },
        { name: 'Go Expert', description: '20 Go Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 20, points: 150, icon: '🦫', language: 'go' },
        { name: 'Go Master', description: 'Alle 40 Go Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 40, points: 200, icon: '🦫', language: 'go' },
        
        // Language-specific achievements - Java
        { name: 'Java Anfänger', description: '5 Java Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 5, points: 30, icon: '☕', language: 'java' },
        { name: 'Java Entwickler', description: '10 Java Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 10, points: 75, icon: '☕', language: 'java' },
        { name: 'Java Expert', description: '20 Java Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 20, points: 150, icon: '☕', language: 'java' },
        { name: 'Java Master', description: 'Alle 40 Java Level abgeschlossen', requirement_type: 'levels_completed', requirement_value: 40, points: 200, icon: '☕', language: 'java' },
        
        // Special achievements
        { name: 'Polyglott', description: 'Level in 3 verschiedenen Sprachen abgeschlossen', requirement_type: 'languages_learned', requirement_value: 3, points: 300, icon: '🌍', language: 'general' },
        { name: 'Vollständigkeit', description: 'Alle Kurse zu 100% abgeschlossen', requirement_type: 'all_courses_completed', requirement_value: 1, points: 1000, icon: '🏆', language: 'general' },
        { name: 'Perfektionist', description: '50 Level mit 100% Score abgeschlossen', requirement_type: 'perfect_scores', requirement_value: 50, points: 500, icon: '💯', language: 'general' },
        { name: 'Schnellschuss', description: '10 Level in einem Tag abgeschlossen', requirement_type: 'daily_streak', requirement_value: 10, points: 200, icon: '⚡', language: 'general' },
        { name: 'Ausdauer', description: '30 Tage in Folge aktiv', requirement_type: 'login_streak', requirement_value: 30, points: 300, icon: '🔥', language: 'general' }
    ];
    
    for (const achievement of achievements) {
        achievementStmt.run(achievement.name, achievement.description, achievement.requirement_type, achievement.requirement_value, achievement.points, achievement.icon, achievement.language);
    }
    
    achievementStmt.finalize();
    
    console.log('✅ All achievements created!');
    console.log(`📊 Total: ${achievements.length} achievements`);
    console.log('  - General: 14 achievements');
    console.log('  - HTML/CSS/JS: 5 achievements');
    console.log('  - JavaScript+PHP: 4 achievements');
    console.log('  - Python: 4 achievements');
    console.log('  - Go: 4 achievements');
    console.log('  - Java: 4 achievements');
    console.log('  - Special: 5 achievements');
    
    directDb.close();
    
    console.log('🎉 Achievement system created successfully!');
});
