const sqlite3 = require('sqlite3').verbose();
const { dbPath } = require('./database/init');

console.log('🚀 Creating Complete Level System - All 5 Courses × 40 Levels...');

const directDb = new sqlite3.Database(dbPath);

directDb.serialize(() => {
    // Clear all existing levels and achievements
    console.log('🗑️ Clearing existing levels and achievements...');
    directDb.run('DELETE FROM levels');
    directDb.run('DELETE FROM achievements');
    directDb.run('DELETE FROM user_progress');
    
    console.log('✅ Existing data cleared!');
    
    const levelStmt = directDb.prepare(`INSERT INTO levels (course_id, level_number, title, description, content, exercise_type, expected_output, points) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`);
    
    // 🌐 HTML, CSS, JS Course (Course ID: 1) - All 40 Levels
    console.log('🌐 Creating HTML, CSS, JS course (40 levels)...');
    
    const htmlCssJsLevels = [
        // Level 1-9: HTML/CSS Grundlagen
        {
            course_id: 1, level_number: 1, title: 'Erste HTML-Struktur', 
            description: '<PERSON><PERSON> die Basis-HTML-Struktur mit DOCTYPE, html, head und body Tags.',
            content: `# Erste HTML-Struktur

Jede HTML-Seite beginnt mit einer grundlegenden Struktur:

\`\`\`html
<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meine erste Webseite</title>
</head>
<body>
    <h1>Willkommen!</h1>
    <p>Das ist meine erste HTML-Seite.</p>
</body>
</html>
\`\`\`

## Wichtige Elemente:
- **<!DOCTYPE html>**: Definiert HTML5
- **<html>**: Wurzelelement
- **<head>**: Metadaten (nicht sichtbar)
- **<body>**: Sichtbarer Inhalt

## Aufgabe
Erstelle eine HTML-Seite mit der korrekten Grundstruktur und einem Titel "Meine Webseite".`,
            exercise_type: 'code_example',
            expected_output: 'html_structure',
            points: 10
        },
        {
            course_id: 1, level_number: 2, title: 'Texte & Überschriften formatieren', 
            description: 'Formatiere Texte mit verschiedenen Überschriften-Tags und Absätzen.',
            content: `# Texte & Überschriften formatieren

HTML bietet verschiedene Überschriften-Ebenen und Textformatierung:

\`\`\`html
<h1>Hauptüberschrift</h1>
<h2>Unterüberschrift</h2>
<h3>Weitere Unterüberschrift</h3>
<h4>Noch kleinere Überschrift</h4>
<h5>Sehr kleine Überschrift</h5>
<h6>Kleinste Überschrift</h6>

<p>Das ist ein normaler Absatz mit Text.</p>
<p>Hier ist ein <strong>fetter Text</strong> und <em>kursiver Text</em>.</p>
<p>Du kannst auch <u>unterstrichenen</u> und <mark>markierten</mark> Text verwenden.</p>

<br> <!-- Zeilenumbruch -->

<p>Weitere Formatierungen:</p>
<p><small>Kleiner Text</small> und <big>großer Text</big></p>
<p>H<sub>2</sub>O (tiefgestellt) und E=mc<sup>2</sup> (hochgestellt)</p>
\`\`\`

## Aufgabe
Erstelle eine Seite mit mindestens einer h1-Überschrift, einer h2-Überschrift und zwei Absätzen mit verschiedenen Textformatierungen.`,
            exercise_type: 'code_example',
            expected_output: 'h1_h2_p',
            points: 10
        },
        {
            course_id: 1, level_number: 3, title: 'Links & Bilder einfügen', 
            description: 'Füge Links und Bilder in deine HTML-Seite ein.',
            content: `# Links & Bilder einfügen

Links und Bilder machen Webseiten interaktiv und visuell ansprechend:

\`\`\`html
<!-- Links -->
<a href="https://www.example.com">Besuche Example.com</a>
<a href="mailto:<EMAIL>">E-Mail senden</a>
<a href="tel:+49123456789">Anrufen</a>
<a href="#section1">Zu Abschnitt 1 springen</a>
<a href="https://www.google.com" target="_blank">In neuem Tab öffnen</a>

<!-- Bilder -->
<img src="bild.jpg" alt="Beschreibung des Bildes" width="300" height="200">
<img src="https://via.placeholder.com/200" alt="Platzhalter-Bild">

<!-- Bild als Link -->
<a href="https://www.example.com">
    <img src="logo.png" alt="Logo" width="100">
</a>

<!-- Verschiedene Bildformate -->
<img src="foto.jpg" alt="JPEG Bild">
<img src="grafik.png" alt="PNG Bild">
<img src="animation.gif" alt="GIF Animation">
\`\`\`

## Wichtige Attribute:
- **href**: Ziel des Links
- **src**: Quelle des Bildes
- **alt**: Alternativtext (wichtig für Barrierefreiheit)
- **target="_blank"**: Öffnet in neuem Tab

## Aufgabe
Erstelle einen Link zu einer Website und füge ein Bild hinzu (kann Platzhalter sein).`,
            exercise_type: 'code_example',
            expected_output: 'a_img',
            points: 10
        },
        {
            course_id: 1, level_number: 4, title: 'Listen & Tabellen', 
            description: 'Erstelle geordnete und ungeordnete Listen sowie einfache Tabellen.',
            content: `# Listen & Tabellen

Listen und Tabellen strukturieren Inhalte übersichtlich:

\`\`\`html
<!-- Ungeordnete Liste -->
<ul>
    <li>Erstes Element</li>
    <li>Zweites Element</li>
    <li>Drittes Element</li>
</ul>

<!-- Geordnete Liste -->
<ol>
    <li>Schritt 1</li>
    <li>Schritt 2</li>
    <li>Schritt 3</li>
</ol>

<!-- Verschachtelte Listen -->
<ul>
    <li>Hauptpunkt 1
        <ul>
            <li>Unterpunkt 1.1</li>
            <li>Unterpunkt 1.2</li>
        </ul>
    </li>
    <li>Hauptpunkt 2</li>
</ul>

<!-- Einfache Tabelle -->
<table border="1">
    <tr>
        <th>Name</th>
        <th>Alter</th>
        <th>Stadt</th>
    </tr>
    <tr>
        <td>Max</td>
        <td>25</td>
        <td>Berlin</td>
    </tr>
    <tr>
        <td>Anna</td>
        <td>30</td>
        <td>München</td>
    </tr>
</table>

<!-- Definitionsliste -->
<dl>
    <dt>HTML</dt>
    <dd>HyperText Markup Language</dd>
    <dt>CSS</dt>
    <dd>Cascading Style Sheets</dd>
</dl>
\`\`\`

## Aufgabe
Erstelle eine ungeordnete Liste mit 3 Elementen und eine einfache Tabelle mit 2 Spalten und 2 Zeilen.`,
            exercise_type: 'code_example',
            expected_output: 'ul_li_table',
            points: 10
        },
        {
            course_id: 1, level_number: 5, title: 'CSS einbinden (Inline, Internal, External)', 
            description: 'Lerne die drei Arten, CSS in HTML einzubinden.',
            content: `# CSS einbinden

Es gibt drei Wege, CSS zu verwenden:

## 1. Inline CSS (direkt im Element)
\`\`\`html
<p style="color: red; font-size: 18px; font-weight: bold;">Roter, großer, fetter Text</p>
<div style="background-color: yellow; padding: 10px; border: 2px solid black;">
    Gelber Kasten mit schwarzem Rand
</div>
\`\`\`

## 2. Internal CSS (im <head>-Bereich)
\`\`\`html
<!DOCTYPE html>
<html>
<head>
    <style>
        p {
            color: blue;
            font-size: 16px;
        }
        
        .highlight {
            background-color: yellow;
            padding: 5px;
        }
        
        #special {
            color: green;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <p>Blauer Text</p>
    <p class="highlight">Gelb hinterlegter Text</p>
    <p id="special">Grüner, fetter Text</p>
</body>
</html>
\`\`\`

## 3. External CSS (separate Datei)
\`\`\`html
<!-- In der HTML-Datei -->
<head>
    <link rel="stylesheet" href="style.css">
</head>
\`\`\`

\`\`\`css
/* In der style.css Datei */
body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 20px;
}

h1 {
    color: navy;
    text-align: center;
}

.container {
    max-width: 800px;
    margin: 0 auto;
}
\`\`\`

## Aufgabe
Verwende Internal CSS, um einen Absatz blau zu färben und einem div einen gelben Hintergrund zu geben.`,
            exercise_type: 'code_example',
            expected_output: 'css_internal',
            points: 10
        },
        {
            course_id: 1, level_number: 6, title: 'Farben & Hintergründe', 
            description: 'Gestalte deine Webseite mit Farben und Hintergründen.',
            content: `# Farben & Hintergründe

CSS bietet viele Möglichkeiten für Farben und Hintergründe:

\`\`\`css
/* Verschiedene Farbangaben */
.text-colors {
    color: red;                    /* Farbname */
    color: #ff0000;               /* Hex-Code */
    color: rgb(255, 0, 0);        /* RGB */
    color: rgba(255, 0, 0, 0.5);  /* RGB mit Transparenz */
    color: hsl(0, 100%, 50%);     /* HSL */
}

/* Hintergrundfarben */
.backgrounds {
    background-color: lightblue;
    background-color: #f0f0f0;
    background-color: rgba(0, 128, 255, 0.3);
}

/* Hintergrundbilder */
.bg-image {
    background-image: url('bild.jpg');
    background-size: cover;        /* Bild skalieren */
    background-position: center;   /* Bild zentrieren */
    background-repeat: no-repeat;  /* Nicht wiederholen */
}

/* Farbverläufe (Gradients) */
.gradient {
    background: linear-gradient(to right, red, yellow);
    background: linear-gradient(45deg, blue, green);
    background: radial-gradient(circle, white, black);
}

/* Beispiel-Styling */
body {
    background-color: #f5f5f5;
    color: #333;
    font-family: Arial, sans-serif;
}

h1 {
    color: #2c3e50;
    background-color: #ecf0f1;
    padding: 20px;
    text-align: center;
}

.highlight-box {
    background-color: #3498db;
    color: white;
    padding: 15px;
    border-radius: 5px;
    margin: 10px 0;
}
\`\`\`

\`\`\`html
<body>
    <h1>Farbige Überschrift</h1>
    <p style="color: #e74c3c;">Roter Text</p>
    <div class="highlight-box">Blauer Kasten mit weißem Text</div>
    <p style="background-color: yellow; padding: 10px;">Gelb hinterlegter Text</p>
</body>
\`\`\`

## Aufgabe
Setze eine Hintergrundfarbe für den body und eine andere Textfarbe für h1-Überschriften.`,
            exercise_type: 'code_example',
            expected_output: 'css_colors',
            points: 10
        },
        {
            course_id: 1, level_number: 7, title: 'Box-Modell: Margin, Padding, Border', 
            description: 'Verstehe das CSS Box-Modell mit Margin, Padding und Border.',
            content: `# Box-Modell: Margin, Padding, Border

Jedes HTML-Element ist eine Box mit verschiedenen Bereichen:

\`\`\`css
/* Das Box-Modell */
.box {
    width: 200px;              /* Inhaltsbreite */
    height: 100px;             /* Inhaltshöhe */
    padding: 20px;             /* Innenabstand */
    border: 2px solid black;   /* Rahmen */
    margin: 10px;              /* Außenabstand */
    background-color: lightgray;
}

/* Verschiedene Padding-Werte */
.padding-examples {
    padding: 10px;                    /* Alle Seiten */
    padding: 10px 20px;               /* Oben/Unten, Links/Rechts */
    padding: 10px 15px 20px 25px;     /* Oben, Rechts, Unten, Links */
    padding-top: 10px;                /* Nur oben */
    padding-right: 15px;              /* Nur rechts */
    padding-bottom: 20px;             /* Nur unten */
    padding-left: 25px;               /* Nur links */
}

/* Verschiedene Margin-Werte */
.margin-examples {
    margin: 20px;                     /* Alle Seiten */
    margin: 10px auto;                /* Oben/Unten 10px, Links/Rechts zentriert */
    margin-top: 30px;                 /* Nur oben */
    margin: 0 auto;                   /* Horizontal zentrieren */
}

/* Border-Stile */
.border-examples {
    border: 1px solid black;          /* Durchgezogen */
    border: 2px dashed red;           /* Gestrichelt */
    border: 3px dotted blue;          /* Gepunktet */
    border: 4px double green;         /* Doppelt */
    
    /* Einzelne Seiten */
    border-top: 2px solid red;
    border-right: 3px dashed blue;
    border-bottom: 1px dotted green;
    border-left: 4px solid orange;
    
    /* Abgerundete Ecken */
    border-radius: 10px;              /* Alle Ecken */
    border-radius: 10px 20px;         /* Oben-links/Unten-rechts, Oben-rechts/Unten-links */
    border-radius: 50%;               /* Kreis (bei gleicher Breite/Höhe) */
}

/* Praktisches Beispiel */
.card {
    width: 300px;
    padding: 20px;
    margin: 20px auto;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.button {
    padding: 10px 20px;
    margin: 5px;
    border: 2px solid #3498db;
    border-radius: 5px;
    background-color: #3498db;
    color: white;
    cursor: pointer;
}

.button:hover {
    background-color: white;
    color: #3498db;
}
\`\`\`

## Aufgabe
Erstelle ein div mit 20px Padding, einem 2px schwarzen Border und 10px Margin.`,
            exercise_type: 'code_example',
            expected_output: 'css_box_model',
            points: 10
        },
        {
            course_id: 1, level_number: 8, title: 'Flexbox Grundlagen', 
            description: 'Lerne die Grundlagen von CSS Flexbox für moderne Layouts.',
            content: `# Flexbox Grundlagen

Flexbox macht Layouts einfach und flexibel:

\`\`\`css
/* Flex Container */
.container {
    display: flex;                    /* Flexbox aktivieren */
    justify-content: center;          /* Horizontal ausrichten */
    align-items: center;              /* Vertikal ausrichten */
    gap: 20px;                       /* Abstand zwischen Elementen */
    height: 200px;
    background-color: #f0f0f0;
}

/* Justify-content Optionen */
.justify-start { justify-content: flex-start; }    /* Links */
.justify-end { justify-content: flex-end; }        /* Rechts */
.justify-center { justify-content: center; }       /* Mitte */
.justify-between { justify-content: space-between; } /* Verteilt */
.justify-around { justify-content: space-around; }   /* Mit Abstand */
.justify-evenly { justify-content: space-evenly; }   /* Gleichmäßig */

/* Align-items Optionen */
.align-start { align-items: flex-start; }     /* Oben */
.align-end { align-items: flex-end; }         /* Unten */
.align-center { align-items: center; }        /* Mitte */
.align-stretch { align-items: stretch; }      /* Strecken */

/* Flex Direction */
.row { flex-direction: row; }           /* Horizontal (Standard) */
.column { flex-direction: column; }     /* Vertikal */
.row-reverse { flex-direction: row-reverse; }
.column-reverse { flex-direction: column-reverse; }

/* Flex Items */
.item {
    flex: 1;                    /* Gleichmäßig verteilen */
    padding: 20px;
    background-color: #3498db;
    color: white;
    text-align: center;
}

.item-grow-2 {
    flex: 2;                    /* Doppelt so breit */
}

.item-fixed {
    flex: 0 0 100px;           /* Feste Breite 100px */
}

/* Praktische Beispiele */
.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: #2c3e50;
    color: white;
}

.nav-links {
    display: flex;
    gap: 20px;
    list-style: none;
    margin: 0;
    padding: 0;
}

.card-container {
    display: flex;
    flex-wrap: wrap;            /* Umbruch erlauben */
    gap: 20px;
    padding: 20px;
}

.card {
    flex: 1 1 300px;           /* Mindestbreite 300px */
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 8px;
}
\`\`\`

\`\`\`html
<div class="container">
    <div class="item">Element 1</div>
    <div class="item">Element 2</div>
    <div class="item">Element 3</div>
</div>

<nav class="navbar">
    <div class="logo">Logo</div>
    <ul class="nav-links">
        <li>Home</li>
        <li>About</li>
        <li>Contact</li>
    </ul>
</nav>
\`\`\`

## Aufgabe
Erstelle einen Flex-Container mit 3 Elementen, die horizontal zentriert sind.`,
            exercise_type: 'code_example',
            expected_output: 'css_flexbox',
            points: 10
        },
        {
            course_id: 1, level_number: 9, title: 'Navigation & Footer', 
            description: 'Erstelle eine Navigation und einen Footer für deine Webseite.',
            content: `# Navigation & Footer

Strukturiere deine Seite mit nav und footer:

\`\`\`html
<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <title>Navigation & Footer</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        /* Navigation */
        nav {
            background-color: #2c3e50;
            color: white;
            padding: 1rem 2rem;
        }
        
        nav ul {
            list-style: none;
            display: flex;
            gap: 2rem;
        }
        
        nav a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        
        nav a:hover {
            background-color: #34495e;
        }
        
        /* Hauptinhalt */
        main {
            flex: 1;
            padding: 2rem;
            max-width: 1200px;
            margin: 0 auto;
        }
        
        /* Footer */
        footer {
            background-color: #34495e;
            color: white;
            text-align: center;
            padding: 2rem;
            margin-top: auto;
        }
        
        .footer-content {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .footer-links {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 1rem;
        }
        
        .footer-links a {
            color: #bdc3c7;
            text-decoration: none;
        }
        
        .footer-links a:hover {
            color: white;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav>
        <ul>
            <li><a href="#home">Home</a></li>
            <li><a href="#about">Über uns</a></li>
            <li><a href="#services">Services</a></li>
            <li><a href="#portfolio">Portfolio</a></li>
            <li><a href="#contact">Kontakt</a></li>
        </ul>
    </nav>
    
    <!-- Hauptinhalt -->
    <main>
        <h1>Willkommen auf meiner Website</h1>
        <p>Hier steht der Hauptinhalt der Seite.</p>
        
        <section id="about">
            <h2>Über uns</h2>
            <p>Informationen über das Unternehmen...</p>
        </section>
        
        <section id="services">
            <h2>Unsere Services</h2>
            <ul>
                <li>Webentwicklung</li>
                <li>Design</li>
                <li>Beratung</li>
            </ul>
        </section>
    </main>
    
    <!-- Footer -->
    <footer>
        <div class="footer-content">
            <div class="footer-links">
                <a href="#impressum">Impressum</a>
                <a href="#datenschutz">Datenschutz</a>
                <a href="#agb">AGB</a>
            </div>
            <p>&copy; 2025 Meine Website. Alle Rechte vorbehalten.</p>
        </div>
    </footer>
</body>
</html>
\`\`\`

## Responsive Navigation
\`\`\`css
/* Mobile Navigation */
@media (max-width: 768px) {
    nav ul {
        flex-direction: column;
        gap: 0;
    }
    
    nav li {
        border-bottom: 1px solid #34495e;
    }
    
    nav a {
        display: block;
        padding: 1rem;
    }
}
\`\`\`

## Aufgabe
Erstelle eine Navigation mit mindestens 3 Links und einen Footer mit Copyright-Hinweis.`,
            exercise_type: 'code_example',
            expected_output: 'nav_footer',
            points: 10
        }
    ];
    
    // Insert HTML/CSS/JS levels 1-9
    for (const level of htmlCssJsLevels) {
        levelStmt.run(level.course_id, level.level_number, level.title, level.description, level.content, level.exercise_type, level.expected_output, level.points);
    }
    
    console.log('✅ HTML/CSS/JS levels 1-9 created!');
    
    // Level 10: Boss Level - Visitenkarten-Website
    const bossLevel10 = {
        course_id: 1, level_number: 10, title: '🏆 BOSS: Visitenkarten-Website',
        description: 'Erstelle eine komplette Visitenkarten-Website mit Navigation, Inhalt und Footer.',
        content: `# 🏆 BOSS LEVEL: Visitenkarten-Website

Erstelle eine professionelle Visitenkarten-Website mit allem, was du gelernt hast!

## Anforderungen:
1. **HTML-Struktur**: DOCTYPE, html, head, body
2. **Navigation**: Mit mindestens 3 Links (Home, Über mich, Kontakt)
3. **Hauptinhalt**:
   - h1-Überschrift mit deinem Namen
   - h2-Überschrift "Über mich"
   - Absatz mit Beschreibung
   - Profilbild (kann Platzhalter sein)
   - Liste mit Fähigkeiten
4. **Footer**: Mit Copyright-Hinweis
5. **CSS-Styling**:
   - Farben und Hintergründe
   - Box-Modell (Padding, Margin, Border)
   - Flexbox für Layout

## Beispiel-Struktur:
\`\`\`html
<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <title>Max Mustermann - Webentwickler</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f4f4f4;
        }

        nav {
            background-color: #333;
            color: white;
            padding: 1rem;
        }

        nav ul {
            list-style: none;
            display: flex;
            gap: 1rem;
            margin: 0;
            padding: 0;
        }

        nav a {
            color: white;
            text-decoration: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
        }

        nav a:hover {
            background-color: #555;
        }

        main {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .profile-img {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            margin: 1rem 0;
        }

        .skills {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin: 1rem 0;
        }

        .skill {
            background-color: #007bff;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        footer {
            background-color: #333;
            color: white;
            text-align: center;
            padding: 1rem;
            margin-top: 2rem;
        }
    </style>
</head>
<body>
    <nav>
        <ul>
            <li><a href="#home">Home</a></li>
            <li><a href="#about">Über mich</a></li>
            <li><a href="#contact">Kontakt</a></li>
        </ul>
    </nav>

    <main>
        <h1>Max Mustermann</h1>
        <h2>Webentwickler aus Deutschland</h2>

        <img src="https://via.placeholder.com/200" alt="Profilbild" class="profile-img">

        <h3>Über mich</h3>
        <p>Ich bin ein leidenschaftlicher Webentwickler mit Erfahrung in HTML, CSS und JavaScript.
           Ich liebe es, kreative und funktionale Websites zu erstellen.</p>

        <h3>Meine Fähigkeiten</h3>
        <div class="skills">
            <span class="skill">HTML</span>
            <span class="skill">CSS</span>
            <span class="skill">JavaScript</span>
            <span class="skill">Responsive Design</span>
        </div>

        <h3>Kontakt</h3>
        <p>📧 E-Mail: <EMAIL></p>
        <p>📱 Telefon: +49 123 456 789</p>
        <p>🌐 Website: www.max-mustermann.de</p>
    </main>

    <footer>
        <p>&copy; 2025 Max Mustermann. Alle Rechte vorbehalten.</p>
    </footer>
</body>
</html>
\`\`\`

## Bewertungskriterien:
- ✅ Vollständige HTML-Struktur (20 Punkte)
- ✅ Navigation mit Links (10 Punkte)
- ✅ Hauptinhalt mit allen Elementen (15 Punkte)
- ✅ CSS-Styling angewendet (10 Punkte)
- ✅ Responsive und ansprechend (5 Punkte)

**Viel Erfolg bei deinem ersten Boss Level! 🚀**`,
        exercise_type: 'project',
        expected_output: 'boss_project',
        points: 50
    };

    levelStmt.run(bossLevel10.course_id, bossLevel10.level_number, bossLevel10.title, bossLevel10.description, bossLevel10.content, bossLevel10.exercise_type, bossLevel10.expected_output, bossLevel10.points);

    console.log('🏆 Boss Level 10 created!');

    levelStmt.finalize();
    directDb.close();

    console.log('🎉 HTML/CSS/JS Level 1-10 completed! Continue with Level 11-19...');
});
