const sqlite3 = require('sqlite3').verbose();
const { dbPath } = require('./database/init');

console.log('🏆 Creating Level-Specific Achievements...');

const directDb = new sqlite3.Database(dbPath);

directDb.serialize(() => {
    // Clear existing achievements
    directDb.run('DELETE FROM achievements');
    
    const achievementStmt = directDb.prepare(`INSERT INTO achievements (name, description, icon, points_required, badge_color) VALUES (?, ?, ?, ?, ?)`);
    
    // Level-based Achievements
    const achievements = [
        // Beginner Achievements (Level 1-10)
        {name: '<PERSON><PERSON><PERSON>itte', description: 'Schließe dein erstes Level ab', icon: '🚀', points_required: 10, badge_color: '#28a745'},
        {name: 'HTML Grundlagen', description: 'Meistere die HTML-Grundstruktur', icon: '🌐', points_required: 50, badge_color: '#e34c26'},
        {name: 'CSS Stylist', description: 'Verwende CSS zum ersten Mal', icon: '🎨', points_required: 80, badge_color: '#1572b6'},
        {name: 'Boss Killer', description: 'Besiege dein erstes Boss Level', icon: '⚔️', points_required: 100, badge_color: '#ffc107'},
        
        // Intermediate Achievements (Level 11-20)
        {name: 'JavaScript Ninja', description: 'Schreibe dein erstes JavaScript', icon: '⚡', points_required: 150, badge_color: '#f7df1e'},
        {name: 'DOM Manipulator', description: 'Verändere HTML mit JavaScript', icon: '🔧', points_required: 200, badge_color: '#17a2b8'},
        {name: 'Event Handler', description: 'Reagiere auf Benutzerinteraktionen', icon: '👆', points_required: 250, badge_color: '#6f42c1'},
        {name: 'Doppel Boss', description: 'Besiege 2 Boss Level', icon: '🏆', points_required: 300, badge_color: '#fd7e14'},
        
        // Advanced Achievements (Level 21-30)
        {name: 'Responsive Designer', description: 'Erstelle responsive Layouts', icon: '📱', points_required: 400, badge_color: '#20c997'},
        {name: 'API Master', description: 'Verwende APIs in deinen Projekten', icon: '🔗', points_required: 500, badge_color: '#6610f2'},
        {name: 'Triple Boss', description: 'Besiege 3 Boss Level', icon: '👑', points_required: 600, badge_color: '#e83e8c'},
        
        // Expert Achievements (Level 31-40)
        {name: 'Frontend Expert', description: 'Meistere alle Frontend-Technologien', icon: '💻', points_required: 800, badge_color: '#dc3545'},
        {name: 'Fullstack Warrior', description: 'Beherrsche Frontend und Backend', icon: '⚔️', points_required: 1000, badge_color: '#343a40'},
        {name: 'Final Boss Slayer', description: 'Besiege ein Final Boss Level', icon: '🐉', points_required: 1500, badge_color: '#6f42c1'},
        
        // Course-Specific Achievements
        {name: 'HTML/CSS/JS Meister', description: 'Schließe den HTML/CSS/JS Kurs ab', icon: '🌐', points_required: 2000, badge_color: '#e34c26'},
        {name: 'PHP Entwickler', description: 'Schließe den JavaScript+PHP Kurs ab', icon: '🐘', points_required: 2000, badge_color: '#777bb4'},
        {name: 'Go Gopher', description: 'Schließe den Go Kurs ab', icon: '🦫', points_required: 2000, badge_color: '#00add8'},
        {name: 'Python Schlangen-Beschwörer', description: 'Schließe den Python Kurs ab', icon: '🐍', points_required: 2000, badge_color: '#3776ab'},
        {name: 'Java Barista', description: 'Schließe den Java Kurs ab', icon: '☕', points_required: 2000, badge_color: '#ed8b00'},
        
        // Special Achievements
        {name: 'Polyglott', description: 'Lerne 3 verschiedene Programmiersprachen', icon: '🌍', points_required: 3000, badge_color: '#17a2b8'},
        {name: 'Code Warrior', description: 'Sammle 5000 Punkte', icon: '⚔️', points_required: 5000, badge_color: '#dc3545'},
        {name: 'Legende', description: 'Sammle 10000 Punkte', icon: '🏆', points_required: 10000, badge_color: '#ffd700'},
        
        // Streak Achievements
        {name: 'Fleißiger Lerner', description: 'Schließe 5 Level an einem Tag ab', icon: '🔥', points_required: 50, badge_color: '#ff6b35'},
        {name: 'Wochenend-Warrior', description: 'Lerne am Wochenende', icon: '📅', points_required: 100, badge_color: '#28a745'},
        {name: 'Nachteulen-Coder', description: 'Code nach 22 Uhr', icon: '🦉', points_required: 200, badge_color: '#6f42c1'},
        
        // Performance Achievements
        {name: 'Perfektionist', description: 'Erreiche 100% in 10 Leveln', icon: '💯', points_required: 500, badge_color: '#28a745'},
        {name: 'Schnellschreiber', description: 'Schließe ein Level in unter 5 Minuten ab', icon: '⚡', points_required: 100, badge_color: '#ffc107'},
        {name: 'Ausdauer-Champion', description: 'Lerne 2 Stunden am Stück', icon: '💪', points_required: 300, badge_color: '#dc3545'},
        
        // Boss Level Achievements
        {name: 'Visitenkarten-Designer', description: 'Erstelle deine erste Website (Level 10)', icon: '💼', points_required: 100, badge_color: '#17a2b8'},
        {name: 'To-Do Meister', description: 'Baue eine funktionierende To-Do App (Level 20)', icon: '✅', points_required: 300, badge_color: '#28a745'},
        {name: 'Quiz Master', description: 'Erstelle eine interaktive Quiz-App (Level 30)', icon: '❓', points_required: 600, badge_color: '#ffc107'},
        {name: 'Portfolio Künstler', description: 'Baue dein ultimatives Portfolio (Level 40)', icon: '🎨', points_required: 1500, badge_color: '#6f42c1'},
        
        // Language-Specific Boss Achievements
        {name: 'Gästebuch Autor', description: 'Erstelle ein PHP Gästebuch', icon: '📝', points_required: 100, badge_color: '#777bb4'},
        {name: 'Login Guardian', description: 'Baue ein sicheres Login-System', icon: '🔐', points_required: 300, badge_color: '#dc3545'},
        {name: 'Forum Moderator', description: 'Erstelle ein Mini-Forum', icon: '💬', points_required: 600, badge_color: '#17a2b8'},
        {name: 'Social Media Mogul', description: 'Baue eine Social Media Plattform', icon: '📱', points_required: 1500, badge_color: '#e83e8c'},
        
        // Go Achievements
        {name: 'Kontakt Manager', description: 'Erstelle einen Go Kontaktspeicher', icon: '📇', points_required: 100, badge_color: '#00add8'},
        {name: 'Notizen Ninja', description: 'Baue eine Notizen-API in Go', icon: '📋', points_required: 300, badge_color: '#00add8'},
        {name: 'Blog Builder', description: 'Erstelle eine Blog API mit Auth', icon: '📰', points_required: 600, badge_color: '#00add8'},
        {name: 'API Architekt', description: 'Baue eine komplette REST API', icon: '🏗️', points_required: 1500, badge_color: '#00add8'},
        
        // Python Achievements
        {name: 'Rechenkünstler', description: 'Baue einen Python Taschenrechner', icon: '🧮', points_required: 100, badge_color: '#3776ab'},
        {name: 'Adressbuch Verwalter', description: 'Erstelle ein OOP Adressbuch', icon: '📞', points_required: 300, badge_color: '#3776ab'},
        {name: 'Flask Magier', description: 'Baue eine Flask Web-App', icon: '🌶️', points_required: 600, badge_color: '#3776ab'},
        {name: 'Python Vollprofi', description: 'Erstelle eine komplette Python-App', icon: '🐍', points_required: 1500, badge_color: '#3776ab'},
        
        // Java Achievements
        {name: 'Bank Manager', description: 'Erstelle eine Bankkonto-Klasse', icon: '🏦', points_required: 100, badge_color: '#ed8b00'},
        {name: 'Bibliothekar', description: 'Baue eine Bibliotheksverwaltung', icon: '📚', points_required: 300, badge_color: '#ed8b00'},
        {name: 'GUI Gestalter', description: 'Erstelle eine GUI To-Do App', icon: '🖥️', points_required: 600, badge_color: '#ed8b00'},
        {name: 'Enterprise Entwickler', description: 'Baue eine komplette Java Enterprise App', icon: '🏢', points_required: 1500, badge_color: '#ed8b00'},
        
        // Milestone Achievements
        {name: 'Hundert Punkte', description: 'Sammle deine ersten 100 Punkte', icon: '💯', points_required: 100, badge_color: '#28a745'},
        {name: 'Fünfhundert Punkte', description: 'Sammle 500 Punkte', icon: '🎯', points_required: 500, badge_color: '#17a2b8'},
        {name: 'Tausend Punkte', description: 'Sammle 1000 Punkte', icon: '🚀', points_required: 1000, badge_color: '#ffc107'},
        {name: 'Zweitausend Punkte', description: 'Sammle 2000 Punkte', icon: '⭐', points_required: 2000, badge_color: '#fd7e14'},
        {name: 'Fünftausend Punkte', description: 'Sammle 5000 Punkte', icon: '🌟', points_required: 5000, badge_color: '#e83e8c'},
        {name: 'Zehntausend Punkte', description: 'Sammle 10000 Punkte - Du bist eine Legende!', icon: '👑', points_required: 10000, badge_color: '#ffd700'}
    ];
    
    // Insert all achievements
    for (const achievement of achievements) {
        achievementStmt.run(achievement.name, achievement.description, achievement.icon, achievement.points_required, achievement.badge_color);
    }
    
    console.log(`✅ ${achievements.length} level-specific achievements created!`);
    
    achievementStmt.finalize();
    directDb.close();
    
    console.log('🎉 All achievements created successfully!');
});
