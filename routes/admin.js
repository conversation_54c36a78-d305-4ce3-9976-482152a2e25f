const express = require('express');
const router = express.Router();
const Database = require('../database/db');
const { requireAdmin } = require('../middleware/admin');
const EmailService = require('../services/emailService');
const db = new Database();
const emailService = new EmailService();

// Apply admin middleware to all routes
router.use(requireAdmin);

// Admin Dashboard
router.get('/', (req, res) => {
    // Get statistics for dashboard
    db.getAllUsers((err, users) => {
        if (err) {
            console.error('Error fetching users:', err);
            return res.status(500).send('Internal server error');
        }

        db.getAllCourses((err, courses) => {
            if (err) {
                console.error('Error fetching courses:', err);
                return res.status(500).send('Internal server error');
            }

            db.getAdminSetting('global_premium_enabled', (err, setting) => {
                if (err) {
                    console.error('Error fetching premium setting:', err);
                    return res.status(500).send('Internal server error');
                }

                db.getAdminAuditLog(20, (err, auditLog) => {
                    if (err) {
                        console.error('Error fetching audit log:', err);
                        return res.status(500).send('Internal server error');
                    }

                    const stats = {
                        totalUsers: users.length,
                        adminUsers: users.filter(u => u.is_admin).length,
                        bannedUsers: users.filter(u => u.is_banned).length,
                        totalCourses: courses.length,
                        globalPremiumEnabled: setting && setting.setting_value === 'true'
                    };

                    res.render('admin/dashboard', {
                        title: 'Admin Dashboard',
                        username: req.session.username,
                        user: req.user,
                        currentLanguage: req.session.language || 'en',
                        stats,
                        users: users.slice(0, 10), // Show first 10 users
                        courses,
                        auditLog
                    });
                });
            });
        });
    });
});

// User Management
router.get('/users', (req, res) => {
    db.getAllUsers((err, users) => {
        if (err) {
            console.error('Error fetching users:', err);
            return res.status(500).send('Internal server error');
        }

        db.getAllCourses((err, courses) => {
            if (err) {
                console.error('Error fetching courses:', err);
                return res.status(500).send('Internal server error');
            }

            // Get course access for each user
            const userPromises = users.map(user => {
                return new Promise((resolve) => {
                    db.getUserCourseAccess(user.id, (err, access) => {
                        user.courseAccess = err ? [] : access.map(a => a.course_id);
                        resolve(user);
                    });
                });
            });

            Promise.all(userPromises).then(usersWithAccess => {
                res.render('admin/users', {
                    title: 'User Management',
                    username: req.session.username,
                    user: req.user,
                    currentLanguage: req.session.language || 'en',
                    users: usersWithAccess,
                    courses
                });
            });
        });
    });
});

// Update user admin status
router.post('/users/:id/admin', (req, res) => {
    const userId = parseInt(req.params.id);
    const isAdmin = req.body.isAdmin === 'true';

    db.updateUserAdminStatus(userId, isAdmin, req.session.userId, (err) => {
        if (err) {
            console.error('Error updating admin status:', err);
            return res.json({ success: false, error: 'Database error' });
        }

        res.json({ success: true });
    });
});

// Update user ban status
router.post('/users/:id/ban', (req, res) => {
    const userId = parseInt(req.params.id);
    const isBanned = req.body.isBanned === 'true';

    db.updateUserBanStatus(userId, isBanned, req.session.userId, (err) => {
        if (err) {
            console.error('Error updating ban status:', err);
            return res.json({ success: false, error: 'Database error' });
        }

        res.json({ success: true });
    });
});

// Delete user
router.delete('/users/:id', (req, res) => {
    const userId = parseInt(req.params.id);

    // Prevent deleting yourself
    if (userId === req.session.userId) {
        return res.json({ success: false, error: 'Cannot delete your own account' });
    }

    db.deleteUser(userId, req.session.userId, (err) => {
        if (err) {
            console.error('Error deleting user:', err);
            return res.json({ success: false, error: 'Database error' });
        }

        res.json({ success: true });
    });
});

// Course Access Management
router.post('/users/:id/course-access', (req, res) => {
    const userId = parseInt(req.params.id);
    const courseId = parseInt(req.body.courseId);
    const grant = req.body.grant;

    if (grant) {
        db.grantCourseAccess(userId, courseId, req.session.userId, (err) => {
            if (err) {
                console.error('Error granting course access:', err);
                return res.json({ success: false, error: 'Database error' });
            }
            res.json({ success: true });
        });
    } else {
        db.revokeCourseAccess(userId, courseId, req.session.userId, (err) => {
            if (err) {
                console.error('Error revoking course access:', err);
                return res.json({ success: false, error: 'Database error' });
            }
            res.json({ success: true });
        });
    }
});

// Global Settings
router.post('/settings/premium', (req, res) => {
    const enabled = req.body.enabled === true || req.body.enabled === 'true';

    console.log('Premium setting update:', { enabled, body: req.body });

    db.updateAdminSetting('global_premium_enabled', enabled.toString(), req.session.userId, (err) => {
        if (err) {
            console.error('Error updating premium setting:', err);
            return res.json({ success: false, error: 'Database error' });
        }

        res.json({ success: true });
    });
});

// Email Management
router.get('/emails', (req, res) => {
    res.render('admin/emails', {
        title: 'Email Management',
        username: req.session.username,
        user: req.user,
        currentLanguage: req.session.language || 'en',
        success: req.query.success,
        error: req.query.error
    });
});

router.post('/emails/send', async (req, res) => {
    const { fromEmail, toEmail, subject, message } = req.body;

    // Validate input
    if (!fromEmail || !toEmail || !subject || !message) {
        return res.redirect('/admin/emails?error=' + encodeURIComponent('All fields are required'));
    }

    // Validate that fromEmail is a @codewave.online address
    if (!fromEmail.endsWith('@codewave.online')) {
        return res.redirect('/admin/emails?error=' + encodeURIComponent('From email must be a @codewave.online address'));
    }

    // Basic email validation for toEmail
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(toEmail)) {
        return res.redirect('/admin/emails?error=' + encodeURIComponent('Invalid recipient email address'));
    }

    try {
        await emailService.sendAdminEmail(fromEmail, toEmail, subject, message);

        // Log the admin action
        db.logAdminAction(req.session.userId, 'SEND_EMAIL', null, null, `Sent email from ${fromEmail} to ${toEmail}`, () => {});

        res.redirect('/admin/emails?success=' + encodeURIComponent('Email sent successfully!'));
    } catch (error) {
        console.error('Error sending admin email:', error);
        res.redirect('/admin/emails?error=' + encodeURIComponent('Failed to send email. Please check your configuration and try again.'));
    }
});

// Audit Log
router.get('/audit', (req, res) => {
    const limit = parseInt(req.query.limit) || 100;

    db.getAdminAuditLog(limit, (err, auditLog) => {
        if (err) {
            console.error('Error fetching audit log:', err);
            return res.status(500).send('Internal server error');
        }

        res.render('admin/audit', {
            title: 'Audit Log',
            username: req.session.username,
            user: req.user,
            currentLanguage: req.session.language || 'en',
            auditLog
        });
    });
});

// Support Management
router.get('/support', (req, res) => {
    db.getAllSupportTickets((err, tickets) => {
        if (err) {
            console.error('Error fetching support tickets:', err);
            return res.status(500).send('Internal server error');
        }

        res.render('admin/support', {
            title: 'Support Management',
            username: req.session.username,
            user: req.user,
            currentLanguage: req.session.language || 'en',
            tickets,
            t: req.t
        });
    });
});

router.get('/support/:id', (req, res) => {
    const ticketId = parseInt(req.params.id);

    db.getSupportTicketById(ticketId, (err, ticket) => {
        if (err) {
            console.error('Error fetching ticket:', err);
            return res.status(500).send('Internal server error');
        }

        if (!ticket) {
            return res.status(404).send('Ticket not found');
        }

        db.getSupportMessages(ticketId, (err, messages) => {
            if (err) {
                console.error('Error fetching messages:', err);
                return res.status(500).send('Internal server error');
            }

            res.render('support/ticket-detail', {
                title: `Ticket #${ticketId}`,
                username: req.session.username,
                user: req.user,
                currentLanguage: req.session.language || 'en',
                ticket,
                messages,
                isStaff: true,
                t: req.t
            });
        });
    });
});

router.post('/support/:id/status', (req, res) => {
    const ticketId = parseInt(req.params.id);
    const status = req.body.status;

    if (!['open', 'pending', 'closed'].includes(status)) {
        return res.json({ success: false, error: 'Invalid status' });
    }

    db.updateSupportTicketStatus(ticketId, status, (err) => {
        if (err) {
            console.error('Error updating ticket status:', err);
            return res.json({ success: false, error: 'Database error' });
        }

        res.json({ success: true });
    });
});

module.exports = router;
