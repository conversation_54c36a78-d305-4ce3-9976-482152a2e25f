const express = require('express');
const router = express.Router();
const Database = require('../database/db');
const db = new Database();

// Middleware functions
function requireAuth(req, res, next) {
    if (!req.session.userId) {
        return res.redirect('/login');
    }
    next();
}

function loadUserObject(req, res, next) {
    if (!req.session.userId) {
        return next();
    }

    db.getUserById(req.session.userId, (err, user) => {
        if (err) {
            console.error('Error loading user:', err);
            return next();
        }
        req.user = user;
        next();
    });
}

// Apply auth middleware to all routes
router.use(requireAuth);
router.use(loadUserObject);

// Support ticket list
router.get('/', (req, res) => {
    const isStaff = req.user && req.user.is_admin;
    
    let sql, params;
    if (isStaff) {
        // Staff can see all tickets
        sql = `
            SELECT st.*, u.username, u.email,
                   COUNT(sm.id) as message_count,
                   MAX(sm.created_at) as last_message_at
            FROM support_tickets st
            JOIN users u ON st.user_id = u.id
            LEFT JOIN support_messages sm ON st.id = sm.ticket_id
            GROUP BY st.id
            ORDER BY st.updated_at DESC
        `;
        params = [];
    } else {
        // Users can only see their own tickets
        sql = `
            SELECT st.*, u.username, u.email,
                   COUNT(sm.id) as message_count,
                   MAX(sm.created_at) as last_message_at
            FROM support_tickets st
            JOIN users u ON st.user_id = u.id
            LEFT JOIN support_messages sm ON st.id = sm.ticket_id
            WHERE st.user_id = ?
            GROUP BY st.id
            ORDER BY st.updated_at DESC
        `;
        params = [req.session.userId];
    }
    
    db.db.all(sql, params, (err, tickets) => {
        if (err) {
            console.error('Error fetching tickets:', err);
            return res.status(500).send('Error fetching tickets');
        }
        
        res.render('support/tickets', {
            title: 'Support',
            username: req.session.username,
            user: req.user,
            tickets: tickets || [],
            isStaff: isStaff,
            currentLanguage: req.session.language || 'en'
        });
    });
});

// Create new ticket
router.get('/new', (req, res) => {
    res.render('support/new-ticket', {
        title: 'New Support Ticket',
        username: req.session.username,
        user: req.user,
        currentLanguage: req.session.language || 'en'
    });
});

// Create ticket POST
router.post('/new', (req, res) => {
    const { title, message } = req.body;
    
    if (!title || !message) {
        return res.status(400).send('Title and message are required');
    }
    
    // Create ticket
    const sql = `INSERT INTO support_tickets (user_id, title) VALUES (?, ?)`;
    db.db.run(sql, [req.session.userId, title], function(err) {
        if (err) {
            console.error('Error creating ticket:', err);
            return res.status(500).send('Error creating ticket');
        }
        
        const ticketId = this.lastID;
        
        // Add initial message
        const msgSql = `INSERT INTO support_messages (ticket_id, user_id, message, is_staff) VALUES (?, ?, ?, ?)`;
        db.db.run(msgSql, [ticketId, req.session.userId, message, 0], (err) => {
            if (err) {
                console.error('Error creating message:', err);
                return res.status(500).send('Error creating message');
            }
            
            res.redirect(`/support/${ticketId}`);
        });
    });
});

// View specific ticket
router.get('/:id', (req, res) => {
    const ticketId = parseInt(req.params.id);
    const isStaff = req.user && req.user.is_admin;
    
    // Get ticket details
    let ticketSql, ticketParams;
    if (isStaff) {
        ticketSql = `
            SELECT st.*, u.username, u.email
            FROM support_tickets st
            JOIN users u ON st.user_id = u.id
            WHERE st.id = ?
        `;
        ticketParams = [ticketId];
    } else {
        ticketSql = `
            SELECT st.*, u.username, u.email
            FROM support_tickets st
            JOIN users u ON st.user_id = u.id
            WHERE st.id = ? AND st.user_id = ?
        `;
        ticketParams = [ticketId, req.session.userId];
    }
    
    db.db.get(ticketSql, ticketParams, (err, ticket) => {
        if (err) {
            console.error('Error fetching ticket:', err);
            return res.status(500).send('Error fetching ticket');
        }
        
        if (!ticket) {
            return res.status(404).send('Ticket not found');
        }
        
        // Get messages
        const msgSql = `
            SELECT sm.*, u.username
            FROM support_messages sm
            JOIN users u ON sm.user_id = u.id
            WHERE sm.ticket_id = ?
            ORDER BY sm.created_at ASC
        `;
        
        db.db.all(msgSql, [ticketId], (err, messages) => {
            if (err) {
                console.error('Error fetching messages:', err);
                return res.status(500).send('Error fetching messages');
            }
            
            res.render('support/ticket-detail', {
                title: `Support Ticket #${ticketId}`,
                username: req.session.username,
                user: req.user,
                ticket: ticket,
                messages: messages || [],
                isStaff: isStaff,
                currentLanguage: req.session.language || 'en'
            });
        });
    });
});

// Add message to ticket
router.post('/:id/message', (req, res) => {
    const ticketId = parseInt(req.params.id);
    const { message } = req.body;
    const isStaff = req.user && req.user.is_admin;
    
    if (!message) {
        return res.status(400).json({ success: false, error: 'Message is required' });
    }
    
    // Verify access to ticket
    let checkSql, checkParams;
    if (isStaff) {
        checkSql = `SELECT id FROM support_tickets WHERE id = ?`;
        checkParams = [ticketId];
    } else {
        checkSql = `SELECT id FROM support_tickets WHERE id = ? AND user_id = ?`;
        checkParams = [ticketId, req.session.userId];
    }
    
    db.db.get(checkSql, checkParams, (err, ticket) => {
        if (err || !ticket) {
            return res.status(404).json({ success: false, error: 'Ticket not found' });
        }
        
        // Add message
        const msgSql = `INSERT INTO support_messages (ticket_id, user_id, message, is_staff) VALUES (?, ?, ?, ?)`;
        db.db.run(msgSql, [ticketId, req.session.userId, message, isStaff ? 1 : 0], (err) => {
            if (err) {
                console.error('Error adding message:', err);
                return res.status(500).json({ success: false, error: 'Error adding message' });
            }
            
            // Update ticket timestamp
            const updateSql = `UPDATE support_tickets SET updated_at = CURRENT_TIMESTAMP WHERE id = ?`;
            db.db.run(updateSql, [ticketId], (err) => {
                if (err) {
                    console.error('Error updating ticket:', err);
                }
                
                res.json({ success: true });
            });
        });
    });
});

// Update ticket status (staff only)
router.post('/:id/status', (req, res) => {
    const ticketId = parseInt(req.params.id);
    const { status } = req.body;
    const isStaff = req.user && req.user.is_admin;
    
    if (!isStaff) {
        return res.status(403).json({ success: false, error: 'Access denied' });
    }
    
    if (!['open', 'closed', 'pending'].includes(status)) {
        return res.status(400).json({ success: false, error: 'Invalid status' });
    }
    
    const sql = `UPDATE support_tickets SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?`;
    db.db.run(sql, [status, ticketId], (err) => {
        if (err) {
            console.error('Error updating ticket status:', err);
            return res.status(500).json({ success: false, error: 'Error updating status' });
        }
        
        res.json({ success: true });
    });
});

module.exports = router;
