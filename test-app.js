// Simple test script for the CodeLearning platform
const Database = require('./database/db');

async function runTests() {
    console.log('🧪 Starting CodeLearning Platform Tests...\n');
    
    const db = new Database();
    
    try {
        // Test 1: Check if courses exist
        console.log('📚 Test 1: Checking courses...');
        await new Promise((resolve, reject) => {
            db.getAllCourses((err, courses) => {
                if (err) {
                    reject(err);
                    return;
                }
                
                if (courses.length >= 2) {
                    console.log('✅ Found', courses.length, 'courses');
                    courses.forEach(course => {
                        console.log(`   - ${course.name} (${course.slug})`);
                    });
                } else {
                    console.log('❌ Expected at least 2 courses, found', courses.length);
                }
                resolve();
            });
        });
        
        // Test 2: Check if levels exist for HTML/CSS/JS course
        console.log('\n📖 Test 2: Checking HTML/CSS/JS levels...');
        await new Promise((resolve, reject) => {
            db.getCourseBySlug('html-css-js', (err, course) => {
                if (err || !course) {
                    reject(new Error('HTML/CSS/JS course not found'));
                    return;
                }
                
                db.getLevelsByCourse(course.id, (err, levels) => {
                    if (err) {
                        reject(err);
                        return;
                    }
                    
                    if (levels.length >= 5) {
                        console.log('✅ Found', levels.length, 'levels for HTML/CSS/JS');
                        levels.slice(0, 3).forEach(level => {
                            console.log(`   - Level ${level.level_number}: ${level.title}`);
                        });
                        if (levels.length > 3) {
                            console.log(`   - ... and ${levels.length - 3} more levels`);
                        }
                    } else {
                        console.log('❌ Expected at least 5 levels, found', levels.length);
                    }
                    resolve();
                });
            });
        });
        
        // Test 3: Check if levels exist for JavaScript Advanced course
        console.log('\n🚀 Test 3: Checking JavaScript Advanced levels...');
        await new Promise((resolve, reject) => {
            db.getCourseBySlug('javascript-advanced', (err, course) => {
                if (err || !course) {
                    reject(new Error('JavaScript Advanced course not found'));
                    return;
                }
                
                db.getLevelsByCourse(course.id, (err, levels) => {
                    if (err) {
                        reject(err);
                        return;
                    }
                    
                    if (levels.length >= 3) {
                        console.log('✅ Found', levels.length, 'levels for JavaScript Advanced');
                        levels.slice(0, 3).forEach(level => {
                            console.log(`   - Level ${level.level_number}: ${level.title}`);
                        });
                        if (levels.length > 3) {
                            console.log(`   - ... and ${levels.length - 3} more levels`);
                        }
                    } else {
                        console.log('❌ Expected at least 3 levels, found', levels.length);
                    }
                    resolve();
                });
            });
        });
        
        // Test 4: Test user creation (simulation)
        console.log('\n👤 Test 4: Testing user operations...');
        const testEmail = '<EMAIL>';
        const testUsername = 'testuser';
        
        // Check if test user already exists
        await new Promise((resolve, reject) => {
            db.getUserByEmail(testEmail, (err, existingUser) => {
                if (err) {
                    reject(err);
                    return;
                }
                
                if (existingUser) {
                    console.log('✅ Test user already exists, skipping creation');
                } else {
                    console.log('✅ Test user does not exist (good for testing)');
                }
                resolve();
            });
        });
        
        // Test 5: Test level unlocking logic
        console.log('\n🔓 Test 5: Testing level unlocking logic...');
        await new Promise((resolve, reject) => {
            db.getCourseBySlug('html-css-js', (err, course) => {
                if (err || !course) {
                    reject(new Error('Course not found'));
                    return;
                }
                
                // Test that level 1 is always unlocked
                db.isLevelUnlocked(999, course.id, 1, (err, isUnlocked) => {
                    if (err) {
                        reject(err);
                        return;
                    }
                    
                    if (isUnlocked) {
                        console.log('✅ Level 1 is unlocked for new users');
                    } else {
                        console.log('❌ Level 1 should be unlocked for new users');
                    }
                    
                    // Test that level 2 is locked for new users
                    db.isLevelUnlocked(999, course.id, 2, (err, isUnlocked) => {
                        if (err) {
                            reject(err);
                            return;
                        }
                        
                        if (!isUnlocked) {
                            console.log('✅ Level 2 is locked for new users');
                        } else {
                            console.log('❌ Level 2 should be locked for new users');
                        }
                        resolve();
                    });
                });
            });
        });
        
        console.log('\n🎉 All tests completed successfully!');
        console.log('\n📋 Summary:');
        console.log('- Database connection: ✅ Working');
        console.log('- Courses: ✅ Loaded');
        console.log('- Levels: ✅ Loaded');
        console.log('- User system: ✅ Ready');
        console.log('- Level progression: ✅ Working');
        
        console.log('\n🚀 Your CodeLearning platform is ready to use!');
        console.log('   Visit http://localhost:3000 to start learning!');
        
    } catch (error) {
        console.error('\n❌ Test failed:', error.message);
        console.error('   Please check your database setup and try again.');
    } finally {
        db.close();
    }
}

// Run tests if called directly
if (require.main === module) {
    runTests();
}

module.exports = { runTests };
