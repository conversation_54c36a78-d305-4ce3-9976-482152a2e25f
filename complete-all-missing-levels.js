const sqlite3 = require('sqlite3').verbose();
const { dbPath } = require('./database/init');

console.log('🚀 Creating ALL Missing Levels for ALL Courses...');

const directDb = new sqlite3.Database(dbPath);

directDb.serialize(() => {
    const levelStmt = directDb.prepare(`INSERT INTO levels (course_id, level_number, title, description, content, exercise_type, expected_output, points) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`);
    
    // JavaScript+PHP Course (Course ID: 2) - Level 10-40
    console.log('⚡ Creating JavaScript+PHP levels 10-40...');
    
    const jsPhpLevels = [
        // Level 10: Boss Level
        {
            course_id: 2, level_number: 10, title: '🏆 BOSS: JavaScript Calculator', 
            description: 'Erstelle einen funktionsfähigen Taschenrechner mit JavaScript.',
            content: `# 🏆 BOSS LEVEL: JavaScript Calculator

Erst<PERSON> einen vollständigen Taschenrechner mit JavaScript!

## Anforderungen:
- ✅ HTML-Interface mit Buttons
- ✅ JavaScript für alle Berechnungen
- ✅ Grundrechenarten (+, -, *, /)
- ✅ Clear-Funktion
- ✅ Display für Eingabe und Ergebnis
- ✅ Error Handling für Division durch 0

## Beispiel-Struktur:
\`\`\`html
<div class="calculator">
    <input type="text" id="display" readonly>
    <div class="buttons">
        <button onclick="clearDisplay()">C</button>
        <button onclick="appendToDisplay('/')">/</button>
        <button onclick="appendToDisplay('*')">*</button>
        <button onclick="deleteLast()">←</button>
        <!-- Weitere Buttons... -->
    </div>
</div>
\`\`\`

Zeige deine JavaScript-Kenntnisse! 🚀`,
            exercise_type: 'project',
            expected_output: 'js_calculator',
            points: 50
        },
        // Level 11-19: PHP Grundlagen
        {
            course_id: 2, level_number: 11, title: 'PHP Grundlagen', 
            description: 'Lerne die Basics von PHP - der Server-Sprache des Webs.',
            content: `# PHP Grundlagen

PHP läuft auf dem Server und generiert HTML:

\`\`\`php
<?php
// PHP-Code beginnt mit <?php

// Variablen beginnen mit $
$name = "Max";
$alter = 25;
$istStudent = true;

// Ausgabe mit echo
echo "Hallo " . $name . "!<br>";
echo "Du bist " . $alter . " Jahre alt.<br>";

// HTML und PHP mischen
?>
<h1>Willkommen <?php echo $name; ?>!</h1>
<p>Heute ist: <?php echo date('d.m.Y'); ?></p>

<?php
// Arrays
$fruechte = array("Apfel", "Banane", "Orange");
$fruechte[] = "Erdbeere"; // Hinzufügen

// Ausgabe von Arrays
foreach($fruechte as $frucht) {
    echo "<li>" . $frucht . "</li>";
}

// Assoziative Arrays
$person = array(
    "name" => "Anna",
    "alter" => 30,
    "stadt" => "Berlin"
);

echo $person["name"]; // Anna
?>
\`\`\`

## Aufgabe
Erstelle eine PHP-Seite, die deinen Namen und das aktuelle Datum ausgibt.`,
            exercise_type: 'code_example',
            expected_output: 'php_basics',
            points: 10
        },
        {
            course_id: 2, level_number: 12, title: 'PHP Funktionen', 
            description: 'Erstelle wiederverwendbare PHP-Funktionen.',
            content: `# PHP Funktionen

Funktionen organisieren deinen PHP-Code:

\`\`\`php
<?php
// Einfache Funktion
function begruessung($name) {
    return "Hallo " . $name . "!";
}

// Funktion mit mehreren Parametern
function addieren($a, $b) {
    return $a + $b;
}

// Funktion mit Default-Parameter
function vorstellen($name, $alter = 0) {
    if ($alter > 0) {
        return "Ich bin " . $name . " und " . $alter . " Jahre alt.";
    } else {
        return "Ich bin " . $name . ".";
    }
}

// Funktionen aufrufen
echo begruessung("Max") . "<br>";
echo "5 + 3 = " . addieren(5, 3) . "<br>";
echo vorstellen("Anna", 25) . "<br>";
echo vorstellen("Tom") . "<br>";

// Funktion für HTML-Ausgabe
function erstelleButton($text, $farbe = "blue") {
    return '<button style="background-color: ' . $farbe . '; color: white; padding: 10px;">' . $text . '</button>';
}

echo erstelleButton("Klick mich!", "red");
echo erstelleButton("Normaler Button");

// Array-Funktionen
function arrayZuHtml($array) {
    $html = "<ul>";
    foreach($array as $item) {
        $html .= "<li>" . $item . "</li>";
    }
    $html .= "</ul>";
    return $html;
}

$hobbys = array("Lesen", "Sport", "Musik");
echo arrayZuHtml($hobbys);
?>
\`\`\`

## Aufgabe
Erstelle eine Funktion, die zwei Zahlen multipliziert und das Ergebnis formatiert ausgibt.`,
            exercise_type: 'code_example',
            expected_output: 'php_functions',
            points: 10
        },
        {
            course_id: 2, level_number: 13, title: 'PHP Formulare', 
            description: 'Verarbeite HTML-Formulare mit PHP.',
            content: `# PHP Formulare

PHP kann Formulardaten verarbeiten:

\`\`\`html
<!-- HTML-Formular -->
<form method="POST" action="">
    <label for="name">Name:</label>
    <input type="text" id="name" name="name" required>
    
    <label for="email">E-Mail:</label>
    <input type="email" id="email" name="email" required>
    
    <label for="nachricht">Nachricht:</label>
    <textarea id="nachricht" name="nachricht" rows="4"></textarea>
    
    <button type="submit" name="submit">Senden</button>
</form>
\`\`\`

\`\`\`php
<?php
// PHP-Verarbeitung
if (isset($_POST['submit'])) {
    $name = $_POST['name'];
    $email = $_POST['email'];
    $nachricht = $_POST['nachricht'];
    
    // Validierung
    if (empty($name) || empty($email)) {
        echo "<p style='color: red;'>Bitte alle Pflichtfelder ausfüllen!</p>";
    } else {
        // Daten verarbeiten
        echo "<div style='background: lightgreen; padding: 10px;'>";
        echo "<h3>Daten empfangen:</h3>";
        echo "<p><strong>Name:</strong> " . htmlspecialchars($name) . "</p>";
        echo "<p><strong>E-Mail:</strong> " . htmlspecialchars($email) . "</p>";
        echo "<p><strong>Nachricht:</strong> " . nl2br(htmlspecialchars($nachricht)) . "</p>";
        echo "</div>";
        
        // Hier könnte man die Daten in Datenbank speichern
        // oder E-Mail versenden
    }
}

// GET-Parameter verarbeiten
if (isset($_GET['page'])) {
    $page = $_GET['page'];
    echo "<p>Aktuelle Seite: " . htmlspecialchars($page) . "</p>";
}

// Sichere Datenverarbeitung
function sichereDaten($data) {
    $data = trim($data);           // Leerzeichen entfernen
    $data = stripslashes($data);   // Backslashes entfernen
    $data = htmlspecialchars($data); // HTML-Zeichen escapen
    return $data;
}
?>
\`\`\`

## Aufgabe
Erstelle ein Kontaktformular, das die eingegebenen Daten sicher verarbeitet und anzeigt.`,
            exercise_type: 'code_example',
            expected_output: 'php_forms',
            points: 10
        },
        {
            course_id: 2, level_number: 14, title: 'PHP Sessions & Cookies', 
            description: 'Verwalte Benutzerdaten mit Sessions und Cookies.',
            content: `# PHP Sessions & Cookies

Speichere Benutzerdaten zwischen Seitenaufrufen:

\`\`\`php
<?php
// Session starten (immer am Anfang der Seite!)
session_start();

// Session-Daten setzen
if (isset($_POST['login'])) {
    $username = $_POST['username'];
    $_SESSION['username'] = $username;
    $_SESSION['login_time'] = date('Y-m-d H:i:s');
    echo "<p style='color: green;'>Erfolgreich eingeloggt als " . $username . "!</p>";
}

// Session-Daten lesen
if (isset($_SESSION['username'])) {
    echo "<h2>Willkommen zurück, " . $_SESSION['username'] . "!</h2>";
    echo "<p>Eingeloggt seit: " . $_SESSION['login_time'] . "</p>";
    echo '<a href="?logout=1">Ausloggen</a>';
} else {
    // Login-Formular anzeigen
    echo '
    <form method="POST">
        <input type="text" name="username" placeholder="Benutzername" required>
        <button type="submit" name="login">Einloggen</button>
    </form>';
}

// Logout
if (isset($_GET['logout'])) {
    session_destroy();
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

// Cookies setzen
if (isset($_POST['save_preference'])) {
    $theme = $_POST['theme'];
    setcookie('user_theme', $theme, time() + (86400 * 30)); // 30 Tage
    echo "<p>Theme gespeichert: " . $theme . "</p>";
}

// Cookies lesen
if (isset($_COOKIE['user_theme'])) {
    $theme = $_COOKIE['user_theme'];
    echo "<p>Dein gespeichertes Theme: " . $theme . "</p>";
    
    // Theme anwenden
    if ($theme == 'dark') {
        echo '<style>body { background: #333; color: white; }</style>';
    }
}

// Warenkorb-Beispiel mit Sessions
if (!isset($_SESSION['warenkorb'])) {
    $_SESSION['warenkorb'] = array();
}

if (isset($_POST['add_item'])) {
    $item = $_POST['item'];
    $_SESSION['warenkorb'][] = $item;
}

echo "<h3>Warenkorb:</h3>";
if (empty($_SESSION['warenkorb'])) {
    echo "<p>Warenkorb ist leer</p>";
} else {
    echo "<ul>";
    foreach ($_SESSION['warenkorb'] as $item) {
        echo "<li>" . htmlspecialchars($item) . "</li>";
    }
    echo "</ul>";
}
?>

<form method="POST">
    <input type="text" name="item" placeholder="Artikel hinzufügen">
    <button type="submit" name="add_item">Hinzufügen</button>
</form>

<form method="POST">
    <select name="theme">
        <option value="light">Hell</option>
        <option value="dark">Dunkel</option>
    </select>
    <button type="submit" name="save_preference">Theme speichern</button>
</form>
\`\`\`

## Aufgabe
Erstelle ein einfaches Login-System mit Sessions und einen Warenkorb.`,
            exercise_type: 'code_example',
            expected_output: 'php_sessions',
            points: 10
        },
        {
            course_id: 2, level_number: 15, title: 'PHP Datei-Operationen', 
            description: 'Arbeite mit Dateien in PHP - Lesen, Schreiben, Upload.',
            content: `# PHP Datei-Operationen

PHP kann Dateien lesen, schreiben und verwalten:

\`\`\`php
<?php
// Datei schreiben
$filename = "daten.txt";
$content = "Hallo Welt!\nDas ist eine neue Zeile.\n";

// Datei erstellen/überschreiben
file_put_contents($filename, $content);
echo "<p>Datei erstellt: " . $filename . "</p>";

// An Datei anhängen
$neuer_inhalt = "Weitere Zeile hinzugefügt: " . date('Y-m-d H:i:s') . "\n";
file_put_contents($filename, $neuer_inhalt, FILE_APPEND);

// Datei lesen
if (file_exists($filename)) {
    $inhalt = file_get_contents($filename);
    echo "<h3>Dateiinhalt:</h3>";
    echo "<pre>" . htmlspecialchars($inhalt) . "</pre>";
    
    // Datei zeilenweise lesen
    $zeilen = file($filename);
    echo "<h3>Zeilenweise:</h3>";
    foreach ($zeilen as $nummer => $zeile) {
        echo "Zeile " . ($nummer + 1) . ": " . htmlspecialchars($zeile) . "<br>";
    }
}

// Datei-Upload verarbeiten
if (isset($_POST['upload'])) {
    $upload_dir = "uploads/";
    
    // Upload-Verzeichnis erstellen falls nicht vorhanden
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0777, true);
    }
    
    if (isset($_FILES['datei']) && $_FILES['datei']['error'] == 0) {
        $dateiname = $_FILES['datei']['name'];
        $temp_datei = $_FILES['datei']['tmp_name'];
        $dateigröße = $_FILES['datei']['size'];
        $datei_typ = $_FILES['datei']['type'];
        
        // Sicherheitsprüfungen
        $erlaubte_typen = array('image/jpeg', 'image/png', 'image/gif', 'text/plain');
        $max_größe = 2 * 1024 * 1024; // 2MB
        
        if (in_array($datei_typ, $erlaubte_typen) && $dateigröße <= $max_größe) {
            $ziel_datei = $upload_dir . basename($dateiname);
            
            if (move_uploaded_file($temp_datei, $ziel_datei)) {
                echo "<p style='color: green;'>Datei erfolgreich hochgeladen: " . $dateiname . "</p>";
                echo "<p>Größe: " . round($dateigröße / 1024, 2) . " KB</p>";
                
                // Bild anzeigen falls es ein Bild ist
                if (strpos($datei_typ, 'image') !== false) {
                    echo "<img src='" . $ziel_datei . "' style='max-width: 300px;'>";
                }
            } else {
                echo "<p style='color: red;'>Fehler beim Upload!</p>";
            }
        } else {
            echo "<p style='color: red;'>Datei nicht erlaubt oder zu groß!</p>";
        }
    }
}

// Verzeichnis-Inhalt anzeigen
$upload_dir = "uploads/";
if (is_dir($upload_dir)) {
    $dateien = scandir($upload_dir);
    echo "<h3>Hochgeladene Dateien:</h3>";
    echo "<ul>";
    foreach ($dateien as $datei) {
        if ($datei != "." && $datei != "..") {
            $dateipfad = $upload_dir . $datei;
            $größe = filesize($dateipfad);
            echo "<li>" . $datei . " (" . round($größe / 1024, 2) . " KB)</li>";
        }
    }
    echo "</ul>";
}

// CSV-Datei verarbeiten
$csv_data = array(
    array("Name", "Alter", "Stadt"),
    array("Max", "25", "Berlin"),
    array("Anna", "30", "München"),
    array("Tom", "22", "Hamburg")
);

$csv_file = "personen.csv";
$handle = fopen($csv_file, 'w');
foreach ($csv_data as $zeile) {
    fputcsv($handle, $zeile);
}
fclose($handle);

echo "<h3>CSV-Datei erstellt: " . $csv_file . "</h3>";

// CSV lesen
if (($handle = fopen($csv_file, "r")) !== FALSE) {
    echo "<table border='1'>";
    while (($data = fgetcsv($handle)) !== FALSE) {
        echo "<tr>";
        foreach ($data as $zelle) {
            echo "<td>" . htmlspecialchars($zelle) . "</td>";
        }
        echo "</tr>";
    }
    echo "</table>";
    fclose($handle);
}
?>

<!-- Upload-Formular -->
<h3>Datei hochladen:</h3>
<form method="POST" enctype="multipart/form-data">
    <input type="file" name="datei" accept="image/*,.txt" required>
    <button type="submit" name="upload">Hochladen</button>
</form>
\`\`\`

## Aufgabe
Erstelle ein Datei-Upload-System, das Bilder hochlädt und anzeigt.`,
            exercise_type: 'code_example',
            expected_output: 'php_files',
            points: 10
        }
    ];
    
    // Insert JS+PHP levels 10-15
    for (const level of jsPhpLevels) {
        levelStmt.run(level.course_id, level.level_number, level.title, level.description, level.content, level.exercise_type, level.expected_output, level.points);
    }
    
    console.log('✅ JavaScript+PHP levels 10-15 created!');
    
    levelStmt.finalize();
    directDb.close();
    
    console.log('🎉 First batch of missing levels created!');
    console.log('📝 Run the next script to continue with more levels...');
});
