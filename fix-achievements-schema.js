const sqlite3 = require('sqlite3').verbose();
const { dbPath } = require('./database/init');

console.log('🔧 Fixing Achievements Schema...');

const directDb = new sqlite3.Database(dbPath);

directDb.serialize(() => {
    // Check current achievements table structure
    directDb.all("PRAGMA table_info(achievements)", (err, columns) => {
        if (err) {
            console.error('Error checking table:', err);
            return;
        }
        
        console.log('Current achievements table columns:');
        columns.forEach(col => {
            console.log(`  ${col.name}: ${col.type}`);
        });
        
        // Check if points_required column exists
        const hasPointsRequired = columns.some(col => col.name === 'points_required');
        const hasBadgeColor = columns.some(col => col.name === 'badge_color');
        
        if (!hasPointsRequired) {
            console.log('Adding points_required column...');
            directDb.run('ALTER TABLE achievements ADD COLUMN points_required INTEGER DEFAULT 0');
        }
        
        if (!hasBadgeColor) {
            console.log('Adding badge_color column...');
            directDb.run('ALTER TABLE achievements ADD COLUMN badge_color VARCHAR(7) DEFAULT "#28a745"');
        }
        
        // Clear existing achievements and recreate with correct structure
        directDb.run('DELETE FROM achievements', (err) => {
            if (err) {
                console.error('Error clearing achievements:', err);
                return;
            }
            
            console.log('✅ Achievements table cleared');
            
            const achievementStmt = directDb.prepare(`INSERT INTO achievements (name, description, icon, points_required, badge_color) VALUES (?, ?, ?, ?, ?)`);
            
            // Simplified but comprehensive achievements
            const achievements = [
                // Beginner Level Achievements
                {name: 'Erste Schritte', description: 'Schließe dein erstes Level ab', icon: '🚀', points_required: 10, badge_color: '#28a745'},
                {name: 'HTML Grundlagen', description: 'Meistere die HTML-Grundstruktur', icon: '🌐', points_required: 50, badge_color: '#e34c26'},
                {name: 'CSS Stylist', description: 'Verwende CSS zum ersten Mal', icon: '🎨', points_required: 80, badge_color: '#1572b6'},
                {name: 'Boss Killer', description: 'Besiege dein erstes Boss Level', icon: '⚔️', points_required: 100, badge_color: '#ffc107'},
                
                // Intermediate Achievements
                {name: 'JavaScript Ninja', description: 'Schreibe dein erstes JavaScript', icon: '⚡', points_required: 150, badge_color: '#f7df1e'},
                {name: 'DOM Manipulator', description: 'Verändere HTML mit JavaScript', icon: '🔧', points_required: 200, badge_color: '#17a2b8'},
                {name: 'Event Handler', description: 'Reagiere auf Benutzerinteraktionen', icon: '👆', points_required: 250, badge_color: '#6f42c1'},
                {name: 'Doppel Boss', description: 'Besiege 2 Boss Level', icon: '🏆', points_required: 300, badge_color: '#fd7e14'},
                
                // Advanced Achievements
                {name: 'Responsive Designer', description: 'Erstelle responsive Layouts', icon: '📱', points_required: 400, badge_color: '#20c997'},
                {name: 'API Master', description: 'Verwende APIs in deinen Projekten', icon: '🔗', points_required: 500, badge_color: '#6610f2'},
                {name: 'Triple Boss', description: 'Besiege 3 Boss Level', icon: '👑', points_required: 600, badge_color: '#e83e8c'},
                {name: 'Final Boss Slayer', description: 'Besiege ein Final Boss Level', icon: '🐉', points_required: 1500, badge_color: '#6f42c1'},
                
                // Course Completion Achievements
                {name: 'HTML/CSS/JS Meister', description: 'Schließe den HTML/CSS/JS Kurs ab', icon: '🌐', points_required: 2000, badge_color: '#e34c26'},
                {name: 'PHP Entwickler', description: 'Schließe den JavaScript+PHP Kurs ab', icon: '🐘', points_required: 2000, badge_color: '#777bb4'},
                {name: 'Go Gopher', description: 'Schließe den Go Kurs ab', icon: '🦫', points_required: 2000, badge_color: '#00add8'},
                {name: 'Python Schlangen-Beschwörer', description: 'Schließe den Python Kurs ab', icon: '🐍', points_required: 2000, badge_color: '#3776ab'},
                {name: 'Java Barista', description: 'Schließe den Java Kurs ab', icon: '☕', points_required: 2000, badge_color: '#ed8b00'},
                
                // Special Achievements
                {name: 'Polyglott', description: 'Lerne 3 verschiedene Programmiersprachen', icon: '🌍', points_required: 3000, badge_color: '#17a2b8'},
                {name: 'Code Warrior', description: 'Sammle 5000 Punkte', icon: '⚔️', points_required: 5000, badge_color: '#dc3545'},
                {name: 'Legende', description: 'Sammle 10000 Punkte', icon: '🏆', points_required: 10000, badge_color: '#ffd700'},
                
                // Boss Level Specific Achievements
                {name: 'Visitenkarten-Designer', description: 'Erstelle deine erste Website (Level 10)', icon: '💼', points_required: 100, badge_color: '#17a2b8'},
                {name: 'To-Do Meister', description: 'Baue eine funktionierende To-Do App (Level 20)', icon: '✅', points_required: 300, badge_color: '#28a745'},
                {name: 'Quiz Master', description: 'Erstelle eine interaktive Quiz-App (Level 30)', icon: '❓', points_required: 600, badge_color: '#ffc107'},
                {name: 'Portfolio Künstler', description: 'Baue dein ultimatives Portfolio (Level 40)', icon: '🎨', points_required: 1500, badge_color: '#6f42c1'},
                
                // Milestone Achievements
                {name: 'Hundert Punkte', description: 'Sammle deine ersten 100 Punkte', icon: '💯', points_required: 100, badge_color: '#28a745'},
                {name: 'Fünfhundert Punkte', description: 'Sammle 500 Punkte', icon: '🎯', points_required: 500, badge_color: '#17a2b8'},
                {name: 'Tausend Punkte', description: 'Sammle 1000 Punkte', icon: '🚀', points_required: 1000, badge_color: '#ffc107'},
                {name: 'Zweitausend Punkte', description: 'Sammle 2000 Punkte', icon: '⭐', points_required: 2000, badge_color: '#fd7e14'},
                {name: 'Fünftausend Punkte', description: 'Sammle 5000 Punkte', icon: '🌟', points_required: 5000, badge_color: '#e83e8c'},
                {name: 'Zehntausend Punkte', description: 'Sammle 10000 Punkte - Du bist eine Legende!', icon: '👑', points_required: 10000, badge_color: '#ffd700'},
                
                // Performance Achievements
                {name: 'Perfektionist', description: 'Erreiche 100% in 10 Leveln', icon: '💯', points_required: 500, badge_color: '#28a745'},
                {name: 'Schnellschreiber', description: 'Schließe ein Level in unter 5 Minuten ab', icon: '⚡', points_required: 100, badge_color: '#ffc107'},
                {name: 'Ausdauer-Champion', description: 'Lerne 2 Stunden am Stück', icon: '💪', points_required: 300, badge_color: '#dc3545'}
            ];
            
            // Insert all achievements
            for (const achievement of achievements) {
                achievementStmt.run(achievement.name, achievement.description, achievement.icon, achievement.points_required, achievement.badge_color);
            }
            
            console.log(`✅ ${achievements.length} achievements created successfully!`);
            
            achievementStmt.finalize();
            directDb.close();
            
            console.log('🎉 Achievements schema fixed and data inserted!');
        });
    });
});
