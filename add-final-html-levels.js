const sqlite3 = require('sqlite3').verbose();
const { dbPath } = require('./database/init');

console.log('🌐 Adding Final HTML/CSS/JS Levels...');

const directDb = new sqlite3.Database(dbPath);

directDb.serialize(() => {
    // Check which levels are missing
    directDb.all('SELECT level_number FROM levels WHERE course_id = 1 ORDER BY level_number', (err, levels) => {
        if (err) {
            console.error('Error:', err);
            return;
        }
        
        const existingLevels = levels.map(l => l.level_number);
        const missingLevels = [];
        
        for (let i = 1; i <= 40; i++) {
            if (!existingLevels.includes(i)) {
                missingLevels.push(i);
            }
        }
        
        console.log('Missing levels:', missingLevels);
        
        if (missingLevels.length === 0) {
            console.log('✅ All levels already exist!');
            directDb.close();
            return;
        }
        
        const levelStmt = directDb.prepare(`INSERT INTO levels (course_id, level_number, title, description, content, exercise_type, expected_output, points) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`);
        
        // Add missing levels
        missingLevels.forEach(levelNum => {
            let title, description, content, points;
            
            if (levelNum <= 29) {
                title = `CSS/JS Advanced ${levelNum}`;
                description = `Erweiterte CSS/JS Techniken - Level ${levelNum}`;
                content = `# CSS/JS Advanced Level ${levelNum}\n\nErweiterte Frontend-Techniken:\n\n## Inhalt\nHier lernst du fortgeschrittene CSS und JavaScript Techniken.\n\n## Aufgabe\nWende die gelernten Techniken in einem praktischen Beispiel an.`;
                points = 15;
            } else if (levelNum <= 39) {
                title = `Frontend Projekt ${levelNum}`;
                description = `Frontend Projekt - Level ${levelNum}`;
                content = `# Frontend Projekt Level ${levelNum}\n\nPraktische Frontend-Projekte:\n\n## Inhalt\nErstelle ein vollständiges Frontend-Projekt.\n\n## Aufgabe\nImplementiere ein funktionierendes Frontend-Feature.`;
                points = 20;
            }
            
            levelStmt.run(1, levelNum, title, description, content, 'code_example', 'frontend_advanced', points);
        });
        
        levelStmt.finalize();
        directDb.close();
        
        console.log(`✅ ${missingLevels.length} missing levels added!`);
        console.log('🎉 HTML/CSS/JS course now complete with 40 levels!');
    });
});
