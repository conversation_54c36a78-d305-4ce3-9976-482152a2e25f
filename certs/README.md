# SSL/TLS Certificates

This directory should contain your SSL/TLS certificates for HTTPS support.

## Required Files

- `private-key.pem` - Your private key file
- `certificate.pem` - Your certificate file (can be self-signed for development)

## Generating Self-Signed Certificates for Development

You can generate self-signed certificates for local development using OpenSSL:

```bash
# Generate private key
openssl genrsa -out private-key.pem 2048

# Generate certificate signing request
openssl req -new -key private-key.pem -out csr.pem

# Generate self-signed certificate (valid for 365 days)
openssl x509 -req -days 365 -in csr.pem -signkey private-key.pem -out certificate.pem

# Clean up CSR file
rm csr.pem
```

## For Production

For production environments, you should use certificates from a trusted Certificate Authority (CA) such as:

- Let's Encrypt (free)
- DigiCert
- Comodo
- GoDaddy
- etc.

## Security Notes

- Never commit your private key to version control
- Keep your certificates up to date
- Use strong encryption (2048-bit RSA minimum)
- Consider using ECDSA certificates for better performance

## File Permissions

Make sure your certificate files have appropriate permissions:

```bash
chmod 600 private-key.pem
chmod 644 certificate.pem
```
