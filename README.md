# CodeLearning - Interaktive Programmier-Lernplattform

Eine vollständige Lernplattform für Webentwicklung mit interaktiven Übungen, Level-System und Fortschritt-Tracking.

## 🚀 Features

- **Benutzerauthentifizierung**: Registrierung, Login und Session-Management
- **Email-Verifikation**: Bestätigung der Email-Adresse bei der Registrierung
- **Passwort-Reset**: Sichere Passwort-Zurücksetzung über Email
- **Level-basiertes Lernen**: 20 Level pro Kurs mit progressivem Schwierigkeitsgrad
- **Multi-Language Code-Ausführung**: HTML, CSS und JavaScript mit Live-Preview
- **Intelligente Code-Validierung**: Level-spezifische Validierung mit hilfreichen Feedback
- **Interaktive Übungen**: Detaillierte Aufgaben mit Schritt-für-Schritt Anleitungen
- **Benutzerprofile**: Vollständige Profile mit Statistiken und Erfolgen
- **Achievement-System**: Freischaltbare Erfolge basierend auf Fortschritt
- **Fortschritt-Tracking**: Echtzeit-Fortschrittsanzeige und Punktesystem
- **Admin-Panel**: Erweiterte Verwaltung mit Email-Versand-Funktionen
- **Email-Management**: Versand von Emails über @codewave.online Adressen
- **Responsive Design**: Funktioniert perfekt auf Desktop und Mobile

## 📚 Verfügbare Kurse

### 1. HTML, CSS & JavaScript Grundlagen (10 Level)
- HTML-Grundstruktur und Tags
- CSS-Styling und Selektoren
- JavaScript-Grundlagen
- DOM-Manipulation
- Praktische Projekte

### 2. JavaScript Vertiefung (5 Level)
- Arrays und Schleifen
- Objekte und Eigenschaften
- Moderne Array-Methoden
- Event Handling
- Asynchrone Programmierung

## 🛠️ Technologie-Stack

- **Backend**: Node.js mit Express
- **Template Engine**: EJS
- **Datenbank**: SQLite
- **Session Management**: express-session
- **Passwort-Hashing**: bcrypt
- **Email-Service**: Resend
- **Frontend**: Tailwind CSS, Font Awesome
- **JavaScript**: Vanilla JS mit modernen Features

## 📦 Installation

1. **Repository klonen oder Dateien kopieren**

2. **Dependencies installieren**:
   ```bash
   npm install
   ```

3. **Datenbank initialisieren**:
   ```bash
   node database/init.js
   ```

4. **Level-Content laden**:
   ```bash
   node database/seed-levels.js
   node database/seed-js-advanced.js
   ```

5. **Server starten**:
   ```bash
   node index.js
   ```

6. **Browser öffnen**: http://localhost:3000

## 🧪 Tests ausführen

```bash
node test-app.js
```

## 📁 Projektstruktur

```
├── database/
│   ├── init.js              # Datenbank-Initialisierung
│   ├── db.js                # Datenbank-Helper
│   ├── seed-levels.js       # HTML/CSS/JS Level-Content
│   └── seed-js-advanced.js  # JavaScript Level-Content
├── views/
│   ├── auth/                # Login/Register Templates
│   ├── course/              # Kurs-Templates
│   ├── dashboard.ejs        # Dashboard
│   └── landing.ejs          # Landing Page
├── public/
│   ├── css/style.css        # Styling
│   └── js/main.js           # Frontend JavaScript
├── index.js                 # Hauptserver
├── test-app.js              # Test-Skript
└── README.md                # Diese Datei
```

## 🎯 Verwendung

1. **Registrierung**: Erstelle ein neues Benutzerkonto
2. **Login**: Melde dich mit deinen Daten an
3. **Kurs wählen**: Wähle einen Kurs im Dashboard
4. **Level absolvieren**: Arbeite dich durch die Level
5. **Code schreiben**: Löse praktische Aufgaben
6. **Fortschritt verfolgen**: Sieh deinen Lernfortschritt

## 🔧 Konfiguration

### Umgebungsvariablen
Erstelle eine `.env` Datei basierend auf `.env.example`:

```bash
# Email Configuration
RESEND_API_KEY=your_resend_api_key_here
FROM_EMAIL=<EMAIL>
BASE_URL=http://localhost:3000

# Server Configuration
PORT=3000
HTTPS_PORT=3443

# Session Configuration
SESSION_SECRET=your-secret-key-change-in-production
```

### Email-Setup (Resend)
1. Registriere dich bei [Resend](https://resend.com)
2. Verifiziere deine Domain (codewave.online)
3. Erstelle einen API Key
4. Konfiguriere die gewünschten Absender-Adressen

### Datenbank
- SQLite-Datei: `database/learning_platform.db`
- Automatische Erstellung bei Initialisierung
- Neue Tabellen für Email-Verifikation und Passwort-Reset

## 🚀 Deployment

Für Produktionsumgebung:
1. Session-Secret ändern
2. HTTPS aktivieren
3. Sichere Code-Ausführung implementieren
4. Backup-Strategie für Datenbank

## 🤝 Beitragen

1. Fork das Repository
2. Erstelle einen Feature-Branch
3. Committe deine Änderungen
4. Erstelle einen Pull Request

## 📝 Lizenz

Dieses Projekt ist für Bildungszwecke erstellt.

## 🎉 Viel Spaß beim Lernen!

Starte deine Reise in die Webentwicklung mit CodeLearning!
