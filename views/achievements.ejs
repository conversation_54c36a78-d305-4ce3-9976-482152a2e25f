<%- include('partials/header') %>

<body class="bg-gray-900 text-gray-100">
    <%- include('partials/navbar') %>

    <div class="max-w-screen-xl mx-auto p-4">
        <!-- Breadcrumb -->
        <nav class="flex mb-8" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="/dashboard" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                        <i class="fas fa-home w-3 h-3 me-2.5"></i>
                        <%= t('nav.dashboard') %>
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400"><%= t("achievements.title") %></span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Statistics Cards -->
        <%- include('partials/stats-cards', { stats: { completedLevels: userStats.completedLevels || 0, totalPoints: userStats.totalScore || 0, codeSolutions: userStats.completedLevels || 0 } }) %>

        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-4xl font-extrabold text-white mb-4">
                <i class="fas fa-trophy text-yellow-500"></i> <%= t("achievements.title") %>
            </h1>
            <p class="text-xl text-gray-300"><%= t('text.collect_achievements') %></p>
        </div>

        <!-- Progress Overview -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-500 bg-opacity-20">
                        <i class="fas fa-layer-group text-blue-400 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400"><%= t('stats.completed_levels') %></p>
                        <p class="text-2xl font-bold text-white"><%= stats.total_completed_levels %></p>
                    </div>
                </div>
            </div>
            <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-500 bg-opacity-20">
                        <i class="fas fa-star text-green-400 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400"><%= t('stats.total_points') %></p>
                        <p class="text-2xl font-bold text-white"><%= stats.total_score %></p>
                    </div>
                </div>
            </div>
            <div class="bg-gray-800 rounded-lg p-6 border border-gray-700">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-500 bg-opacity-20">
                        <i class="fas fa-graduation-cap text-purple-400 text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-400"><%= t('text.courses_completed') %></p>
                        <p class="text-2xl font-bold text-white"><%= stats.completed_courses %></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Achievements Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <% achievements.forEach(achievement => { %>
                <div class="bg-gray-800 rounded-lg p-6 border border-gray-700 <%= achievement.earned ? 'ring-2 ring-yellow-500' : '' %> transition-all duration-300 hover:scale-105">
                    <div class="flex items-center justify-between mb-4">
                        <div class="p-3 rounded-full <%= achievement.earned ? 'bg-yellow-500 bg-opacity-20' : 'bg-gray-600 bg-opacity-20' %>">
                            <i class="<%= achievement.icon %> <%= achievement.earned ? 'text-yellow-400' : 'text-gray-500' %> text-2xl"></i>
                        </div>
                        <% if (achievement.earned) { %>
                            <div class="flex items-center text-yellow-400">
                                <i class="fas fa-check-circle mr-1"></i>
                                <span class="text-sm font-medium"><%= t('achievements.earned') %></span>
                            </div>
                        <% } else { %>
                            <div class="flex items-center text-gray-500">
                                <i class="fas fa-lock mr-1"></i>
                                <span class="text-sm font-medium"><%= t("achievements.locked") %></span>
                            </div>
                        <% } %>
                    </div>
                    
                    <div class="flex items-center justify-between mb-2">
                        <h3 class="text-xl font-bold <%= achievement.earned ? 'text-white' : 'text-gray-400' %>">
                            <%= t('achievement.name.' + achievement.id) %>
                        </h3>
                        <% if (achievement.language && achievement.language !== 'general') { %>
                            <span class="px-2 py-1 text-xs font-medium rounded-full <%=
                                achievement.language === 'html-css-js' ? 'bg-orange-500 bg-opacity-20 text-orange-400' :
                                achievement.language === 'javascript' ? 'bg-yellow-500 bg-opacity-20 text-yellow-400' :
                                achievement.language === 'php' ? 'bg-purple-500 bg-opacity-20 text-purple-400' :
                                achievement.language === 'python' ? 'bg-blue-500 bg-opacity-20 text-blue-400' :
                                achievement.language === 'go' ? 'bg-cyan-500 bg-opacity-20 text-cyan-400' :
                                achievement.language === 'java' ? 'bg-red-500 bg-opacity-20 text-red-400' :
                                'bg-gray-500 bg-opacity-20 text-gray-400'
                            %>">
                                <%= achievement.language.toUpperCase() %>
                            </span>
                        <% } %>
                    </div>

                    <p class="<%= achievement.earned ? 'text-gray-300' : 'text-gray-500' %> mb-4">
                        <%= t('achievement.desc.' + achievement.id) %>
                    </p>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-coins <%= achievement.earned ? 'text-yellow-400' : 'text-gray-500' %> mr-2"></i>
                            <span class="<%= achievement.earned ? 'text-yellow-400' : 'text-gray-500' %> font-medium">
                                +<%= achievement.points %> <%= t("achievements.points") %>
                            </span>
                        </div>
                        <% if (achievement.earned && achievement.earned_at) { %>
                            <span class="text-xs text-gray-400">
                                <%= new Date(achievement.earned_at).toLocaleDateString('de-DE') %>
                            </span>
                        <% } %>
                    </div>
                    
                    <!-- Enhanced Progress Bar with New Achievement System -->
                    <% if (!achievement.earned && achievement.progress !== undefined) { %>
                        <div class="mt-4">
                            <div class="flex justify-between text-xs text-gray-400 mb-1">
                                <span><%= t('achievements.progress') %></span>
                                <span>
                                    <%= achievement.progress %>/<%= achievement.progress_max %>
                                    <% if (achievement.requirement_type === 'levels_completed') { %>
                                        levels
                                    <% } else if (achievement.requirement_type === 'total_score') { %>
                                        points
                                    <% } else if (achievement.requirement_type === 'boss_levels_completed') { %>
                                        boss levels
                                    <% } else if (achievement.requirement_type === 'perfect_scores') { %>
                                        perfect scores
                                    <% } else if (achievement.requirement_type === 'course_completed') { %>
                                        courses
                                    <% } %>
                                </span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-500"
                                     style="width: <%= achievement.progress_percentage %>%"></div>
                            </div>
                            <% if (achievement.progress_percentage > 0) { %>
                                <div class="text-xs text-gray-400 mt-1">
                                    <%= achievement.progress_percentage %>% complete
                                </div>
                            <% } %>
                        </div>
                    <% } %>
                </div>
            <% }); %>
        </div>

        <!-- Back Button -->
        <div class="mt-8 text-center">
            <a href="/profile" class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                <i class="fas fa-arrow-left mr-2"></i>
                <%= t('text.back_to_profile') %>
            </a>
        </div>
    </div>

<%- include('partials/footer') %>
