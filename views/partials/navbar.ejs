<nav class="bg-gray-800 border-gray-700 border-b">
    <div class="max-w-screen-xl flex flex-wrap items-center justify-between mx-auto p-4">
        <a href="/dashboard" class="flex items-center space-x-3 rtl:space-x-reverse">
<img src="../images/logo.png" alt="Logo" class="w-10 h-10">
            <span class="self-center text-2xl font-semibold whitespace-nowrap text-white">CodeWave</span>
        </a>

        <!-- Main Navigation -->
        <div class="hidden md:flex md:w-auto md:order-1">
            <ul class="flex flex-col p-4 md:p-0 mt-4 font-medium border border-gray-100 rounded-lg bg-gray-50 md:space-x-8 rtl:space-x-reverse md:flex-row md:mt-0 md:border-0 md:bg-transparent dark:bg-gray-800 md:dark:bg-transparent dark:border-gray-700">
                <li>
                    <a href="/dashboard" class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700"><%= t('nav.dashboard') %></a>
                </li>
                <li>
                    <a href="/leaderboard" class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700 flex items-center">
                        <i class="fas fa-crown text-yellow-500 mr-1"></i>
                        Leaderboard
                    </a>
                </li>
                <li>
                    <a href="/achievements" class="block py-2 px-3 text-gray-900 rounded hover:bg-gray-100 md:hover:bg-transparent md:hover:text-blue-700 md:p-0 dark:text-white md:dark:hover:text-blue-500 dark:hover:bg-gray-700 dark:hover:text-white md:dark:hover:bg-transparent dark:border-gray-700 flex items-center">
                        <i class="fas fa-trophy text-yellow-500 mr-1"></i>
                        <%= t('nav.achievements') %>
                    </a>
                </li>
                <% if (typeof user !== 'undefined' && user && user.is_admin) { %>
                    <li>
                        <a href="/admin" class="block py-2 px-3 text-red-300 rounded hover:bg-red-900 md:hover:bg-transparent md:hover:text-red-400 md:p-0 md:dark:hover:text-red-400 dark:hover:bg-red-900 dark:hover:text-red-300 md:dark:hover:bg-transparent dark:border-gray-700 flex items-center bg-red-900 bg-opacity-30">
                            <i class="fas fa-shield-alt text-red-400 mr-1"></i>
                            Admin Panel
                        </a>
                    </li>
                <% } %>
            </ul>
        </div>

        <div class="flex items-center md:order-2 space-x-3 md:space-x-0 rtl:space-x-reverse">
            <!-- Language Switcher -->
            <%- include('language-switcher') %>

            <span class="text-gray-300 mr-4" style="margin-left: 30px;"><%= typeof username !== 'undefined' ? username : (typeof user !== 'undefined' && user ? user.username : 'User') %></span>
            <div style="margin-left: 5px;">
            <button type="button" class="flex text-sm bg-gray-800 rounded-full md:me-0 focus:ring-4 focus:ring-gray-300 dark:focus:ring-gray-600" id="user-menu-button" aria-expanded="false" data-dropdown-toggle="user-dropdown" data-dropdown-placement="bottom">
                <span class="sr-only">Open user menu</span>
                <div class="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center">
                    <i class="fas fa-user text-white text-sm"></i>
                </div>
            </button>
            </div>
            <!-- Dropdown menu -->
            <div class="z-50 hidden my-4 text-base list-none bg-white divide-y divide-gray-100 rounded-lg shadow dark:bg-gray-700 dark:divide-gray-600" id="user-dropdown">
                <div class="px-4 py-3">
                    <span class="block text-sm text-gray-900 dark:text-white"><%= username %></span>
                    <span class="block text-sm text-gray-500 truncate dark:text-gray-400">Lernender</span>
                </div>
                <ul class="py-2" aria-labelledby="user-menu-button">
                    <li><a href="/profile" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white">Profil</a></li>
                    <li><a href="/logout" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 dark:text-gray-200 dark:hover:text-white">Logout</a></li>
                </ul>
            </div>
        </div>
    </div>
</nav>
