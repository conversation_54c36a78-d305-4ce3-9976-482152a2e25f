<!-- Enhanced Language Switcher -->
<div class="relative inline-block text-left z-50">
    <div>
        <button type="button" class="group inline-flex items-center justify-center px-4 py-2.5 bg-gray-800 bg-opacity-80 backdrop-blur-sm border border-gray-600 rounded-xl text-sm font-medium text-white hover:bg-gray-700 hover:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-900 transition-all duration-200 shadow-lg" id="language-menu-button" aria-expanded="false" aria-haspopup="true">
            <%
            const languageNames = {
                'en': 'English',
                'de': 'Deutsch',
                'cs': 'Čeština',
                'fr': 'Français'
            };
            const languageFlags = {
                'en': '<span class="flag-icon flag-icon-us"></span>',
                'de': '<span class="flag-icon flag-icon-de"></span>',
                'cs': '<span class="flag-icon flag-icon-cz"></span>',
                'fr': '<span class="flag-icon flag-icon-fr"></span>'
            };
            %>
            <div class="flex items-center space-x-2">
                <span class="hidden sm:block"><%= languageNames[currentLanguage] %></span>
                <i class="fas fa-globe text-blue-400 text-xs"></i>
            </div>
            <svg class="ml-2 h-4 w-4 text-gray-400 group-hover:text-blue-400 transition-colors" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
        </button>
    </div>

    <div class="origin-top-right absolute right-0 mt-3 w-64 rounded-xl shadow-2xl bg-gray-800 bg-opacity-95 backdrop-blur-lg border border-gray-600 focus:outline-none hidden transform opacity-0 scale-95 transition-all duration-200 z-50" role="menu" aria-orientation="vertical" aria-labelledby="language-menu-button" tabindex="-1" id="language-menu">
        <div class="py-2" role="none">
            <div class="px-3 py-2 border-b border-gray-700">
                <p class="text-xs font-semibold text-gray-400 uppercase tracking-wider">Select Language</p>
            </div>
            <a href="#" class="language-option group flex items-center px-4 py-3 text-sm text-white hover:bg-gray-700 hover:bg-opacity-50 transition-all duration-150" role="menuitem" data-lang="en">
                <span class="flex-1">English</span>
                <% if (currentLanguage === 'en') { %>
                    <i class="fas fa-check text-blue-400 text-xs"></i>
                <% } %>
            </a>
            <a href="#" class="language-option group flex items-center px-4 py-3 text-sm text-white hover:bg-gray-700 hover:bg-opacity-50 transition-all duration-150" role="menuitem" data-lang="de">
                <span class="flex-1">Deutsch</span>
                <% if (currentLanguage === 'de') { %>
                    <i class="fas fa-check text-blue-400 text-xs"></i>
                <% } %>
            </a>
            <a href="#" class="language-option group flex items-center px-4 py-3 text-sm text-white hover:bg-gray-700 hover:bg-opacity-50 transition-all duration-150" role="menuitem" data-lang="cs">
                <span class="flex-1">Čeština</span>
                <% if (currentLanguage === 'cs') { %>
                    <i class="fas fa-check text-blue-400 text-xs"></i>
                <% } %>
            </a>
            <a href="#" class="language-option group flex items-center px-4 py-3 text-sm text-white hover:bg-gray-700 hover:bg-opacity-50 transition-all duration-150" role="menuitem" data-lang="fr">
                <span class="flex-1">Français</span>
                <% if (currentLanguage === 'fr') { %>
                    <i class="fas fa-check text-blue-400 text-xs"></i>
                <% } %>
            </a>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const menuButton = document.getElementById('language-menu-button');
    const menu = document.getElementById('language-menu');
    const languageOptions = document.querySelectorAll('.language-option');

    // Toggle menu with animation
    menuButton.addEventListener('click', function() {
        if (menu.classList.contains('hidden')) {
            menu.classList.remove('hidden');
            setTimeout(() => {
                menu.classList.remove('opacity-0', 'scale-95');
                menu.classList.add('opacity-100', 'scale-100');
            }, 10);
            menuButton.setAttribute('aria-expanded', 'true');
        } else {
            menu.classList.add('opacity-0', 'scale-95');
            menu.classList.remove('opacity-100', 'scale-100');
            setTimeout(() => {
                menu.classList.add('hidden');
            }, 200);
            menuButton.setAttribute('aria-expanded', 'false');
        }
    });

    // Close menu when clicking outside
    document.addEventListener('click', function(event) {
        if (!menuButton.contains(event.target) && !menu.contains(event.target)) {
            menu.classList.add('opacity-0', 'scale-95');
            menu.classList.remove('opacity-100', 'scale-100');
            setTimeout(() => {
                menu.classList.add('hidden');
            }, 200);
            menuButton.setAttribute('aria-expanded', 'false');
        }
    });

    // Handle language selection
    languageOptions.forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            const selectedLang = this.getAttribute('data-lang');
            
            // Send language change request
            fetch(`/api/language/${selectedLang}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Reload page to apply new language
                    window.location.reload();
                } else {
                    console.error('Failed to change language:', data.error);
                }
            })
            .catch(error => {
                console.error('Error changing language:', error);
            });
        });
    });
});
</script>
