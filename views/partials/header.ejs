<!DOCTYPE html>
<html lang="de" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" type="image/x-icon" href="https://codewave.online/images/logo.png">
    <title><%= title %></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/flowbite@2.5.1/dist/flowbite.min.css" rel="stylesheet" />
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/7.0.1/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/7.2.3/css/flag-icons.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {"50":"#eff6ff","100":"#dbeafe","200":"#bfdbfe","300":"#93c5fd","400":"#60a5fa","500":"#3b82f6","600":"#2563eb","700":"#1d4ed8","800":"#1e40af","900":"#1e3a8a","950":"#172554"}
                    }
                }
            }
        }
    </script>
    <style>
        /* Enhanced code highlighting in instructions */
        .prose code {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            color: #fbbf24;
            padding: 0.375rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            font-family: 'Fira Code', ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
            font-weight: 600;
            border: 1px solid #4b5563;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
            display: inline-block;
        }

        .prose code::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, #60a5fa, transparent);
            border-radius: 0.375rem 0.375rem 0 0;
        }
        .prose pre {
            background: linear-gradient(135deg, #111827 0%, #1f2937 100%);
            border: 1px solid #374151;
            border-radius: 0.75rem;
            padding: 1.25rem;
            overflow-x: auto;
            margin: 1.5rem 0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .prose pre::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #ef4444, #f59e0b, #10b981, #3b82f6, #8b5cf6);
            border-radius: 0.75rem 0.75rem 0 0;
        }

        .prose pre code {
            background: none !important;
            border: none !important;
            padding: 0 !important;
            color: #e5e7eb !important;
            box-shadow: none !important;
            font-weight: 500;
        }

        .prose pre code::before {
            display: none !important;
        }

        /* Tab completion styles */
        .tab-completion {
            position: absolute;
            background: #1f2937;
            border: 1px solid #374151;
            border-radius: 0.5rem;
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
        }
        .tab-completion-item {
            padding: 0.5rem 1rem;
            cursor: pointer;
            border-bottom: 1px solid #374151;
        }
        .tab-completion-item:hover,
        .tab-completion-item.selected {
            background: #374151;
        }
        .tab-completion-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body class="bg-gray-900 text-gray-100">
