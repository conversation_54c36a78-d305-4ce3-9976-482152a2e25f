<%- include('partials/header') %>
    <%- include('partials/navbar') %>

    <div class="max-w-screen-xl mx-auto p-4">
        <!-- Breadcrumb -->
        <nav class="flex mb-8" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="/dashboard" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                        Dashboard
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400"><%= t("profile.title") %></span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- <%= t("profile.title") %>e Header -->
        <div class="bg-gray-800 rounded-lg p-6 mb-8 border border-gray-700">
            <div class="flex items-center">
                <div class="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center mr-6">
                    <i class="fas fa-user text-white text-3xl"></i>
                </div>
                <div>
                    <h1 class="text-3xl font-bold text-white mb-2"><%= user.username %></h1>
                    <p class="text-gray-300 mb-1"><%= user.email %></p>
                    <p class="text-gray-400 text-sm">
                        <%= t("profile.member_since") %> <%= new Date(user.created_at).toLocaleDateString('de-DE') %>
                    </p>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <!-- Level abgeschlossen -->
            <div class="bg-blue-600 rounded-lg p-6 text-center text-white">
                <i class="fas fa-trophy text-3xl mb-4"></i>
                <h3 class="text-2xl font-bold mb-2"><%= stats.completedLevels %></h3>
                <p class="text-blue-100">Level abgeschlossen</p>
            </div>

            <!-- Gesamtpunkte -->
            <div class="bg-green-600 rounded-lg p-6 text-center text-white">
                <i class="fas fa-star text-3xl mb-4"></i>
                <h3 class="text-2xl font-bold mb-2"><%= stats.totalScore %></h3>
                <p class="text-green-100">Gesamtpunkte</p>
            </div>

            <!-- Gesamtfortschritt -->
            <div class="bg-cyan-600 rounded-lg p-6 text-center text-white">
                <i class="fas fa-percentage text-3xl mb-4"></i>
                <h3 class="text-2xl font-bold mb-2"><%= stats.overallProgress %>%</h3>
                <p class="text-cyan-100">Gesamtfortschritt</p>
            </div>

            <!-- Kurse verfügbar -->
            <div class="bg-yellow-600 rounded-lg p-6 text-center text-white">
                <i class="fas fa-book text-3xl mb-4"></i>
                <h3 class="text-2xl font-bold mb-2"><%= stats.courses.length %></h3>
                <p class="text-yellow-100">Kurse verfügbar</p>
            </div>
        </div>

        <!-- <%= t("profile.title") %>e Settings and Course Progress -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- <%= t("profile.title") %>e Settings -->
            <div class="bg-gray-800 rounded-lg border border-gray-700">
                <div class="p-6 border-b border-gray-700">
                    <h3 class="text-xl font-bold text-white flex items-center">
                        <i class="fas fa-cog text-blue-500 mr-3"></i>
                        <%= t("profile.title") %> bearbeiten
                    </h3>
                </div>
                <div class="p-6">
                    <form id="profileForm">
                        <div class="mb-6">
                            <label for="username" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white"><%= t("profile.username") %></label>
                            <input type="text" id="username" name="username" value="<%= user.username %>" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" required>
                        </div>
                        <div class="mb-6">
                            <label for="email" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">E-Mail</label>
                            <input type="email" id="email" name="email" value="<%= user.email %>" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500" required>
                        </div>
                        <div class="mb-6">
                            <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Registriert seit</label>
                            <input type="text" value="<%= new Date(user.created_at).toLocaleDateString('de-DE') %>" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:text-gray-400" readonly>
                        </div>
                        <% if (user.last_login) { %>
                            <div class="mb-6">
                                <label class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">Letzter Login</label>
                                <input type="text" value="<%= new Date(user.last_login).toLocaleString('de-DE') %>" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:text-gray-400" readonly>
                            </div>
                        <% } %>

                        <!-- Password Change Section -->
                        <div class="border-t border-gray-600 pt-6 mt-6">
                            <h4 class="text-lg font-semibold text-white mb-4">
                                <i class="fas fa-key text-yellow-500 mr-2"></i>
                                <%= t('profile.change_password') %>
                            </h4>
                            <div class="mb-4">
                                <label for="currentPassword" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                                    <%= t('profile.current_password') %>
                                </label>
                                <input type="password" id="currentPassword" name="currentPassword" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white">
                            </div>
                            <div class="mb-4">
                                <label for="newPassword" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                                    <%= t('profile.new_password') %>
                                </label>
                                <input type="password" id="newPassword" name="newPassword" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white">
                            </div>
                            <div class="mb-6">
                                <label for="confirmPassword" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                                    <%= t('profile.confirm_password') %>
                                </label>
                                <input type="password" id="confirmPassword" name="confirmPassword" class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5 dark:bg-gray-600 dark:border-gray-500 dark:placeholder-gray-400 dark:text-white">
                            </div>
                        </div>

                        <div class="flex gap-4">
                            <button type="submit" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800">
                                <i class="fas fa-save mr-2"></i>
                                <%= t('profile.save_changes') %>
                            </button>
                            <button type="button" id="changePasswordBtn" class="text-white bg-yellow-600 hover:bg-yellow-700 focus:ring-4 focus:outline-none focus:ring-yellow-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center">
                                <i class="fas fa-key mr-2"></i>
                                <%= t('profile.change_password_btn') %>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Course Progress -->
            <div class="bg-gray-800 rounded-lg border border-gray-700">
                <div class="p-6 border-b border-gray-700">
                    <h3 class="text-xl font-bold text-white flex items-center">
                        <i class="fas fa-chart-line text-blue-500 mr-3"></i>
                        Kurs-Fortschritt
                    </h3>
                </div>
                <div class="p-6">
                    <% stats.courses.forEach(course => { %>
                        <div class="mb-6 last:mb-0">
                            <div class="flex justify-between items-center mb-3">
                                <h4 class="text-lg font-medium text-white flex items-center">
                                    <% if (course.slug === 'html-css-js') { %>
                                        <i class="fab fa-html5 text-orange-500 mr-2"></i>
                                    <% } else if (course.slug === 'javascript-advanced') { %>
                                        <i class="fab fa-js-square text-yellow-500 mr-2"></i>
                                    <% } else if (course.slug === 'php') { %>
                                        <i class="fab fa-php text-purple-500 mr-2"></i>
                                    <% } else if (course.slug === 'python') { %>
                                        <i class="fab fa-python text-blue-500 mr-2"></i>
                                    <% } else if (course.slug === 'go') { %>
                                        <i class="fas fa-bolt text-cyan-500 mr-2"></i>
                                    <% } else if (course.slug === 'java') { %>
                                        <i class="fab fa-java text-red-500 mr-2"></i>
                                    <% } else { %>
                                        <i class="fas fa-code text-gray-500 mr-2"></i>
                                    <% } %>
                                    <%= course.name %>
                                </h4>
                                <span class="text-sm text-gray-400">
                                    <%= course.stats.completed_levels %> / <%= course.stats.total_levels %>
                                </span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2 mb-3">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: <%= course.progressPercentage %>%"></div>
                            </div>
                            <div class="flex justify-between text-sm">
                                <span class="text-gray-400">
                                    <i class="fas fa-star text-yellow-500"></i> <%= course.stats.total_score %> Punkte
                                </span>
                                <span class="text-gray-400">
                                    <%= course.progressPercentage %>% abgeschlossen
                                </span>
                            </div>
                        </div>
                    <% }); %>

                    <div class="text-center mt-6">
                        <a href="/achievements" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 mr-2 mb-2 dark:bg-blue-600 dark:hover:bg-blue-700 focus:outline-none dark:focus:ring-blue-800 inline-flex items-center">
                            <i class="fas fa-trophy mr-2"></i>
                            Alle Erfolge anzeigen
                        </a>
                        <a href="/dashboard" class="py-2.5 px-5 mr-2 mb-2 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-200 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700 inline-flex items-center">
                            <i class="fas fa-arrow-left mr-2"></i>
                            Zurück zum Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Achievement Section -->
        <div class="bg-gray-800 rounded-lg border border-gray-700 mt-8">
            <div class="p-6 border-b border-gray-700">
                <h3 class="text-xl font-bold text-white flex items-center">
                    <i class="fas fa-medal text-yellow-500 mr-3"></i>
                    Erfolge
                </h3>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
                    <div class="text-center p-4 rounded-lg <%= stats.completedLevels >= 1 ? 'bg-green-900 bg-opacity-30 border border-green-700' : 'bg-gray-700 opacity-50' %>">
                        <i class="fas fa-baby text-3xl mb-3 <%= stats.completedLevels >= 1 ? 'text-green-400' : 'text-gray-500' %>"></i>
                        <h4 class="font-bold text-white mb-1">Erste Schritte</h4>
                        <p class="text-sm text-gray-400">Erstes Level abgeschlossen</p>
                    </div>
                    <div class="text-center p-4 rounded-lg <%= stats.completedLevels >= 5 ? 'bg-orange-900 bg-opacity-30 border border-orange-700' : 'bg-gray-700 opacity-50' %>">
                        <i class="fas fa-fire text-3xl mb-3 <%= stats.completedLevels >= 5 ? 'text-orange-400' : 'text-gray-500' %>"></i>
                        <h4 class="font-bold text-white mb-1">Auf Kurs</h4>
                        <p class="text-sm text-gray-400">5 Level abgeschlossen</p>
                    </div>
                    <div class="text-center p-4 rounded-lg <%= stats.completedLevels >= 10 ? 'bg-blue-900 bg-opacity-30 border border-blue-700' : 'bg-gray-700 opacity-50' %>">
                        <i class="fas fa-rocket text-3xl mb-3 <%= stats.completedLevels >= 10 ? 'text-blue-400' : 'text-gray-500' %>"></i>
                        <h4 class="font-bold text-white mb-1">Durchstarter</h4>
                        <p class="text-sm text-gray-400">10 Level abgeschlossen</p>
                    </div>
                    <div class="text-center p-4 rounded-lg <%= stats.totalScore >= 1000 ? 'bg-yellow-900 bg-opacity-30 border border-yellow-700' : 'bg-gray-700 opacity-50' %>">
                        <i class="fas fa-crown text-3xl mb-3 <%= stats.totalScore >= 1000 ? 'text-yellow-400' : 'text-gray-500' %>"></i>
                        <h4 class="font-bold text-white mb-1">Punktesammler</h4>
                        <p class="text-sm text-gray-400">1000 Punkte erreicht</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Progress Reset Section -->
        <div class="bg-red-900 bg-opacity-20 border border-red-700 rounded-lg mt-8">
            <div class="p-6 border-b border-red-700">
                <h3 class="text-xl font-bold text-white flex items-center">
                    <i class="fas fa-exclamation-triangle text-red-500 mr-3"></i>
                    <%= t('profile.danger_zone') %>
                </h3>
            </div>
            <div class="p-6">
                <div class="flex items-start justify-between">
                    <div class="flex-1">
                        <h4 class="text-lg font-semibold text-white mb-2">
                            <%= t('profile.reset_progress') %>
                        </h4>
                        <p class="text-gray-300 mb-4">
                            <%= t('profile.reset_progress_desc') %>
                        </p>
                        <div class="bg-red-900 bg-opacity-30 border border-red-700 rounded-lg p-4 mb-4">
                            <h5 class="text-red-400 font-semibold mb-2">
                                <i class="fas fa-trash-alt mr-2"></i>
                                <%= t('profile.will_be_deleted') %>:
                            </h5>
                            <ul class="text-red-300 text-sm space-y-1">
                                <li>• <%= t('profile.all_level_progress') %></li>
                                <li>• <%= t('profile.all_achievements') %></li>
                                <li>• <%= t('profile.all_scores') %></li>
                                <li>• <%= t('profile.all_submissions') %></li>
                            </ul>
                        </div>
                        <div class="bg-green-900 bg-opacity-30 border border-green-700 rounded-lg p-4 mb-4">
                            <h5 class="text-green-400 font-semibold mb-2">
                                <i class="fas fa-shield-alt mr-2"></i>
                                <%= t('profile.will_be_kept') %>:
                            </h5>
                            <ul class="text-green-300 text-sm space-y-1">
                                <li>• <%= t('profile.account_data') %></li>
                                <li>• <%= t('profile.premium_access') %></li>
                                <li>• <%= t('profile.course_access') %></li>
                                <li>• <%= t('profile.admin_privileges') %></li>
                            </ul>
                        </div>
                    </div>
                    <div class="ml-6">
                        <button id="resetProgressBtn" class="bg-red-600 hover:bg-red-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center">
                            <i class="fas fa-redo mr-2"></i>
                            <%= t('profile.reset_progress_btn') %>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

<%- include('partials/footer') %>
    <script>
        // <%= t("profile.title") %>e form submission
        document.getElementById('profileForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            fetch('/profile/update', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message, 'success');
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showNotification(data.message, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Fehler beim Aktualisieren des <%= t("profile.title") %>s', 'error');
            });
        });

        // Password change functionality
        document.getElementById('changePasswordBtn').addEventListener('click', function() {
            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;

            // Validation
            if (!currentPassword || !newPassword || !confirmPassword) {
                showNotification('<%- t("profile.password_fields_required") %>', 'error');
                return;
            }

            if (newPassword !== confirmPassword) {
                showNotification('<%- t("profile.passwords_dont_match") %>', 'error');
                return;
            }

            if (newPassword.length < 6) {
                showNotification('<%- t("profile.password_too_short") %>', 'error');
                return;
            }

            // Show loading state
            const button = this;
            const originalText = button.innerHTML;
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i><%- t("profile.changing_password") %>';

            fetch('/profile/change-password', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    currentPassword: currentPassword,
                    newPassword: newPassword
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('<%- t("profile.password_changed") %>', 'success');
                    // Clear password fields
                    document.getElementById('currentPassword').value = '';
                    document.getElementById('newPassword').value = '';
                    document.getElementById('confirmPassword').value = '';
                } else {
                    showNotification(data.message || '<%- t("profile.password_change_error") %>', 'error');
                }
                button.disabled = false;
                button.innerHTML = originalText;
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('<%- t("profile.password_change_error") %>', 'error');
                button.disabled = false;
                button.innerHTML = originalText;
            });
        });

        // Progress reset functionality
        document.getElementById('resetProgressBtn').addEventListener('click', function() {
            // First confirmation
            if (!confirm('<%- t("profile.reset_confirm_1") %>')) {
                return;
            }

            // Second confirmation with typing requirement
            const confirmText = prompt('<%- t("profile.reset_confirm_2") %>');
            if (confirmText === null) {
                // User cancelled
                return;
            }

            if (confirmText.trim().toUpperCase() !== 'RESET') {
                alert('<%- t("profile.reset_cancelled") %>');
                return;
            }

            // Final confirmation
            if (!confirm('<%- t("profile.reset_confirm_3") %>')) {
                return;
            }

            // Show loading state
            const button = this;
            const originalText = button.innerHTML;
            button.disabled = true;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i><%- t("profile.resetting") %>';

            fetch('/profile/reset-progress', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('<%- t("profile.reset_success") %>', 'success');
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    showNotification(data.message || '<%- t("profile.reset_error") %>', 'error');
                    button.disabled = false;
                    button.innerHTML = originalText;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('<%- t("profile.reset_error") %>', 'error');
                button.disabled = false;
                button.innerHTML = originalText;
            });
        });

        // Notification function
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
                type === 'success' ? 'bg-green-600 text-white' : 'bg-red-600 text-white'
            }`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 5000);
        }
    </script>
</body>
</html>
