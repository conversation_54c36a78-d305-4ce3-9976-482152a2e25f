<%- include('partials/header') %>

<body class="bg-gray-900 text-gray-100">
    <%- include('partials/navbar') %>

    <div class="max-w-screen-xl mx-auto p-4">
        <!-- Breadcrumb -->
        <nav class="flex mb-8" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="/dashboard" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                        <i class="fas fa-home w-3 h-3 me-2.5"></i>
                        <%= t('nav.dashboard') %>
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400"><%= t('nav.leaderboard') %></span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Statistics Cards -->
        <%- include('partials/stats-cards', { stats: { completedLevels: userStats.completedLevels || 0, totalPoints: userStats.totalScore || 0, codeSolutions: userStats.completedLevels || 0 } }) %>

        <!-- Header -->
        <div class="mb-8 text-center">
            <h1 class="text-4xl font-extrabold text-white mb-4">
                <i class="fas fa-crown text-yellow-500"></i> <%= t('nav.leaderboard') %>
            </h1>
            <p class="text-xl text-gray-300"><%= t('leaderboard.subtitle') %></p>
            <% if (currentUserRank !== 'Nicht gerankt') { %>
                <div class="mt-4 inline-flex items-center px-3 py-2 text-sm font-medium text-center text-white bg-blue-700 rounded-lg">
                    <i class="fas fa-medal mr-2"></i>
                    <%= t('leaderboard.your_rank') %>: #<%= currentUserRank %>
                </div>
            <% } %>
        </div>

        <!-- Leaderboard Table -->
        <div class="relative overflow-x-auto shadow-md sm:rounded-lg">
            <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                <thead class="text-xs text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                    <tr>
                        <th scope="col" class="px-6 py-3"><%= t('leaderboard.rank') %></th>
                        <th scope="col" class="px-6 py-3"><%= t('leaderboard.user') %></th>
                        <th scope="col" class="px-6 py-3"><%= t('leaderboard.total_points') %></th>
                        <th scope="col" class="px-6 py-3"><%= t('leaderboard.levels') %></th>
                        <th scope="col" class="px-6 py-3"><%= t('leaderboard.achievements') %></th>
                        <th scope="col" class="px-6 py-3"><%= t('leaderboard.member_since') %></th>
                    </tr>
                </thead>
                <tbody>
                    <% leaderboard.forEach((user, index) => { %>
                        <tr class="<%= user.username === username ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800' : 'bg-white dark:bg-gray-800' %> border-b dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
                            <td class="px-6 py-4 font-medium">
                                <div class="flex items-center">
                                    <% if (user.rank === 1) { %>
                                        <i class="fas fa-crown text-yellow-500 text-lg mr-2"></i>
                                    <% } else if (user.rank === 2) { %>
                                        <i class="fas fa-medal text-gray-400 text-lg mr-2"></i>
                                    <% } else if (user.rank === 3) { %>
                                        <i class="fas fa-medal text-amber-600 text-lg mr-2"></i>
                                    <% } else { %>
                                        <span class="w-6 mr-2"></span>
                                    <% } %>
                                    <span class="<%= user.username === username ? 'text-blue-600 dark:text-blue-400 font-bold' : 'text-gray-900 dark:text-white' %>">
                                        #<%= user.rank %>
                                    </span>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 rounded-full bg-blue-600 flex items-center justify-center mr-3">
                                        <i class="fas fa-user text-white text-sm"></i>
                                    </div>
                                    <span class="font-medium <%= user.username === username ? 'text-blue-600 dark:text-blue-400' : 'text-gray-900 dark:text-white' %>">
                                        <%= user.username %>
                                        <% if (user.username === username) { %>
                                            <span class="ml-2 bg-blue-100 text-blue-800 text-xs font-medium px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300">Du</span>
                                        <% } %>
                                    </span>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex flex-col">
                                    <span class="font-bold text-lg text-gray-900 dark:text-white">
                                        <%= user.total_score %>
                                    </span>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <i class="fas fa-layer-group text-blue-500 mr-2"></i>
                                    <span class="font-medium text-gray-900 dark:text-white">
                                        <%= user.completed_levels %>
                                    </span>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <i class="fas fa-trophy text-yellow-500 mr-2"></i>
                                    <span class="font-medium text-gray-900 dark:text-white">
                                        <%= user.earned_achievements %>
                                    </span>
                                </div>
                            </td>
                            <td class="px-6 py-4 text-gray-500 dark:text-gray-400">
                                <%= new Date(user.created_at).toLocaleDateString('de-DE') %>
                            </td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>

        <% if (leaderboard.length === 0) { %>
            <div class="text-center py-12">
                <i class="fas fa-users text-gray-400 text-6xl mb-4"></i>
                <h3 class="text-xl font-medium text-gray-900 dark:text-white mb-2">Noch keine Rangliste</h3>
                <p class="text-gray-500 dark:text-gray-400">Sei der Erste und sammle Punkte!</p>
            </div>
        <% } %>

        <!-- Info Box -->
        <div class="mt-8 p-4 mb-4 text-sm text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400" role="alert">
            <div class="flex items-center">
                <i class="fas fa-info-circle mr-2"></i>
                <span class="font-medium">Punktesystem:</span>
            </div>
            <ul class="mt-2 ml-4 list-disc list-inside">
                <li>Level abschließen: Bis zu 100 Punkte je nach Qualität der Lösung</li>
                <li>Erfolge freischalten: 50-500 zusätzliche Punkte</li>
                <li>Das Leaderboard wird in Echtzeit aktualisiert</li>
            </ul>
        </div>

        <!-- Back Button -->
        <div class="mt-8 text-center">
            <a href="/dashboard" class="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:border-gray-700 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-white">
                <i class="fas fa-arrow-left mr-2"></i>
                Zurück zum Dashboard
            </a>
        </div>
    </div>

<%- include('partials/footer') %>
