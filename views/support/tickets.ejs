<%- include('../partials/header') %>

<body class="bg-gray-900 text-gray-100">
    <%- include('../partials/navbar') %>

    <div class="max-w-screen-xl mx-auto p-4">
        <!-- Breadcrumb -->
        <nav class="flex mb-8" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="/dashboard" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                        <i class="fas fa-home w-3 h-3 me-2.5"></i>
                        <%= t('nav.dashboard') %>
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400"><%= t('support.title') %></span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Header -->
        <div class="mb-8 flex justify-between items-center">
            <div>
                <h1 class="text-4xl font-extrabold text-white mb-4">
                    <i class="fas fa-headset text-blue-500"></i> <%= t('support.title') %>
                </h1>
                <p class="text-xl text-gray-300"><%= t('support.subtitle') %></p>
            </div>
            <a href="/support/new" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                <i class="fas fa-plus mr-2"></i>
                <%= t('support.new_ticket') %>
            </a>
        </div>

        <!-- Tickets Table -->
        <div class="bg-gray-800 rounded-lg shadow-lg border border-gray-700">
            <div class="p-6">
                <% if (tickets.length === 0) { %>
                    <div class="text-center py-12">
                        <i class="fas fa-ticket-alt text-gray-500 text-6xl mb-4"></i>
                        <h3 class="text-xl font-medium text-gray-300 mb-2"><%= t('support.no_tickets') %></h3>
                        <p class="text-gray-500 mb-6"><%= t('support.no_tickets_desc') %></p>
                        <a href="/support/new" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i>
                            <%= t('support.create_first_ticket') %>
                        </a>
                    </div>
                <% } else { %>
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm text-left text-gray-300">
                            <thead class="text-xs text-gray-400 uppercase bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3">#</th>
                                    <th scope="col" class="px-6 py-3"><%= t('support.title_col') %></th>
                                    <% if (isStaff) { %>
                                        <th scope="col" class="px-6 py-3"><%= t('support.user') %></th>
                                    <% } %>
                                    <th scope="col" class="px-6 py-3"><%= t('support.status') %></th>
                                    <th scope="col" class="px-6 py-3"><%= t('support.messages') %></th>
                                    <th scope="col" class="px-6 py-3"><%= t('support.last_update') %></th>
                                    <th scope="col" class="px-6 py-3"><%= t('support.actions') %></th>
                                </tr>
                            </thead>
                            <tbody>
                                <% tickets.forEach(ticket => { %>
                                    <tr class="bg-gray-800 border-b border-gray-700 hover:bg-gray-700">
                                        <td class="px-6 py-4 font-medium text-white">#<%= ticket.id %></td>
                                        <td class="px-6 py-4">
                                            <div class="font-medium text-white"><%= ticket.title %></div>
                                        </td>
                                        <% if (isStaff) { %>
                                            <td class="px-6 py-4">
                                                <div class="text-gray-300"><%= ticket.username %></div>
                                                <div class="text-xs text-gray-500"><%= ticket.email %></div>
                                            </td>
                                        <% } %>
                                        <td class="px-6 py-4">
                                            <span class="px-2 py-1 text-xs rounded-full <%= ticket.status === 'open' ? 'bg-green-600 text-white' : ticket.status === 'closed' ? 'bg-red-600 text-white' : 'bg-yellow-600 text-white' %>">
                                                <%= t('support.status_' + ticket.status) %>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4">
                                            <span class="text-gray-300"><%= ticket.message_count %></span>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="text-gray-300">
                                                <%= new Date(ticket.last_message_at || ticket.updated_at).toLocaleDateString() %>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <a href="/support/<%= ticket.id %>" class="text-blue-400 hover:text-blue-300 font-medium">
                                                <%= t('support.view') %>
                                            </a>
                                        </td>
                                    </tr>
                                <% }); %>
                            </tbody>
                        </table>
                    </div>
                <% } %>
            </div>
        </div>
    </div>

    <%- include('../partials/footer') %>
</body>
</html>
