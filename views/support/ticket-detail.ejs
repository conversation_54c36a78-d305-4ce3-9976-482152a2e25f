<%- include('../partials/header') %>

<body class="bg-gray-900 text-gray-100">
    <%- include('../partials/navbar') %>

    <div class="max-w-screen-xl mx-auto p-4">
        <!-- Breadcrumb -->
        <nav class="flex mb-8" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="/dashboard" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                        <i class="fas fa-home w-3 h-3 me-2.5"></i>
                        <%= t('nav.dashboard') %>
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                        </svg>
                        <a href="/support" class="ms-1 text-sm font-medium text-gray-500 hover:text-blue-600 md:ms-2 dark:text-gray-400 dark:hover:text-white"><%= t('support.title') %></a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400">#<%= ticket.id %></span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Ticket Header -->
        <div class="mb-8 flex justify-between items-start">
            <div>
                <h1 class="text-4xl font-extrabold text-white mb-2">
                    <i class="fas fa-ticket-alt text-blue-500"></i> 
                    <%= t('support.ticket') %> #<%= ticket.id %>
                </h1>
                <h2 class="text-2xl text-gray-300 mb-4"><%= ticket.title %></h2>
                <div class="flex items-center space-x-4">
                    <span class="px-3 py-1 text-sm rounded-full <%= ticket.status === 'open' ? 'bg-green-600 text-white' : ticket.status === 'closed' ? 'bg-red-600 text-white' : 'bg-yellow-600 text-white' %>">
                        <%= t('support.status_' + ticket.status) %>
                    </span>
                    <span class="text-gray-400">
                        <%= t('support.created') %>: <%= new Date(ticket.created_at).toLocaleDateString() %>
                    </span>
                    <% if (isStaff) { %>
                        <span class="text-gray-400">
                            <%= t('support.user') %>: <%= ticket.username %>
                        </span>
                    <% } %>
                </div>
            </div>
            <% if (isStaff) { %>
                <div class="flex space-x-2">
                    <select id="statusSelect" class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-2.5">
                        <option value="open" <%= ticket.status === 'open' ? 'selected' : '' %>><%= t('support.status_open') %></option>
                        <option value="pending" <%= ticket.status === 'pending' ? 'selected' : '' %>><%= t('support.status_pending') %></option>
                        <option value="closed" <%= ticket.status === 'closed' ? 'selected' : '' %>><%= t('support.status_closed') %></option>
                    </select>
                    <button onclick="updateStatus()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm">
                        <%= t('support.update_status') %>
                    </button>
                </div>
            <% } %>
        </div>

        <!-- Messages -->
        <div class="bg-gray-800 rounded-lg shadow-lg border border-gray-700 mb-6">
            <div class="p-6">
                <h3 class="text-xl font-bold text-white mb-6">
                    <i class="fas fa-comments mr-2"></i>
                    <%= t('support.conversation') %>
                </h3>
                
                <div id="messages" class="space-y-4 mb-6">
                    <% messages.forEach(message => { %>
                        <div class="flex <%= message.is_staff ? 'justify-end' : 'justify-start' %>">
                            <div class="max-w-3xl <%= message.is_staff ? 'bg-blue-600' : 'bg-gray-700' %> rounded-lg p-4">
                                <div class="flex items-center mb-2">
                                    <div class="flex items-center">
                                        <% if (message.is_staff) { %>
                                            <i class="fas fa-shield-alt text-yellow-400 mr-2"></i>
                                        <% } else { %>
                                            <i class="fas fa-user text-gray-400 mr-2"></i>
                                        <% } %>
                                        <span class="font-medium text-white"><%= message.username %></span>
                                        <% if (message.is_staff) { %>
                                            <span class="ml-2 px-2 py-1 bg-yellow-600 text-white text-xs rounded-full">Staff</span>
                                        <% } %>
                                    </div>
                                    <span class="ml-auto text-xs text-gray-300">
                                        <%= new Date(message.created_at).toLocaleString() %>
                                    </span>
                                </div>
                                <div class="text-white whitespace-pre-wrap"><%= message.message %></div>
                            </div>
                        </div>
                    <% }); %>
                </div>
                
                <!-- Reply Form -->
                <% if (ticket.status !== 'closed') { %>
                    <div class="border-t border-gray-700 pt-6">
                        <h4 class="text-lg font-medium text-white mb-4">
                            <%= t('support.add_reply') %>
                        </h4>
                        <form id="replyForm">
                            <div class="mb-4">
                                <textarea id="replyMessage" name="message" rows="4" required
                                          class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                                          placeholder="<%= t('support.reply_placeholder') %>"></textarea>
                            </div>
                            <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                                <i class="fas fa-reply mr-2"></i>
                                <%= t('support.send_reply') %>
                            </button>
                        </form>
                    </div>
                <% } else { %>
                    <div class="border-t border-gray-700 pt-6 text-center">
                        <p class="text-gray-400">
                            <i class="fas fa-lock mr-2"></i>
                            <%= t('support.ticket_closed') %>
                        </p>
                    </div>
                <% } %>
            </div>
        </div>

        <!-- Back Button -->
        <a href="/support" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
            <i class="fas fa-arrow-left mr-2"></i>
            <%= t('support.back_to_tickets') %>
        </a>
    </div>

    <script>
        // Reply form submission
        document.getElementById('replyForm')?.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const message = document.getElementById('replyMessage').value.trim();
            if (!message) return;
            
            fetch(`/support/<%= ticket.id %>/message`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ message: message })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Network error');
            });
        });
        
        // Status update (staff only)
        function updateStatus() {
            const status = document.getElementById('statusSelect').value;
            
            fetch(`/support/<%= ticket.id %>/status`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ status: status })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Network error');
            });
        }
    </script>

    <%- include('../partials/footer') %>
</body>
</html>
