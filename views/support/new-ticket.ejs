<%- include('../partials/header') %>

<body class="bg-gray-900 text-gray-100">
    <%- include('../partials/navbar') %>

    <div class="max-w-screen-xl mx-auto p-4">
        <!-- Breadcrumb -->
        <nav class="flex mb-8" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="/dashboard" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                        <i class="fas fa-home w-3 h-3 me-2.5"></i>
                        <%= t('nav.dashboard') %>
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                        </svg>
                        <a href="/support" class="ms-1 text-sm font-medium text-gray-500 hover:text-blue-600 md:ms-2 dark:text-gray-400 dark:hover:text-white"><%= t('support.title') %></a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400"><%= t('support.new_ticket') %></span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-4xl font-extrabold text-white mb-4">
                <i class="fas fa-plus text-blue-500"></i> <%= t('support.new_ticket') %>
            </h1>
            <p class="text-xl text-gray-300"><%= t('support.new_ticket_desc') %></p>
        </div>

        <!-- New Ticket Form -->
        <div class="bg-gray-800 rounded-lg shadow-lg border border-gray-700">
            <div class="p-6">
                <form action="/support/new" method="POST">
                    <div class="mb-6">
                        <label for="title" class="block mb-2 text-sm font-medium text-gray-300">
                            <%= t('support.ticket_title') %>
                        </label>
                        <input type="text" id="title" name="title" required
                               class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                               placeholder="<%= t('support.title_placeholder') %>">
                    </div>
                    
                    <div class="mb-6">
                        <label for="message" class="block mb-2 text-sm font-medium text-gray-300">
                            <%= t('support.message') %>
                        </label>
                        <textarea id="message" name="message" rows="6" required
                                  class="bg-gray-700 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full p-2.5"
                                  placeholder="<%= t('support.message_placeholder') %>"></textarea>
                    </div>
                    
                    <div class="flex space-x-4">
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                            <i class="fas fa-paper-plane mr-2"></i>
                            <%= t('support.create_ticket') %>
                        </button>
                        <a href="/support" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                            <i class="fas fa-arrow-left mr-2"></i>
                            <%= t('common.back') %>
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <%- include('../partials/footer') %>
</body>
</html>
