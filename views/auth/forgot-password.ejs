<%- include('../partials/header') %>

<div class="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center px-4 py-12">
    <div class="max-w-md w-full">
        <div class="bg-gray-800 rounded-2xl shadow-2xl border border-gray-700 overflow-hidden">
            <div class="px-8 py-10">
                <!-- Header -->
                <div class="text-center mb-8">
                    <div class="mx-auto w-16 h-16 bg-blue-500 bg-opacity-20 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-key text-3xl text-blue-400"></i>
                    </div>
                    <h1 class="text-3xl font-bold text-white mb-2"><%= t('auth.forgot_password_title') %></h1>
                    <p class="text-gray-400 text-sm leading-relaxed">
                        <%= t('auth.forgot_password_description') %>
                    </p>
                </div>

                <!-- Error Message -->
                <% if (error) { %>
                    <div class="bg-red-900 bg-opacity-50 border border-red-700 text-red-300 px-4 py-3 rounded-lg mb-6 flex items-center">
                        <i class="fas fa-exclamation-triangle mr-3 text-red-400"></i>
                        <div>
                            <p class="font-medium">Error</p>
                            <p class="text-sm text-red-400"><%= error %></p>
                        </div>
                    </div>
                <% } %>

                <!-- Success Message -->
                <% if (success) { %>
                    <div class="bg-green-900 bg-opacity-50 border border-green-700 text-green-300 px-4 py-3 rounded-lg mb-6 flex items-center">
                        <i class="fas fa-check-circle mr-3 text-green-400"></i>
                        <div>
                            <p class="font-medium"><%= t('auth.success') %></p>
                            <p class="text-sm text-green-400"><%= success %></p>
                        </div>
                    </div>
                    <div class="text-center">
                        <a href="/login" class="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center justify-center group">
                            <i class="fas fa-sign-in-alt mr-2 group-hover:translate-x-1 transition-transform"></i>
                            <%= t('auth.to_login') %>
                        </a>
                    </div>
                <% } else { %>
                    <!-- Form -->
                    <form method="POST" action="/forgot-password" class="space-y-6">
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-300 mb-2">
                                <i class="fas fa-envelope mr-2 text-blue-400"></i>
                                <%= t('auth.email_address') %>
                            </label>
                            <input type="email"
                                   id="email"
                                   name="email"
                                   required
                                   class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                                   placeholder="<%= t('auth.email_placeholder') %>">
                        </div>

                        <button type="submit"
                                class="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center justify-center group">
                            <i class="fas fa-paper-plane mr-2 group-hover:translate-x-1 transition-transform"></i>
                            <%= t('auth.send_reset_link') %>
                        </button>
                    </form>

                    <!-- Navigation Links -->
                    <div class="mt-8 space-y-4 text-center">
                        <div>
                            <a href="/login" class="text-gray-400 hover:text-blue-400 transition-colors duration-200 flex items-center justify-center">
                                <i class="fas fa-arrow-left mr-2"></i>
                                <%= t('auth.back_to_login') %>
                            </a>
                        </div>
                        <div>
                            <a href="/register" class="text-gray-400 hover:text-orange-400 transition-colors duration-200 flex items-center justify-center">
                                <i class="fas fa-user-plus mr-2"></i>
                                Not a member? Register now.
                            </a>
                        </div>
                    </div>
                <% } %>
            </div>
        </div>

        <!-- Help Section -->
        <div class="mt-8 text-center">
            <p class="text-gray-400 text-sm mb-4">
                Need more help? Contact our support team.
            </p>
            <div class="flex justify-center space-x-6 text-gray-500">
                <a href="mailto:<EMAIL>" class="hover:text-blue-400 transition-colors">
                    <i class="fas fa-envelope mr-1"></i>
                    Email
                </a>
                <a href="#" class="hover:text-blue-400 transition-colors">
                    <i class="fas fa-question-circle mr-1"></i>
                    FAQ
                </a>
            </div>
        </div>
    </div>
</div>

<%- include('../partials/footer') %>
