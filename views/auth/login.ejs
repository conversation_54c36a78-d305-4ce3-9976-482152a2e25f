<%- include('../partials/header') %>
<body class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 min-h-screen flex items-center justify-center relative overflow-hidden">
    <!-- Background Animation -->
    <div class="absolute inset-0 opacity-20">
        <div class="absolute top-10 left-10 w-72 h-72 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse"></div>
        <div class="absolute top-40 right-10 w-72 h-72 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse animation-delay-2000"></div>
        <div class="absolute bottom-10 left-1/2 w-72 h-72 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse animation-delay-4000"></div>
    </div>

    <!-- Language Switcher -->
    <div class="absolute top-6 right-6 z-10">
        <%- include('../partials/language-switcher') %>
    </div>

    <div class="relative w-full max-w-md mx-auto p-6 mt-20">
        <div class="bg-gray-800 bg-opacity-90 backdrop-blur-lg rounded-2xl shadow-2xl border border-gray-700 p-8">
            <div class="text-center mb-8">
                <div class="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full mb-6">
                    <img src="https://codewave.online/images/logo.png" alt="Logo" class="w-10 h-10">
                </div>
                <h1 class="text-3xl font-bold text-white mb-2">CodeWave</h1>
                <p class="text-gray-400 text-lg"><%= t('auth.login_title') %></p>
            </div>

            <% if (error) { %>
                <div class="bg-red-900 bg-opacity-50 border border-red-700 text-red-300 px-4 py-3 rounded-lg mb-6 flex items-center">
                    <i class="fas fa-exclamation-triangle mr-3"></i>
                    <span><%= error %></span>
                </div>
            <% } %>

            <form method="POST" action="/login" class="space-y-6">
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-300 mb-2">
                        <i class="fas fa-envelope mr-2 text-blue-400"></i><%= t('auth.username') %>
                    </label>
                    <input type="email"
                           id="email"
                           name="email"
                           required
                           class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                           placeholder="<%= t('auth.username') %>">
                </div>

                <div>
                    <label for="password" class="block text-sm font-medium text-gray-300 mb-2">
                        <i class="fas fa-lock mr-2 text-blue-400"></i><%= t('auth.password') %>
                    </label>
                    <input type="password"
                           id="password"
                           name="password"
                           required
                           class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                           placeholder="<%= t('auth.password') %>">
                </div>

                <button type="submit" class="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transform hover:scale-105 transition-all duration-200 flex items-center justify-center">
                    <i class="fas fa-sign-in-alt mr-3"></i>
                    <%= t('auth.login_button') %>
                </button>
            </form>

            <div class="mt-8 pt-6 border-t border-gray-700">
                <div class="text-center">
                    <p class="text-gray-400 mb-4"><%= t('auth.no_account_yet') %></p>
                    <a href="/register" class="inline-flex items-center justify-center px-6 py-3 border-2 border-blue-600 text-blue-400 font-semibold rounded-lg hover:bg-blue-600 hover:text-white transition-all duration-200">
                        <i class="fas fa-user-plus mr-2"></i>
                        <%= t('auth.register_now') %>
                    </a>
                </div>

                <div class="text-center mt-4">
                    <a href="/forgot-password" class="text-blue-400 hover:text-blue-300 transition-colors duration-200">
                        <i class="fas fa-key mr-2"></i>
                        <%= t('auth.forgot_password') %>
                    </a>
                </div>

                <div class="text-center mt-6">
                    <a href="/" class="text-gray-400 hover:text-white transition-colors duration-200 flex items-center justify-center">
                        <i class="fas fa-arrow-left mr-2"></i>
                        <%= t('auth.back_to_home') %>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.1/dist/flowbite.min.js"></script>
</body>
</html>
