<%- include('../partials/header') %>

<div class="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center px-4 py-12">
    <div class="max-w-md w-full">
        <div class="bg-gray-800 rounded-2xl shadow-2xl border border-gray-700 overflow-hidden">
            <div class="px-8 py-10 text-center">
                <% if (success) { %>
                    <!-- Success State -->
                    <div class="mb-6">
                        <div class="mx-auto w-20 h-20 bg-green-500 bg-opacity-20 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-check-circle text-4xl text-green-400"></i>
                        </div>
                        <div class="w-16 h-1 bg-gradient-to-r from-green-400 to-green-600 mx-auto rounded-full"></div>
                    </div>

                    <h1 class="text-3xl font-bold text-white mb-4">
                        <%= t('auth.password_reset_success') %>
                    </h1>

                    <div class="bg-green-900 bg-opacity-50 border border-green-700 text-green-300 px-4 py-3 rounded-lg mb-8 flex items-center">
                        <i class="fas fa-check-circle mr-3 text-green-400"></i>
                        <div class="text-left">
                            <p class="font-medium"><%= t('auth.successfully_reset') %></p>
                            <p class="text-sm text-green-400"><%= t('auth.password_reset_complete') %></p>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <a href="/login" class="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center justify-center group">
                            <i class="fas fa-sign-in-alt mr-2 group-hover:translate-x-1 transition-transform"></i>
                            <%= t('common.login') %>
                        </a>
                        <a href="/" class="w-full bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center justify-center">
                            <i class="fas fa-home mr-2"></i>
                            <%= t('common.home') %>
                        </a>
                    </div>

                <% } else { %>
                    <!-- Error State -->
                    <div class="mb-6">
                        <div class="mx-auto w-20 h-20 bg-red-500 bg-opacity-20 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-times-circle text-4xl text-red-400"></i>
                        </div>
                        <div class="w-16 h-1 bg-gradient-to-r from-red-400 to-red-600 mx-auto rounded-full"></div>
                    </div>

                    <h1 class="text-3xl font-bold text-white mb-4">
                        <%= t('auth.password_reset_failed') %>
                    </h1>

                    <div class="bg-red-900 bg-opacity-50 border border-red-700 text-red-300 px-4 py-3 rounded-lg mb-6 flex items-center">
                        <i class="fas fa-exclamation-triangle mr-3 text-red-400"></i>
                        <div class="text-left">
                            <p class="font-medium"><%= t('auth.error_resetting') %></p>
                            <p class="text-sm text-red-400"><%= message %></p>
                        </div>
                    </div>

                    <div class="bg-gray-700 rounded-lg p-4 mb-8 text-left">
                        <h3 class="text-white font-semibold mb-3 flex items-center">
                            <i class="fas fa-info-circle mr-2 text-blue-400"></i>
                            <%= t('auth.probable_causes') %>
                        </h3>
                        <ul class="text-gray-300 space-y-2 text-sm">
                            <li class="flex items-start">
                                <i class="fas fa-clock mr-2 text-orange-400 mt-0.5"></i>
                                <%= t('auth.link_expired') %>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check mr-2 text-green-400 mt-0.5"></i>
                                <%= t('auth.link_already_used') %>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-exclamation mr-2 text-red-400 mt-0.5"></i>
                                <%= t('auth.link_invalid') %>
                            </li>
                        </ul>
                    </div>

                    <div class="space-y-4">
                        <a href="/forgot-password" class="w-full bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center justify-center group">
                            <i class="fas fa-key mr-2 group-hover:scale-110 transition-transform"></i>
                            <%= t('auth.new_reset_link') %>
                        </a>
                        <a href="/login" class="w-full bg-gray-700 hover:bg-gray-600 text-gray-300 hover:text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center justify-center">
                            <i class="fas fa-sign-in-alt mr-2"></i>
                            <%= t('auth.back_to_login') %>
                        </a>
                    </div>
                <% } %>
            </div>
        </div>

        <!-- Security Notice -->
        <div class="mt-8 bg-gray-800 bg-opacity-50 rounded-lg p-4 border border-gray-700">
            <div class="flex items-center text-yellow-400 mb-2">
                <i class="fas fa-shield-alt mr-2"></i>
                <span class="font-semibold"><%= t('auth.security_notice') %></span>
            </div>
            <p class="text-gray-300 text-sm">
                <%= t('auth.sessions_ended') %>
            </p>
        </div>

        <!-- Help Section -->
        <div class="mt-6 text-center">
            <p class="text-gray-400 text-sm mb-4">
                <%= t('common.need_help') %>
            </p>
            <div class="flex justify-center space-x-6 text-gray-500">
                <a href="mailto:<EMAIL>" class="hover:text-blue-400 transition-colors">
                    <i class="fas fa-envelope mr-1"></i>
                    <%= t('common.email') %>
                </a>
                <a href="#" class="hover:text-blue-400 transition-colors">
                    <i class="fas fa-question-circle mr-1"></i>
                    <%= t('common.faq') %>
                </a>
            </div>
        </div>
    </div>
</div>

<%- include('../partials/footer') %>
