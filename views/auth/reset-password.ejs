<%- include('../partials/header') %>

<div class="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center px-4 py-12">
    <div class="max-w-md w-full">
        <div class="bg-gray-800 rounded-2xl shadow-2xl border border-gray-700 overflow-hidden">
            <div class="px-8 py-10">
                <!-- Header -->
                <div class="text-center mb-8">
                    <div class="mx-auto w-16 h-16 bg-green-500 bg-opacity-20 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-lock text-3xl text-green-400"></i>
                    </div>
                    <h1 class="text-3xl font-bold text-white mb-2"><%= t('auth.create_new_password') %></h1>
                    <p class="text-gray-400 text-sm leading-relaxed">
                        <%= t('auth.password_requirements') %>
                    </p>
                </div>

                <!-- Error Message -->
                <% if (error) { %>
                    <div class="bg-red-900 bg-opacity-50 border border-red-700 text-red-300 px-4 py-3 rounded-lg mb-6 flex items-center">
                        <i class="fas fa-exclamation-triangle mr-3 text-red-400"></i>
                        <div>
                            <p class="font-medium"><%= t('common.error') %></p>
                            <p class="text-sm text-red-400"><%= error %></p>
                        </div>
                    </div>
                <% } %>

                <!-- Form -->
                <form method="POST" action="/reset-password" class="space-y-6">
                    <input type="hidden" name="token" value="<%= token %>">

                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-300 mb-2">
                            <i class="fas fa-lock mr-2 text-green-400"></i>
                            <%= t('auth.new_password') %>
                        </label>
                        <input type="password"
                               id="password"
                               name="password"
                               required
                               minlength="6"
                               class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200"
                               placeholder="<%= t('auth.new_password_placeholder') %>">
                        <p class="text-gray-400 text-xs mt-1"><%= t('auth.minimum_characters') %></p>
                    </div>

                    <div>
                        <label for="confirmPassword" class="block text-sm font-medium text-gray-300 mb-2">
                            <i class="fas fa-lock mr-2 text-green-400"></i>
                            <%= t('auth.confirm_password') %>
                        </label>
                        <input type="password"
                               id="confirmPassword"
                               name="confirmPassword"
                               required
                               minlength="6"
                               class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200"
                               placeholder="<%= t('auth.confirm_password_placeholder') %>">
                    </div>

                    <!-- Password Strength Indicator -->
                    <div id="passwordStrength" class="hidden">
                        <div class="flex items-center space-x-2 text-sm">
                            <span class="text-gray-400"><%= t('auth.password_strength') %></span>
                            <div class="flex space-x-1">
                                <div id="strength1" class="w-6 h-2 bg-gray-600 rounded"></div>
                                <div id="strength2" class="w-6 h-2 bg-gray-600 rounded"></div>
                                <div id="strength3" class="w-6 h-2 bg-gray-600 rounded"></div>
                                <div id="strength4" class="w-6 h-2 bg-gray-600 rounded"></div>
                            </div>
                            <span id="strengthText" class="text-gray-400"><%= t('auth.strength_very_low') %></span>
                        </div>
                    </div>

                    <button type="submit"
                            class="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-200 flex items-center justify-center group">
                        <i class="fas fa-save mr-2 group-hover:scale-110 transition-transform"></i>
                        <%= t('auth.save_new_password') %>
                    </button>
                </form>

                <!-- Navigation -->
                <div class="mt-8 text-center">
                    <a href="/login" class="text-gray-400 hover:text-green-400 transition-colors duration-200 flex items-center justify-center">
                        <i class="fas fa-arrow-left mr-2"></i>
                        <%= t('auth.back_to_login') %>
                    </a>
                </div>
            </div>
        </div>

        <!-- Security Tips -->
        <div class="mt-8 bg-gray-800 bg-opacity-50 rounded-lg p-4 border border-gray-700">
            <h3 class="text-white font-semibold mb-3 flex items-center">
                <i class="fas fa-shield-alt mr-2 text-green-400"></i>
                <%= t('auth.security_tips') %>
            </h3>
            <ul class="text-gray-300 space-y-1 text-sm">
                <li class="flex items-start">
                    <i class="fas fa-check mr-2 text-green-400 mt-0.5"></i>
                    <%= t('auth.tip_8_characters') %>
                </li>
                <li class="flex items-start">
                    <i class="fas fa-check mr-2 text-green-400 mt-0.5"></i>
                    <%= t('auth.tip_mixed_case') %>
                </li>
                <li class="flex items-start">
                    <i class="fas fa-check mr-2 text-green-400 mt-0.5"></i>
                    <%= t('auth.tip_numbers_symbols') %>
                </li>
            </ul>
        </div>
    </div>
</div>

<script>
// Password strength checker
function checkPasswordStrength(password) {
    let strength = 0;
    let feedback = [];

    if (password.length >= 8) strength++;
    else feedback.push('Minimum 8 Zeichen');

    if (/[a-z]/.test(password) && /[A-Z]/.test(password)) strength++;
    else feedback.push('Lowercase und Uppercase');

    if (/\d/.test(password)) strength++;
    else feedback.push('Numbers');

    if (/[^A-Za-z0-9]/.test(password)) strength++;
    else feedback.push('Symbols');

    return { strength, feedback };
}

function updatePasswordStrength(password) {
    const strengthIndicator = document.getElementById('passwordStrength');
    const strengthBars = [
        document.getElementById('strength1'),
        document.getElementById('strength2'),
        document.getElementById('strength3'),
        document.getElementById('strength4')
    ];
    const strengthText = document.getElementById('strengthText');

    if (password.length === 0) {
        strengthIndicator.classList.add('hidden');
        return;
    }

    strengthIndicator.classList.remove('hidden');
    const { strength } = checkPasswordStrength(password);

    // Reset all bars
    strengthBars.forEach(bar => {
        bar.className = 'w-6 h-2 bg-gray-600 rounded';
    });

    // Update bars based on strength
    const colors = ['bg-red-500', 'bg-orange-500', 'bg-yellow-500', 'bg-green-500'];
    const texts = ['<%= t("auth.strength_low") %>', '<%= t("auth.strength_medium") %>', '<%= t("auth.strength_high") %>', '<%= t("auth.strength_very_high") %>'];
    const textColors = ['text-red-400', 'text-orange-400', 'text-yellow-400', 'text-green-400'];

    for (let i = 0; i < strength; i++) {
        strengthBars[i].className = `w-6 h-2 ${colors[Math.min(strength - 1, 3)]} rounded`;
    }

    strengthText.textContent = texts[Math.min(strength - 1, 3)] || '<%= t("auth.strength_very_low") %>';
    strengthText.className = textColors[Math.min(strength - 1, 3)] || 'text-red-400';
}

// Password confirmation validation
document.getElementById('confirmPassword').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;

    if (password !== confirmPassword) {
        this.setCustomValidity('Passwörter stimmen nicht überein');
        this.classList.add('border-red-500');
        this.classList.remove('border-gray-600');
    } else {
        this.setCustomValidity('');
        this.classList.remove('border-red-500');
        this.classList.add('border-gray-600');
    }
});

document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    updatePasswordStrength(password);

    const confirmPassword = document.getElementById('confirmPassword');
    if (confirmPassword.value) {
        confirmPassword.dispatchEvent(new Event('input'));
    }
});

// Form submission validation
document.querySelector('form').addEventListener('submit', function(e) {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    if (password !== confirmPassword) {
        e.preventDefault();
        alert('Die Passwörter stimmen nicht überein!');
        return false;
    }

    if (password.length < 6) {
        e.preventDefault();
        alert('Das Passwort muss mindestens 6 Zeichen lang sein!');
        return false;
    }
});
</script>

<%- include('../partials/footer') %>
