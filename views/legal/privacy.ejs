<%- include('../partials/header') %>

<body class="bg-gray-900 text-gray-100">
    <%- include('../partials/navbar') %>

    <div class="max-w-4xl mx-auto p-6">
        <!-- Breadcrumb -->
        <nav class="flex mb-8" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="/dashboard" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                        <i class="fas fa-home w-3 h-3 me-2.5"></i>
                        <%= t('nav.dashboard') %>
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400"><%= t('legal.privacy_policy') %></span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-4xl font-extrabold text-white mb-4">
                <i class="fas fa-shield-alt text-blue-500"></i> <%= t('legal.privacy_policy') %>
            </h1>
            <p class="text-xl text-gray-300"><%= t('legal.last_updated') %>: <%= new Date().toLocaleDateString() %></p>
        </div>

        <!-- Content -->
        <div class="bg-gray-800 rounded-lg shadow-lg border border-gray-700 p-8">
            <div class="prose prose-invert max-w-none">
                <h2 class="text-2xl font-bold text-white mb-4">1. <%= t('legal.information_collection') %></h2>
                <p class="text-gray-300 mb-4">
                    <%= t('legal.information_collection_text') %>
                </p>
                <ul class="list-disc list-inside text-gray-300 mb-6 space-y-2">
                    <li><%= t('legal.personal_info') %></li>
                    <li><%= t('legal.usage_data') %></li>
                    <li><%= t('legal.technical_data') %></li>
                </ul>

                <h2 class="text-2xl font-bold text-white mb-4">2. <%= t('legal.information_use') %></h2>
                <p class="text-gray-300 mb-4">
                    <%= t('legal.information_use_text') %>
                </p>
                <ul class="list-disc list-inside text-gray-300 mb-6 space-y-2">
                    <li><%= t('legal.provide_service') %></li>
                    <li><%= t('legal.improve_service') %></li>
                    <li><%= t('legal.communicate') %></li>
                    <li><%= t('legal.security') %></li>
                </ul>

                <h2 class="text-2xl font-bold text-white mb-4">3. <%= t('legal.information_sharing') %></h2>
                <p class="text-gray-300 mb-6">
                    <%= t('legal.information_sharing_text') %>
                </p>

                <h2 class="text-2xl font-bold text-white mb-4">4. <%= t('legal.data_security') %></h2>
                <p class="text-gray-300 mb-6">
                    <%= t('legal.data_security_text') %>
                </p>

                <h2 class="text-2xl font-bold text-white mb-4">5. <%= t('legal.cookies') %></h2>
                <p class="text-gray-300 mb-6">
                    <%= t('legal.cookies_text') %>
                </p>

                <h2 class="text-2xl font-bold text-white mb-4">6. <%= t('legal.your_rights') %></h2>
                <p class="text-gray-300 mb-4">
                    <%= t('legal.your_rights_text') %>
                </p>
                <ul class="list-disc list-inside text-gray-300 mb-6 space-y-2">
                    <li><%= t('legal.access_data') %></li>
                    <li><%= t('legal.correct_data') %></li>
                    <li><%= t('legal.delete_data') %></li>
                    <li><%= t('legal.data_portability') %></li>
                </ul>

                <h2 class="text-2xl font-bold text-white mb-4">7. <%= t('legal.data_retention') %></h2>
                <p class="text-gray-300 mb-6">
                    <%= t('legal.data_retention_text') %>
                </p>

                <h2 class="text-2xl font-bold text-white mb-4">8. <%= t('legal.children_privacy') %></h2>
                <p class="text-gray-300 mb-6">
                    <%= t('legal.children_privacy_text') %>
                </p>

                <h2 class="text-2xl font-bold text-white mb-4">9. <%= t('legal.changes_policy') %></h2>
                <p class="text-gray-300 mb-6">
                    <%= t('legal.changes_policy_text') %>
                </p>

                <h2 class="text-2xl font-bold text-white mb-4">10. <%= t('legal.contact') %></h2>
                <p class="text-gray-300 mb-6">
                    <%= t('legal.contact_privacy_text') %> <a href="/support" class="text-blue-400 hover:text-blue-300"><%= t('support.title') %></a>.
                </p>
            </div>
        </div>

        <!-- Back Button -->
        <div class="mt-8">
            <a href="/dashboard" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                <i class="fas fa-arrow-left mr-2"></i>
                <%= t('common.back') %>
            </a>
        </div>
    </div>

    <%- include('../partials/footer') %>
</body>
</html>
