<%- include('partials/header') %>
    <%- include('partials/navbar') %>

    <div class="max-w-screen-xl mx-auto p-4">
        <!-- Welcome Section -->
        <div class="mb-8">
            <div class="bg-gradient-to-br from-blue-600 via-purple-600 to-blue-800 rounded-2xl p-8 text-center relative overflow-hidden">
                <!-- Background Pattern -->
                <div class="absolute inset-0 opacity-10">
                    <div class="absolute top-0 left-0 w-full h-full" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.4"><circle cx="30" cy="30" r="2"/></g></svg></div>
                </div>

                <div class="relative">
                    <div class="inline-flex items-center justify-center w-20 h-20 bg-white bg-opacity-20 rounded-full mb-6">
                        <i class="fas fa-user-graduate text-white text-3xl"></i>
                    </div>
                    <h1 class="text-4xl font-extrabold text-white mb-4"><%= t('dashboard.welcome') %>, <%= username %>!</h1>
                    <p class="text-xl text-blue-100 mb-6 max-w-2xl mx-auto"><%= t('dashboard.progress_overview') %></p>
                    <div class="flex justify-center space-x-4 mt-6">
                        <div class="flex items-center text-blue-200">
                            <i class="fas fa-code text-2xl mr-2"></i>
                            <span class="text-sm">Interactive Learning</span>
                        </div>
                        <div class="flex items-center text-blue-200">
                            <i class="fas fa-trophy text-2xl mr-2"></i>
                            <span class="text-sm">Achievement System</span>
                        </div>
                        <div class="flex items-center text-blue-200">
                            <i class="fas fa-rocket text-2xl mr-2"></i>
                            <span class="text-sm">Progress Tracking</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistik Cards -->
        <%- include('partials/stats-cards', { stats: { completedLevels: userStats.completedLevels || 0, totalPoints: userStats.totalScore || 0, codeSolutions: userStats.completedLevels || 0 } }) %>

        <!-- Courses Section -->
        <div class="mb-8">
            <h2 class="text-3xl font-bold text-white mb-6">
                <i class="fas fa-graduation-cap text-blue-500"></i> <%= t('courses.title') %>
            </h2>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <% courses.forEach(course => { %>
                <div class="bg-gray-800 border border-gray-700 rounded-lg shadow-lg <%= course.isLocked ? 'opacity-75' : '' %>">
                    <div class="<%= course.isLocked ? 'bg-gray-600' : 'bg-blue-600' %> text-white p-4 rounded-t-lg relative">
                        <% if (course.isLocked) { %>
                            <div class="absolute top-2 right-2">
                                <span class="bg-red-600 text-white text-xs px-2 py-1 rounded-full flex items-center">
                                    <i class="fas fa-lock mr-1"></i>
                                    Premium
                                </span>
                            </div>
                        <% } %>
                        <h5 class="text-xl font-bold flex items-center">
                            <% if (course.slug === 'html-css-js') { %>
                                <i class="fab fa-html5 text-2xl mr-3"></i>
                            <% } else if (course.slug === 'javascript-advanced') { %>
                                <i class="fab fa-js-square text-2xl mr-3"></i>
                            <% } else if (course.slug === 'php') { %>
                                <i class="fab fa-php text-2xl mr-3"></i>
                            <% } else if (course.slug === 'python') { %>
                                <i class="fab fa-python text-2xl mr-3"></i>
                            <% } else if (course.slug === 'go') { %>
                                <i class="fas fa-bolt text-2xl mr-3"></i>
                            <% } else if (course.slug === 'java') { %>
                                <i class="fab fa-java text-2xl mr-3"></i>
                            <% } else { %>
                                <i class="fas fa-code text-2xl mr-3"></i>
                            <% } %>
                            <%= t('course.name.' + course.slug) %>
                        </h5>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-300 mb-4"><%= t('course.desc.' + course.slug) %></p>

                        <!-- Progress Bar -->
                        <div class="mb-4">
                            <div class="flex justify-between items-center mb-2">
                                <span class="text-sm text-gray-400"><%= t('courses.progress') %></span>
                                <span class="text-sm text-gray-400">
                                    <%= course.stats ? course.stats.completed_levels : 0 %> / <%= course.total_levels %> <%= t('courses.levels') %>
                                </span>
                            </div>
                            <div class="w-full bg-gray-700 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full" style="width: <%= course.progressPercentage || 0 %>%"></div>
                            </div>
                        </div>

                        <!-- Stats Grid -->
                        <div class="grid grid-cols-3 gap-4 mb-4">
                            <div class="text-center p-3 bg-gray-700 rounded-lg">
                                <div class="text-xl font-bold text-blue-400"><%= course.total_levels %></div>
                                <div class="text-xs text-gray-400"><%= t('courses.levels') %></div>
                            </div>
                            <div class="text-center p-3 bg-gray-700 rounded-lg">
                                <div class="text-xl font-bold text-green-400">
                                    <%= course.stats ? course.stats.completed_levels : 0 %>
                                </div>
                                <div class="text-xs text-gray-400"><%= t('progress.completed') %></div>
                            </div>
                            <div class="text-center p-3 bg-gray-700 rounded-lg">
                                <div class="text-xl font-bold text-yellow-400">
                                    <%= course.stats ? course.stats.total_score : 0 %>
                                </div>
                                <div class="text-xs text-gray-400"><%= t('common.points') %></div>
                            </div>
                        </div>

                        <!-- Next Level Info -->
                        <% if (course.nextLevel) { %>
                            <div class="mb-4 p-3 bg-blue-900 bg-opacity-30 rounded-lg border border-blue-700">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm text-blue-300 font-medium"><%= t('level.next_level') %>:</p>
                                        <p class="text-white">Level <%= course.nextLevel.level_number %>: <%= course.nextLevel.title %></p>
                                    </div>
                                    <a href="/course/<%= course.slug %>/level/<%= course.nextLevel.level_number %>" class="text-blue-400 hover:text-blue-300">
                                        <i class="fas fa-arrow-right"></i>
                                    </a>
                                </div>
                            </div>
                        <% } %>

                        <!-- Action Button -->
                        <% if (course.isLocked) { %>
                            <div class="w-full text-center">
                                <button disabled class="w-full text-gray-400 bg-gray-600 cursor-not-allowed font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center justify-center mb-2">
                                    <i class="fas fa-lock mr-2"></i>
                                    Premium Course
                                </button>
                                <p class="text-xs text-gray-400">Contact an administrator for access</p>
                            </div>
                        <% } else { %>
                            <a href="/course/<%= course.slug %>" class="w-full text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800 inline-flex items-center justify-center">
                                <i class="fas fa-play mr-2"></i>
                                <%= t("button.start_course") %>
                            </a>
                        <% } %>
                    </div>
                </div>
            <% }); %>
        </div>
        <div class="mb-8">
            <h2 class="text-3xl font-bold text-white mb-6">
                <i class="fas fa-chart-simple text-yellow-500"></i> <%= t('profile.statistics') %>
            </h2>
            <div class="bg-gray-800 rounded-lg p-8 border border-gray-700">
                <div class="text-center py-8">
                            <div class="grid grid-cols-4 gap-6 mb-4">
            <div class="col-md-3">
                <div class="card text-center border-0 bg-primary text-white">
                    <div class="card-body">
                        <div class="w-16 h-16 bg-yellow-600 bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-trophy text-yellow-400 text-2xl"></i>
                    </div>
                        <h4 class="card-title">0</h4>
                        <p class="card-text"><%= t("dashboard.level_completed") %></p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-0 bg-success text-white">
                    <div class="card-body">
                        <div class="w-16 h-16 bg-green-600 bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-star text-green-400 text-2xl"></i>
                    </div>
                        <h4 class="card-title">0</h4>
                        <p class="card-text"><%= t('dashboard.stats.total_score') %></p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-0 bg-warning text-white">
                    <div class="card-body">
                        <div class="w-16 h-16 bg-blue-600 bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-code text-blue-400 text-2xl"></i>
                    </div>
                        <h4 class="card-title">0</h4>
                        <p class="card-text">Code-Übungen</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center border-0 bg-info text-white">
                    <div class="card-body">
                        <div class="w-16 h-16 bg-red-600 bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-calendar text-red-400 text-2xl"></i>
                    </div>
                        <h4 class="card-title">0</h4>
                        <p class="card-text">Tage aktiv</p>
                    </div>
                </div>
            </div>
        </div>
        </div>
        </div>
            </div>

        <!-- Recent Activity -->
        <div class="mb-8">
            <h2 class="text-3xl font-bold text-white mb-6">
                <i class="fas fa-history text-green-500"></i> <%= t('dashboard.recent_activities') %>
            </h2>
            <div class="bg-gray-800 rounded-lg border border-gray-700">
                <% if (activities && activities.length > 0) { %>
                    <div class="divide-y divide-gray-700">
                        <% activities.forEach(activity => { %>
                            <div class="p-6 flex items-center space-x-4">
                                <div class="flex-shrink-0">
                                    <% if (activity.activity_type === 'level_completed') { %>
                                        <div class="w-12 h-12 bg-green-500 bg-opacity-20 rounded-full flex items-center justify-center">
                                            <i class="fas fa-check text-green-400 text-lg"></i>
                                        </div>
                                    <% } else if (activity.activity_type === 'achievement_earned') { %>
                                        <div class="w-12 h-12 bg-yellow-500 bg-opacity-20 rounded-full flex items-center justify-center">
                                            <i class="fas fa-trophy text-yellow-400 text-lg"></i>
                                        </div>
                                    <% } %>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <% if (activity.activity_type === 'level_completed') { %>
                                        <p class="text-white font-medium">
                                            Level <%= activity.level_number %> abgeschlossen: <span class="text-blue-400"><%= activity.activity_title %></span>
                                        </p>
                                        <p class="text-gray-400 text-sm">
                                            <i class="fas fa-book mr-1"></i><%= activity.course_name %>
                                        </p>
                                    <% } else if (activity.activity_type === 'achievement_earned') { %>
                                        <p class="text-white font-medium">
                                            <i class="fas fa-trophy text-yellow-400 mr-1"></i><%= t("dashboard.achievement_earned") %>: <span class="text-yellow-400"><%= activity.activity_title %></span>
                                        </p>
                                        <p class="text-gray-400 text-sm">
                                            Achievement erhalten
                                        </p>
                                    <% } %>
                                </div>
                                <div class="flex-shrink-0 text-right">
                                    <div class="text-green-400 font-medium">
                                        +<%= activity.points %> <%= t('dashboard.points') %>
                                    </div>
                                    <div class="text-gray-400 text-sm">
                                        <%= new Date(activity.activity_date).toLocaleDateString('de-DE', {
                                            day: '2-digit',
                                            month: '2-digit',
                                            hour: '2-digit',
                                            minute: '2-digit'
                                        }) %>
                                    </div>
                                </div>
                            </div>
                        <% }); %>
                    </div>
                <% } else { %>
                    <div class="p-8">
                        <div class="text-center py-8">
                            <div class="w-16 h-16 bg-blue-600 bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-info-circle text-blue-400 text-2xl"></i>
                            </div>
                            <p class="text-gray-400 text-lg">
                                Noch keine Aktivitäten. Starte deinen ersten Kurs!
                            </p>
                        </div>
                    </div>
                <% } %>
            </div>
        </div>
    </div>

<%- include('partials/footer') %>
