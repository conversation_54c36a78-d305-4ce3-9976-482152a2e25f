<%- include('../partials/header') %>

<body class="bg-gray-900 min-h-screen">
    <%- include('../partials/navbar') %>

    <div class="container mx-auto px-4 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
            <div class="lg:col-span-1">
                <%- include('../partials/admin-sidebar') %>
            </div>
            <div class="lg:col-span-3">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-3xl font-bold text-white">
                        <i class="fas fa-envelope mr-3 text-orange-400"></i>Email Management
                    </h2>
                </div>

                <% if (success) { %>
                    <div class="bg-green-900 bg-opacity-50 border border-green-700 text-green-300 px-4 py-3 rounded-lg mb-6 flex items-center">
                        <i class="fas fa-check-circle mr-3"></i>
                        <span><%= success %></span>
                    </div>
                <% } %>

                <% if (error) { %>
                    <div class="bg-red-900 bg-opacity-50 border border-red-700 text-red-300 px-4 py-3 rounded-lg mb-6 flex items-center">
                        <i class="fas fa-exclamation-triangle mr-3"></i>
                        <span><%= error %></span>
                    </div>
                <% } %>

                <div class="bg-gray-800 rounded-xl border border-gray-700">
                    <div class="px-6 py-4 border-b border-gray-700">
                        <h5 class="text-xl font-bold text-white flex items-center">
                            <i class="fas fa-paper-plane mr-3 text-orange-400"></i>Email senden
                        </h5>
                    </div>
                    <div class="p-6">
                        <div class="bg-blue-900 bg-opacity-50 border border-blue-700 text-blue-300 px-4 py-3 rounded-lg mb-6 flex items-center">
                            <i class="fas fa-info-circle mr-3"></i>
                            <div>
                                <strong>Hinweis:</strong> Du kannst nur von @codewave.online Email-Adressen senden.
                                Stelle sicher, dass die Absender-Adresse in deinem Resend-Account konfiguriert ist.
                            </div>
                        </div>

                        <form method="POST" action="/admin/emails/send" class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="fromEmailPrefix" class="block text-sm font-medium text-gray-300 mb-2">
                                        <i class="fas fa-user mr-2 text-orange-400"></i>Von (Absender)
                                    </label>
                                    <div class="flex">
                                        <input type="text"
                                               class="flex-1 px-4 py-3 bg-gray-700 border border-gray-600 rounded-l-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                                               id="fromEmailPrefix"
                                               name="fromEmailPrefix"
                                               placeholder="noreply"
                                               required
                                               pattern="[a-zA-Z0-9._-]+">
                                        <span class="px-4 py-3 bg-gray-600 border border-l-0 border-gray-600 rounded-r-lg text-gray-300">@codewave.online</span>
                                    </div>
                                    <p class="text-sm text-gray-400 mt-1">Beispiele: noreply, support, admin, info</p>
                                </div>
                                <div>
                                    <label for="toEmail" class="block text-sm font-medium text-gray-300 mb-2">
                                        <i class="fas fa-envelope mr-2 text-orange-400"></i>An (Empfänger)
                                    </label>
                                    <input type="email"
                                           class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                                           id="toEmail"
                                           name="toEmail"
                                           placeholder="<EMAIL>"
                                           required>
                                </div>
                            </div>

                            <div>
                                <label for="subject" class="block text-sm font-medium text-gray-300 mb-2">
                                    <i class="fas fa-tag mr-2 text-orange-400"></i>Betreff
                                </label>
                                <input type="text"
                                       class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                                       id="subject"
                                       name="subject"
                                       placeholder="Email-Betreff"
                                       required
                                       maxlength="200">
                            </div>

                            <div>
                                <label for="message" class="block text-sm font-medium text-gray-300 mb-2">
                                    <i class="fas fa-comment mr-2 text-orange-400"></i>Nachricht
                                </label>
                                <textarea class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                                          id="message"
                                          name="message"
                                          rows="8"
                                          placeholder="Deine Nachricht hier..."
                                          required></textarea>
                                <p class="text-sm text-gray-400 mt-1">Die Nachricht wird automatisch in ein schönes HTML-Template eingebettet.</p>
                            </div>

                            <div class="flex flex-col sm:flex-row gap-4 sm:justify-end">
                                <button type="button"
                                        class="px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors duration-200 flex items-center justify-center"
                                        onclick="clearForm()">
                                    <i class="fas fa-eraser mr-2"></i>Formular leeren
                                </button>
                                <button type="submit"
                                        class="px-6 py-3 bg-orange-600 hover:bg-orange-700 text-white rounded-lg transition-colors duration-200 flex items-center justify-center">
                                    <i class="fas fa-paper-plane mr-2"></i>Email senden
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="bg-gray-800 rounded-xl border border-gray-700 mt-8">
                    <div class="px-6 py-4 border-b border-gray-700">
                        <h5 class="text-xl font-bold text-white flex items-center">
                            <i class="fas fa-info-circle mr-3 text-blue-400"></i>Email-Konfiguration
                        </h5>
                    </div>
                    <div class="p-6">
                        <h6 class="text-lg font-semibold text-white mb-4">Voraussetzungen für den Email-Versand:</h6>
                        <ul class="space-y-2 text-gray-300 mb-6">
                            <li class="flex items-start">
                                <i class="fas fa-key text-orange-400 mr-3 mt-1"></i>
                                <div>
                                    <strong>Resend API Key:</strong> Muss in den Umgebungsvariablen gesetzt sein (<code class="bg-gray-700 px-2 py-1 rounded text-orange-300">RESEND_API_KEY</code>)
                                </div>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-globe text-orange-400 mr-3 mt-1"></i>
                                <div>
                                    <strong>Domain-Verifikation:</strong> codewave.online muss in deinem Resend-Account verifiziert sein
                                </div>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-envelope text-orange-400 mr-3 mt-1"></i>
                                <div>
                                    <strong>Absender-Adressen:</strong> Die verwendeten @codewave.online Adressen müssen konfiguriert sein
                                </div>
                            </li>
                        </ul>

                        <div class="bg-yellow-900 bg-opacity-50 border border-yellow-700 text-yellow-300 px-4 py-3 rounded-lg flex items-start">
                            <i class="fas fa-exclamation-triangle mr-3 mt-1"></i>
                            <div>
                                <strong>Wichtig:</strong> Verwende diese Funktion verantwortungsvoll und nur für legitime Geschäftszwecke.
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
    // Auto-construct the full from email
    document.getElementById('fromEmailPrefix').addEventListener('input', function() {
        const prefix = this.value;
        const fullEmail = prefix + '@codewave.online';
        // Store the full email in a hidden field
        let hiddenField = document.getElementById('fromEmail');
        if (!hiddenField) {
            hiddenField = document.createElement('input');
            hiddenField.type = 'hidden';
            hiddenField.id = 'fromEmail';
            hiddenField.name = 'fromEmail';
            this.form.appendChild(hiddenField);
        }
        hiddenField.value = fullEmail;
    });

    // Clear form function
    function clearForm() {
        document.getElementById('fromEmailPrefix').value = '';
        document.getElementById('toEmail').value = '';
        document.getElementById('subject').value = '';
        document.getElementById('message').value = '';
        const hiddenField = document.getElementById('fromEmail');
        if (hiddenField) {
            hiddenField.value = '';
        }
    }

    // Initialize the hidden field on page load
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('fromEmailPrefix').dispatchEvent(new Event('input'));
    });
    </script>
</body>
</html>
