<%- include('../partials/header') %>
<body class="bg-gray-900 min-h-screen">
    <%- include('../partials/navbar') %>
    
    <div class="container mx-auto px-4 py-8">
        <!-- Admin Header -->
        <div class="mb-8">
            <div class="bg-gradient-to-r from-red-600 to-purple-600 rounded-2xl p-8 text-center relative overflow-hidden">
                <div class="absolute inset-0 opacity-10">
                    <div class="absolute top-0 left-0 w-full h-full" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.4"><circle cx="30" cy="30" r="2"/></g></svg>'); background-size: 60px 60px;"></div>
                </div>
                
                <div class="relative">
                    <div class="inline-flex items-center justify-center w-20 h-20 bg-white bg-opacity-20 rounded-full mb-6">
                        <i class="fas fa-shield-alt text-white text-3xl"></i>
                    </div>
                    <h1 class="text-4xl font-extrabold text-white mb-4">Admin Dashboard</h1>
                    <p class="text-xl text-red-100 mb-6 max-w-2xl mx-auto">Manage users, courses, and platform settings</p>
                    <div class="flex justify-center space-x-4 mt-6">
                        <div class="flex items-center text-red-200">
                            <i class="fas fa-users text-2xl mr-2"></i>
                            <span class="text-sm">User Management</span>
                        </div>
                        <div class="flex items-center text-red-200">
                            <i class="fas fa-graduation-cap text-2xl mr-2"></i>
                            <span class="text-sm">Course Control</span>
                        </div>
                        <div class="flex items-center text-red-200">
                            <i class="fas fa-cog text-2xl mr-2"></i>
                            <span class="text-sm">Settings</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-gray-800 rounded-xl p-6 border border-gray-700">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm font-medium">Total Users</p>
                        <p class="text-3xl font-bold text-white"><%= stats.totalUsers %></p>
                    </div>
                    <div class="w-12 h-12 bg-blue-600 bg-opacity-20 rounded-full flex items-center justify-center">
                        <i class="fas fa-users text-blue-400 text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-gray-800 rounded-xl p-6 border border-gray-700">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm font-medium">Admin Users</p>
                        <p class="text-3xl font-bold text-white"><%= stats.adminUsers %></p>
                    </div>
                    <div class="w-12 h-12 bg-purple-600 bg-opacity-20 rounded-full flex items-center justify-center">
                        <i class="fas fa-shield-alt text-purple-400 text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-gray-800 rounded-xl p-6 border border-gray-700">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm font-medium">Banned Users</p>
                        <p class="text-3xl font-bold text-white"><%= stats.bannedUsers %></p>
                    </div>
                    <div class="w-12 h-12 bg-red-600 bg-opacity-20 rounded-full flex items-center justify-center">
                        <i class="fas fa-ban text-red-400 text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-gray-800 rounded-xl p-6 border border-gray-700">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm font-medium">Total Courses</p>
                        <p class="text-3xl font-bold text-white"><%= stats.totalCourses %></p>
                    </div>
                    <div class="w-12 h-12 bg-green-600 bg-opacity-20 rounded-full flex items-center justify-center">
                        <i class="fas fa-graduation-cap text-green-400 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Global Settings -->
            <div class="bg-gray-800 rounded-xl p-6 border border-gray-700">
                <h3 class="text-xl font-bold text-white mb-4 flex items-center">
                    <i class="fas fa-cog text-blue-400 mr-3"></i>
                    Global Settings
                </h3>
                
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-4 bg-gray-700 rounded-lg">
                        <div>
                            <h4 class="text-white font-medium">Premium Courses Access</h4>
                            <p class="text-gray-400 text-sm">Enable premium courses for all users</p>
                        </div>
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" id="premiumToggle" class="sr-only peer" <%= stats.globalPremiumEnabled ? 'checked' : '' %>>
                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                    </div>
                </div>
            </div>

            <!-- Quick Navigation -->
            <div class="bg-gray-800 rounded-xl p-6 border border-gray-700">
                <h3 class="text-xl font-bold text-white mb-4 flex items-center">
                    <i class="fas fa-tachometer-alt text-green-400 mr-3"></i>
                    Quick Actions
                </h3>
                
                <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                    <a href="/admin/users" class="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg text-center transition-colors duration-200">
                        <i class="fas fa-users text-2xl mb-2"></i>
                        <p class="font-medium">Manage Users</p>
                    </a>

                    <a href="/admin/emails" class="bg-orange-600 hover:bg-orange-700 text-white p-4 rounded-lg text-center transition-colors duration-200">
                        <i class="fas fa-envelope text-2xl mb-2"></i>
                        <p class="font-medium">Send Emails</p>
                    </a>

                    <a href="/admin/support" class="bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg text-center transition-colors duration-200">
                        <i class="fas fa-headset text-2xl mb-2"></i>
                        <p class="font-medium">Support Tickets</p>
                    </a>

                    <a href="/admin/audit" class="bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-lg text-center transition-colors duration-200">
                        <i class="fas fa-history text-2xl mb-2"></i>
                        <p class="font-medium">Audit Log</p>
                    </a>
                </div>
            </div>
        </div>

        <!-- Recent Users and Audit Log -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Recent Users -->
            <div class="bg-gray-800 rounded-xl p-6 border border-gray-700">
                <h3 class="text-xl font-bold text-white mb-4 flex items-center">
                    <i class="fas fa-user-plus text-blue-400 mr-3"></i>
                    Recent Users
                </h3>
                
                <div class="space-y-3">
                    <% users.forEach(user => { %>
                        <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                            <div class="flex items-center">
                                <div class="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-white font-bold"><%= user.username.charAt(0).toUpperCase() %></span>
                                </div>
                                <div>
                                    <p class="text-white font-medium"><%= user.username %></p>
                                    <p class="text-gray-400 text-sm"><%= new Date(user.created_at).toLocaleDateString() %></p>
                                </div>
                            </div>
                            <div class="flex space-x-2">
                                <% if (user.is_admin) { %>
                                    <span class="px-2 py-1 bg-purple-600 text-white text-xs rounded-full">Admin</span>
                                <% } %>
                                <% if (user.is_banned) { %>
                                    <span class="px-2 py-1 bg-red-600 text-white text-xs rounded-full">Banned</span>
                                <% } %>
                            </div>
                        </div>
                    <% }); %>
                </div>
                
                <div class="mt-4">
                    <a href="/admin/users" class="text-blue-400 hover:text-blue-300 text-sm font-medium">View all users →</a>
                </div>
            </div>

            <!-- Recent Audit Log -->
            <div class="bg-gray-800 rounded-xl p-6 border border-gray-700">
                <h3 class="text-xl font-bold text-white mb-4 flex items-center">
                    <i class="fas fa-history text-purple-400 mr-3"></i>
                    Recent Activity
                </h3>
                
                <div class="space-y-3">
                    <% auditLog.slice(0, 5).forEach(log => { %>
                        <div class="p-3 bg-gray-700 rounded-lg">
                            <div class="flex items-center justify-between mb-1">
                                <p class="text-white font-medium"><%= log.admin_username %></p>
                                <p class="text-gray-400 text-xs"><%= new Date(log.timestamp).toLocaleString() %></p>
                            </div>
                            <p class="text-gray-300 text-sm"><%= log.details %></p>
                        </div>
                    <% }); %>
                </div>
                
                <div class="mt-4">
                    <a href="/admin/audit" class="text-purple-400 hover:text-purple-300 text-sm font-medium">View full audit log →</a>
                </div>
            </div>
        </div>
    </div>

    <%- include('../partials/footer') %>

    <script>
        // Premium toggle functionality
        document.getElementById('premiumToggle').addEventListener('change', function() {
            const enabled = this.checked;
            
            fetch('/admin/settings/premium', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ enabled: enabled })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success notification
                    const notification = document.createElement('div');
                    notification.className = 'fixed top-4 right-4 bg-green-600 text-white px-6 py-3 rounded-lg shadow-lg z-50';
                    notification.textContent = `Premium courses ${enabled ? 'enabled' : 'disabled'} for all users`;
                    document.body.appendChild(notification);
                    
                    setTimeout(() => {
                        notification.remove();
                    }, 3000);
                } else {
                    alert('Error updating setting: ' + data.error);
                    this.checked = !enabled; // Revert toggle
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error updating setting');
                this.checked = !enabled; // Revert toggle
            });
        });
    </script>
</body>
</html>
