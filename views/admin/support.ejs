<%- include('../partials/header') %>

<body class="bg-gray-900 text-gray-100">
    <%- include('../partials/navbar') %>

    <div class="max-w-screen-xl mx-auto p-4">
        <!-- Breadcrumb -->
        <nav class="flex mb-8" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="/dashboard" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                        <i class="fas fa-home w-3 h-3 me-2.5"></i>
                        <%= t('nav.dashboard') %>
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                        </svg>
                        <a href="/admin" class="ms-1 text-sm font-medium text-gray-500 hover:text-blue-600 md:ms-2 dark:text-gray-400 dark:hover:text-white">Admin</a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400"><%= t('support.title') %></span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Header -->
        <div class="mb-8 flex justify-between items-center">
            <div>
                <h1 class="text-4xl font-extrabold text-white mb-2">
                    <i class="fas fa-headset text-blue-500"></i> 
                    <%= t('admin.support_management') %>
                </h1>
                <p class="text-xl text-gray-300"><%= t('admin.support_management_desc') %></p>
            </div>
            <div class="flex space-x-4">
                <div class="bg-gray-800 px-4 py-2 rounded-lg border border-gray-700">
                    <span class="text-sm text-gray-400"><%= t('support.total_tickets') %>:</span>
                    <span class="text-lg font-bold text-white ml-2"><%= tickets.length %></span>
                </div>
                <div class="bg-gray-800 px-4 py-2 rounded-lg border border-gray-700">
                    <span class="text-sm text-gray-400"><%= t('support.open_tickets') %>:</span>
                    <span class="text-lg font-bold text-green-400 ml-2"><%= tickets.filter(t => t.status === 'open').length %></span>
                </div>
            </div>
        </div>

        <!-- Filter Tabs -->
        <div class="mb-6">
            <div class="border-b border-gray-700">
                <nav class="-mb-px flex space-x-8">
                    <button onclick="filterTickets('all')" class="filter-tab active border-b-2 border-blue-500 py-2 px-1 text-sm font-medium text-blue-400">
                        <%= t('common.all') %> (<%= tickets.length %>)
                    </button>
                    <button onclick="filterTickets('open')" class="filter-tab border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-400 hover:text-gray-300 hover:border-gray-300">
                        <%= t('support.status_open') %> (<%= tickets.filter(t => t.status === 'open').length %>)
                    </button>
                    <button onclick="filterTickets('pending')" class="filter-tab border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-400 hover:text-gray-300 hover:border-gray-300">
                        <%= t('support.status_pending') %> (<%= tickets.filter(t => t.status === 'pending').length %>)
                    </button>
                    <button onclick="filterTickets('closed')" class="filter-tab border-b-2 border-transparent py-2 px-1 text-sm font-medium text-gray-400 hover:text-gray-300 hover:border-gray-300">
                        <%= t('support.status_closed') %> (<%= tickets.filter(t => t.status === 'closed').length %>)
                    </button>
                </nav>
            </div>
        </div>

        <!-- Tickets Table -->
        <div class="bg-gray-800 rounded-lg shadow-lg border border-gray-700 overflow-hidden">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left text-gray-300">
                    <thead class="text-xs text-gray-400 uppercase bg-gray-700">
                        <tr>
                            <th scope="col" class="px-6 py-3">ID</th>
                            <th scope="col" class="px-6 py-3"><%= t('support.title_col') %></th>
                            <th scope="col" class="px-6 py-3"><%= t('support.user') %></th>
                            <th scope="col" class="px-6 py-3"><%= t('support.status') %></th>
                            <th scope="col" class="px-6 py-3"><%= t('support.messages') %></th>
                            <th scope="col" class="px-6 py-3"><%= t('support.last_update') %></th>
                            <th scope="col" class="px-6 py-3"><%= t('support.actions') %></th>
                        </tr>
                    </thead>
                    <tbody>
                        <% if (tickets.length === 0) { %>
                            <tr>
                                <td colspan="7" class="px-6 py-8 text-center text-gray-400">
                                    <i class="fas fa-inbox text-4xl mb-4"></i>
                                    <p><%= t('support.no_tickets') %></p>
                                </td>
                            </tr>
                        <% } else { %>
                            <% tickets.forEach(ticket => { %>
                                <tr class="ticket-row bg-gray-800 border-b border-gray-700 hover:bg-gray-750" data-status="<%= ticket.status %>">
                                    <td class="px-6 py-4 font-medium text-white">#<%= ticket.id %></td>
                                    <td class="px-6 py-4">
                                        <div class="max-w-xs truncate" title="<%= ticket.title %>">
                                            <%= ticket.title %>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            <i class="fas fa-user text-gray-400 mr-2"></i>
                                            <%= ticket.username %>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <span class="px-2 py-1 text-xs rounded-full <%= ticket.status === 'open' ? 'bg-green-600 text-white' : ticket.status === 'closed' ? 'bg-red-600 text-white' : 'bg-yellow-600 text-white' %>">
                                            <%= t('support.status_' + ticket.status) %>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4">
                                        <span class="bg-gray-700 text-gray-300 px-2 py-1 rounded text-xs">
                                            <%= ticket.message_count || 0 %>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 text-gray-400">
                                        <%= new Date(ticket.updated_at || ticket.created_at).toLocaleDateString() %>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="flex space-x-2">
                                            <a href="/admin/support/<%= ticket.id %>" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs transition-colors duration-200">
                                                <i class="fas fa-eye mr-1"></i>
                                                <%= t('support.view') %>
                                            </a>
                                            <% if (ticket.status !== 'closed') { %>
                                                <button onclick="quickCloseTicket(<%= ticket.id %>)" class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs transition-colors duration-200">
                                                    <i class="fas fa-times mr-1"></i>
                                                    <%= t('common.close') %>
                                                </button>
                                            <% } %>
                                        </div>
                                    </td>
                                </tr>
                            <% }); %>
                        <% } %>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Back to Admin -->
        <div class="mt-8">
            <a href="/admin" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200">
                <i class="fas fa-arrow-left mr-2"></i>
                <%= t('admin.back_to_dashboard') %>
            </a>
        </div>
    </div>

    <script>
        function filterTickets(status) {
            const rows = document.querySelectorAll('.ticket-row');
            const tabs = document.querySelectorAll('.filter-tab');
            
            // Update tab styles
            tabs.forEach(tab => {
                tab.classList.remove('active', 'border-blue-500', 'text-blue-400');
                tab.classList.add('border-transparent', 'text-gray-400');
            });
            
            event.target.classList.add('active', 'border-blue-500', 'text-blue-400');
            event.target.classList.remove('border-transparent', 'text-gray-400');
            
            // Filter rows
            rows.forEach(row => {
                if (status === 'all' || row.dataset.status === status) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        function quickCloseTicket(ticketId) {
            if (confirm('<%= t("admin.confirm_close_ticket") %>')) {
                fetch(`/admin/support/${ticketId}/status`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ status: 'closed' })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + (data.error || 'Unknown error'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Network error');
                });
            }
        }
    </script>

    <%- include('../partials/footer') %>
</body>
</html>
