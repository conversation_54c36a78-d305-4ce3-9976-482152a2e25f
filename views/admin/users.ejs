<%- include('../partials/header') %>
<body class="bg-gray-900 min-h-screen">
    <%- include('../partials/navbar') %>
    
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-white mb-2">User Management</h1>
                    <p class="text-gray-400">Manage user accounts, permissions, and course access</p>
                </div>
                <a href="/admin" class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>

        <!-- Users Table -->
        <div class="bg-gray-800 rounded-xl border border-gray-700 overflow-hidden">
            <div class="p-6 border-b border-gray-700">
                <h2 class="text-xl font-bold text-white">All Users</h2>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">User</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Email</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Course Access</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-700">
                        <% users.forEach(user => { %>
                            <tr class="hover:bg-gray-700 transition-colors duration-200" data-user-id="<%= user.id %>">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mr-3">
                                            <span class="text-white font-bold"><%= user.username.charAt(0).toUpperCase() %></span>
                                        </div>
                                        <div>
                                            <div class="text-white font-medium"><%= user.username %></div>
                                            <div class="text-gray-400 text-sm">Joined <%= new Date(user.created_at).toLocaleDateString() %></div>
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-gray-300"><%= user.email %></div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex space-x-2">
                                        <% if (user.is_admin) { %>
                                            <span class="px-2 py-1 bg-purple-600 text-white text-xs rounded-full">Admin</span>
                                        <% } %>
                                        <% if (user.is_banned) { %>
                                            <span class="px-2 py-1 bg-red-600 text-white text-xs rounded-full">Banned</span>
                                        <% } else { %>
                                            <span class="px-2 py-1 bg-green-600 text-white text-xs rounded-full">Active</span>
                                        <% } %>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex flex-wrap gap-1">
                                        <% courses.forEach(course => { %>
                                            <% const hasAccess = course.slug === 'html-css-js' || course.slug === 'javascript' || course.slug === 'python' || user.courseAccess.includes(course.id); %>
                                            <span class="px-2 py-1 text-xs rounded-full <%= hasAccess ? 'bg-green-600 text-white' : 'bg-gray-600 text-gray-300' %>">
                                                <%= course.name %>
                                            </span>
                                        <% }); %>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="flex space-x-2">
                                        <!-- Admin Toggle -->
                                        <label class="relative inline-flex items-center cursor-pointer" title="<%= user.is_admin ? 'Remove Admin' : 'Make Admin' %>">
                                            <input type="checkbox" class="sr-only peer admin-toggle" data-user-id="<%= user.id %>" <%= user.is_admin ? 'checked' : '' %>>
                                            <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
                                            <span class="ml-2 text-xs text-gray-300">
                                                <i class="fas fa-shield-alt"></i>
                                            </span>
                                        </label>
                                        
                                        <!-- Ban Toggle -->
                                        <button onclick="toggleBan(<%= user.id %>, <%= !user.is_banned %>)" 
                                                class="<%= user.is_banned ? 'bg-red-600 hover:bg-red-700' : 'bg-yellow-600 hover:bg-yellow-700' %> text-white px-3 py-1 rounded text-xs transition-colors duration-200"
                                                title="<%= user.is_banned ? 'Unban User' : 'Ban User' %>">
                                            <i class="fas fa-ban"></i>
                                        </button>
                                        
                                        <!-- Course Access -->
                                        <button onclick="showCourseModal(<%= user.id %>, '<%= user.username %>')" 
                                                class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-xs transition-colors duration-200"
                                                title="Manage Course Access">
                                            <i class="fas fa-graduation-cap"></i>
                                        </button>
                                        
                                        <!-- Delete User -->
                                        <% if (user.id !== user.id) { %>
                                            <button onclick="deleteUser(<%= user.id %>, '<%= user.username %>')" 
                                                    class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-xs transition-colors duration-200"
                                                    title="Delete User">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        <% } %>
                                    </div>
                                </td>
                            </tr>
                        <% }); %>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Course Access Modal -->
    <div id="courseModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
        <div class="bg-gray-800 rounded-xl p-6 max-w-md w-full mx-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-xl font-bold text-white">Manage Course Access</h3>
                <button onclick="closeCourseModal()" class="text-gray-400 hover:text-white">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <div id="courseModalContent">
                <!-- Content will be populated by JavaScript -->
            </div>
            
            <div class="flex justify-end space-x-3 mt-6">
                <button onclick="closeCourseModal()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded transition-colors duration-200">
                    Cancel
                </button>
                <button onclick="saveCourseAccess()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded transition-colors duration-200">
                    Save Changes
                </button>
            </div>
        </div>
    </div>

    <%- include('../partials/footer') %>

    <script>
        let currentUserId = null;
        let currentUserAccess = [];

        // Admin toggle event listeners
        document.addEventListener('DOMContentLoaded', function() {
            const adminToggles = document.querySelectorAll('.admin-toggle');
            adminToggles.forEach(toggle => {
                toggle.addEventListener('change', function() {
                    const userId = this.dataset.userId;
                    const makeAdmin = this.checked;

                    console.log('Admin toggle changed:', userId, makeAdmin);

                    if (confirm(`Are you sure you want to ${makeAdmin ? 'grant admin privileges to' : 'remove admin privileges from'} this user?`)) {
                        console.log('Sending admin toggle request...');
                        fetch(`/admin/users/${userId}/admin`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ isAdmin: makeAdmin })
                        })
                        .then(response => {
                            console.log('Response status:', response.status);
                            return response.json();
                        })
                        .then(data => {
                            console.log('Response data:', data);
                            if (data.success) {
                                // Success - toggle stays in new position
                                console.log('Admin status updated successfully');
                            } else {
                                // Error - revert toggle
                                this.checked = !makeAdmin;
                                alert('Error: ' + (data.error || 'Unknown error'));
                            }
                        })
                        .catch(error => {
                            console.error('Fetch error:', error);
                            // Error - revert toggle
                            this.checked = !makeAdmin;
                            alert('Network error: ' + error.message);
                        });
                    } else {
                        // User cancelled - revert toggle
                        this.checked = !makeAdmin;
                    }
                });
            });
        });

        function toggleBan(userId, banUser) {
            if (confirm(`Are you sure you want to ${banUser ? 'ban' : 'unban'} this user?`)) {
                fetch(`/admin/users/${userId}/ban`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ isBanned: banUser })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.error);
                    }
                });
            }
        }

        function deleteUser(userId, username) {
            if (confirm(`Are you sure you want to permanently delete user "${username}"? This action cannot be undone.`)) {
                fetch(`/admin/users/${userId}`, {
                    method: 'DELETE',
                    headers: { 'Content-Type': 'application/json' }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('Error: ' + data.error);
                    }
                });
            }
        }

        function showCourseModal(userId, username) {
            currentUserId = userId;
            
            // Get current user access
            const userRow = document.querySelector(`tr[data-user-id="${userId}"]`);
            const accessSpans = userRow.querySelectorAll('td:nth-child(4) span');
            currentUserAccess = [];
            
            const courses = <%- JSON.stringify(courses) %>;
            const users = <%- JSON.stringify(users) %>;
            const user = users.find(u => u.id === userId);
            
            let modalContent = `<p class="text-gray-300 mb-4">Managing course access for <strong>${username}</strong></p>`;
            modalContent += '<div class="space-y-3">';
            
            courses.forEach(course => {
                const isFree = ['html-css-js', 'javascript', 'python'].includes(course.slug);
                const hasAccess = isFree || user.courseAccess.includes(course.id);
                
                if (!isFree) {
                    modalContent += `
                        <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg">
                            <span class="text-white">${course.name}</span>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" class="sr-only peer course-checkbox" data-course-id="${course.id}" ${hasAccess ? 'checked' : ''}>
                                <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-800 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                            </label>
                        </div>
                    `;
                } else {
                    modalContent += `
                        <div class="flex items-center justify-between p-3 bg-gray-700 rounded-lg opacity-50">
                            <span class="text-white">${course.name}</span>
                            <span class="text-green-400 text-sm">Free Course</span>
                        </div>
                    `;
                }
            });
            
            modalContent += '</div>';
            
            document.getElementById('courseModalContent').innerHTML = modalContent;
            document.getElementById('courseModal').classList.remove('hidden');
        }

        function closeCourseModal() {
            document.getElementById('courseModal').classList.add('hidden');
            currentUserId = null;
        }

        function saveCourseAccess() {
            const checkboxes = document.querySelectorAll('.course-checkbox');
            const promises = [];
            
            checkboxes.forEach(checkbox => {
                const courseId = parseInt(checkbox.dataset.courseId);
                const hasAccess = checkbox.checked;
                
                promises.push(
                    fetch(`/admin/users/${currentUserId}/course-access`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ courseId, grant: hasAccess })
                    })
                );
            });
            
            Promise.all(promises)
                .then(responses => Promise.all(responses.map(r => r.json())))
                .then(results => {
                    const allSuccess = results.every(r => r.success);
                    if (allSuccess) {
                        closeCourseModal();
                        location.reload();
                    } else {
                        alert('Some changes failed to save');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error saving changes');
                });
        }
    </script>
</body>
</html>
