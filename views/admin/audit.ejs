<%- include('../partials/header') %>
<body class="bg-gray-900 min-h-screen">
    <%- include('../partials/navbar') %>
    
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-white mb-2">Audit Log</h1>
                    <p class="text-gray-400">Track all administrative actions and changes</p>
                </div>
                <a href="/admin" class="bg-gray-700 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Dashboard
                </a>
            </div>
        </div>

        <!-- Audit Log Table -->
        <div class="bg-gray-800 rounded-xl border border-gray-700 overflow-hidden">
            <div class="p-6 border-b border-gray-700">
                <h2 class="text-xl font-bold text-white">Administrative Actions</h2>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Timestamp</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Admin</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Action</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Target</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">Details</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-gray-700">
                        <% if (auditLog.length === 0) { %>
                            <tr>
                                <td colspan="5" class="px-6 py-8 text-center text-gray-400">
                                    <i class="fas fa-history text-4xl mb-4"></i>
                                    <p>No audit log entries found</p>
                                </td>
                            </tr>
                        <% } else { %>
                            <% auditLog.forEach(log => { %>
                                <tr class="hover:bg-gray-700 transition-colors duration-200">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-gray-300 text-sm">
                                            <%= new Date(log.timestamp).toLocaleString() %>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full flex items-center justify-center mr-3">
                                                <span class="text-white font-bold text-sm"><%= log.admin_username ? log.admin_username.charAt(0).toUpperCase() : 'S' %></span>
                                            </div>
                                            <div class="text-white font-medium"><%= log.admin_username || 'System' %></div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <% 
                                        let actionColor = 'bg-gray-600';
                                        let actionIcon = 'fas fa-cog';
                                        
                                        switch(log.action) {
                                            case 'UPDATE_ADMIN_STATUS':
                                                actionColor = 'bg-purple-600';
                                                actionIcon = 'fas fa-shield-alt';
                                                break;
                                            case 'UPDATE_BAN_STATUS':
                                                actionColor = 'bg-red-600';
                                                actionIcon = 'fas fa-ban';
                                                break;
                                            case 'DELETE_USER':
                                                actionColor = 'bg-red-700';
                                                actionIcon = 'fas fa-trash';
                                                break;
                                            case 'GRANT_COURSE_ACCESS':
                                                actionColor = 'bg-green-600';
                                                actionIcon = 'fas fa-plus';
                                                break;
                                            case 'REVOKE_COURSE_ACCESS':
                                                actionColor = 'bg-yellow-600';
                                                actionIcon = 'fas fa-minus';
                                                break;
                                            case 'UPDATE_SETTING':
                                                actionColor = 'bg-blue-600';
                                                actionIcon = 'fas fa-cog';
                                                break;
                                        }
                                        %>
                                        <span class="px-3 py-1 <%= actionColor %> text-white text-xs rounded-full flex items-center w-fit">
                                            <i class="<%= actionIcon %> mr-2"></i>
                                            <%= log.action.replace(/_/g, ' ') %>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="text-gray-300">
                                            <% if (log.target_username) { %>
                                                <div class="flex items-center">
                                                    <i class="fas fa-user mr-2 text-blue-400"></i>
                                                    <%= log.target_username %>
                                                </div>
                                            <% } else if (log.course_name) { %>
                                                <div class="flex items-center">
                                                    <i class="fas fa-graduation-cap mr-2 text-green-400"></i>
                                                    <%= log.course_name %>
                                                </div>
                                            <% } else { %>
                                                <span class="text-gray-500">-</span>
                                            <% } %>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="text-gray-300 text-sm max-w-xs truncate" title="<%= log.details %>">
                                            <%= log.details %>
                                        </div>
                                    </td>
                                </tr>
                            <% }); %>
                        <% } %>
                    </tbody>
                </table>
            </div>
            
            <% if (auditLog.length > 0) { %>
                <div class="p-6 border-t border-gray-700 bg-gray-750">
                    <div class="flex items-center justify-between">
                        <p class="text-gray-400 text-sm">
                            Showing <%= auditLog.length %> recent entries
                        </p>
                        <div class="flex items-center space-x-4">
                            <div class="flex items-center space-x-2 text-sm text-gray-400">
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-purple-600 rounded-full mr-2"></div>
                                    <span>Admin Actions</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-green-600 rounded-full mr-2"></div>
                                    <span>Access Granted</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-red-600 rounded-full mr-2"></div>
                                    <span>Restrictions</span>
                                </div>
                                <div class="flex items-center">
                                    <div class="w-3 h-3 bg-blue-600 rounded-full mr-2"></div>
                                    <span>Settings</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <% } %>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mt-8">
            <div class="bg-gray-800 rounded-xl p-6 border border-gray-700">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm font-medium">Total Actions</p>
                        <p class="text-2xl font-bold text-white"><%= auditLog.length %></p>
                    </div>
                    <div class="w-12 h-12 bg-blue-600 bg-opacity-20 rounded-full flex items-center justify-center">
                        <i class="fas fa-history text-blue-400 text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-gray-800 rounded-xl p-6 border border-gray-700">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm font-medium">Admin Changes</p>
                        <p class="text-2xl font-bold text-white"><%= auditLog.filter(log => log.action === 'UPDATE_ADMIN_STATUS').length %></p>
                    </div>
                    <div class="w-12 h-12 bg-purple-600 bg-opacity-20 rounded-full flex items-center justify-center">
                        <i class="fas fa-shield-alt text-purple-400 text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-gray-800 rounded-xl p-6 border border-gray-700">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm font-medium">Ban Actions</p>
                        <p class="text-2xl font-bold text-white"><%= auditLog.filter(log => log.action === 'UPDATE_BAN_STATUS').length %></p>
                    </div>
                    <div class="w-12 h-12 bg-red-600 bg-opacity-20 rounded-full flex items-center justify-center">
                        <i class="fas fa-ban text-red-400 text-xl"></i>
                    </div>
                </div>
            </div>

            <div class="bg-gray-800 rounded-xl p-6 border border-gray-700">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-gray-400 text-sm font-medium">Course Changes</p>
                        <p class="text-2xl font-bold text-white"><%= auditLog.filter(log => log.action.includes('COURSE_ACCESS')).length %></p>
                    </div>
                    <div class="w-12 h-12 bg-green-600 bg-opacity-20 rounded-full flex items-center justify-center">
                        <i class="fas fa-graduation-cap text-green-400 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <%- include('../partials/footer') %>
</body>
</html>
