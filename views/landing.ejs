<%- include('partials/header') %>
    <%- include('partials/navbar-landing') %>
                <a href="/login" class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 text-center dark:bg-blue-600 dark:hover:bg-blue-700 dark:focus:ring-blue-800"><%= t('auth.login') %></a>
                <a href="/register" class="text-gray-900 bg-white border border-gray-300 focus:outline-none hover:bg-gray-100 focus:ring-4 focus:ring-gray-100 font-medium rounded-lg text-sm px-5 py-2.5 me-2 mb-2 dark:bg-gray-800 dark:text-white dark:border-gray-600 dark:hover:bg-gray-700 dark:hover:border-gray-600 dark:focus:ring-gray-700 ml-3"><%= t('auth.register') %></a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 pt-20 relative overflow-hidden">
        <!-- Background Animation -->
        <div class="absolute inset-0 opacity-20">
            <div class="absolute top-10 left-10 w-72 h-72 bg-blue-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse"></div>
            <div class="absolute top-40 right-10 w-72 h-72 bg-purple-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse animation-delay-2000"></div>
            <div class="absolute bottom-10 left-1/2 w-72 h-72 bg-pink-500 rounded-full mix-blend-multiply filter blur-xl animate-pulse animation-delay-4000"></div>
        </div>

        <div class="relative py-16 px-4 mx-auto max-w-screen-xl text-center lg:py-24">
            <div class="mb-8">
                <div class="inline-flex items-center justify-center w-20 h-20 bg-blue-600 bg-opacity-20 rounded-full mb-6">
                    <i class="fas fa-code text-blue-400 text-4xl"></i>
                </div>
                <h1 class="mb-6 text-5xl font-extrabold tracking-tight leading-none text-white md:text-6xl lg:text-7xl">
                    Code<span class="text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400">Wave</span>
                </h1>
                <p class="mb-8 text-xl font-normal text-gray-300 lg:text-2xl sm:px-16 lg:px-48 leading-relaxed">
                    <%= t('landing.hero_subtitle') %>
                </p>
            </div>

            <div class="flex flex-col space-y-4 sm:flex-row sm:justify-center sm:space-y-0 sm:space-x-4 mb-12">
                <a href="/register" class="group inline-flex justify-center items-center py-4 px-8 text-lg font-semibold text-center text-white rounded-xl bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 focus:ring-4 focus:ring-blue-300 transform hover:scale-105 transition-all duration-200 shadow-xl">
                    <i class="fas fa-rocket mr-3 group-hover:animate-bounce"></i>
                    <%= t('landing.get_started') %>
                    <svg class="w-3.5 h-3.5 ms-2 rtl:rotate-180" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 10">
                        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M1 5h12m0 0L9 1m4 4L9 9"/>
                    </svg>
                </a>
                <a href="/login" class="group inline-flex justify-center items-center py-4 px-8 text-lg font-semibold text-center text-gray-300 rounded-xl border-2 border-gray-600 hover:border-blue-400 hover:text-white focus:ring-4 focus:ring-gray-600 transform hover:scale-105 transition-all duration-200">
                    <i class="fas fa-sign-in-alt mr-3 group-hover:text-blue-400"></i>
                    <%= t('auth.login') %>
                </a>
            </div>

            <!-- Trust Indicators -->
            <div class="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 opacity-60">
                <div class="text-center">
                    <div class="text-3xl font-bold text-white">6</div>
                    <div class="text-gray-400 text-sm"><%= t('landing.trust_languages') %></div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-white">120+</div>
                    <div class="text-gray-400 text-sm"><%= t('landing.trust_lessons') %></div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-white">100%</div>
                    <div class="text-gray-400 text-sm"><%= t('landing.trust_free') %></div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-white">24/7</div>
                    <div class="text-gray-400 text-sm"><%= t('landing.trust_available') %></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="bg-gray-800 py-16">
        <div class="py-8 px-4 mx-auto max-w-screen-xl sm:py-16 lg:px-6">
            <div class="max-w-screen-md mb-8 lg:mb-16 text-center mx-auto">
                <h2 class="mb-4 text-4xl tracking-tight font-extrabold text-white"><%= t('landing.features_title') %></h2>
                <p class="text-gray-300 sm:text-xl"><%= t('landing.features_subtitle') %></p>
            </div>
            <div class="space-y-8 md:grid md:grid-cols-2 lg:grid-cols-3 md:gap-12 md:space-y-0">
                <div class="text-center">
                    <div class="flex justify-center items-center mb-4 w-10 h-10 rounded-full bg-blue-100 lg:h-12 lg:w-12 dark:bg-blue-900 mx-auto">
                        <i class="fas fa-layer-group text-blue-600 dark:text-blue-300 text-xl"></i>
                    </div>
                    <h3 class="mb-2 text-xl font-bold text-white"><%= t('landing.feature_levels_title') %></h3>
                    <p class="text-gray-300"><%= t('landing.feature_levels_desc') %></p>
                </div>
                <div class="text-center">
                    <div class="flex justify-center items-center mb-4 w-10 h-10 rounded-full bg-green-100 lg:h-12 lg:w-12 dark:bg-green-900 mx-auto">
                        <i class="fas fa-code text-green-600 dark:text-green-300 text-xl"></i>
                    </div>
                    <h3 class="mb-2 text-xl font-bold text-white"><%= t('landing.feature_interactive_title') %></h3>
                    <p class="text-gray-300"><%= t('landing.feature_interactive_desc') %></p>
                </div>
                <div class="text-center">
                    <div class="flex justify-center items-center mb-4 w-10 h-10 rounded-full bg-yellow-100 lg:h-12 lg:w-12 dark:bg-yellow-900 mx-auto">
                        <i class="fas fa-rocket text-yellow-600 dark:text-yellow-300 text-xl"></i>
                    </div>
                    <h3 class="mb-2 text-xl font-bold text-white"><%= t('landing.feature_projects_title') %></h3>
                    <p class="text-gray-300"><%= t('landing.feature_projects_desc') %></p>
                </div>
            </div>
        </div>
    </section>

    <!-- Courses Section -->
    <section class="py-16 bg-gray-800">
        <div class="max-w-screen-xl mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-extrabold text-white mb-4"><%= t('landing.courses_title') %></h2>
                <p class="text-xl text-gray-300"><%= t('landing.courses_subtitle') %></p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- HTML/CSS/JS Course -->
                <div class="bg-gray-700 border border-gray-600 rounded-lg shadow-lg h-full">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <i class="fab fa-html5 text-4xl text-red-500 mr-4"></i>
                            <h3 class="text-xl font-bold text-white"><%= t('course.html_title') %></h3>
                        </div>
                        <p class="text-gray-300 mb-6 text-sm">
                            <%= t('course.html_desc') %>
                        </p>
                        <ul class="space-y-2 mb-6">
                            <li class="flex items-center text-gray-300 text-sm">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                20 <%= t('landing.course_levels') %>
                            </li>
                            <li class="flex items-center text-gray-300 text-sm">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <%= t('course.feature_html_basics') %>
                            </li>
                            <li class="flex items-center text-gray-300 text-sm">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <%= t('course.feature_js_intro') %>
                            </li>
                            <li class="flex items-center text-gray-300 text-sm">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <%= t('landing.course_practical') %>
                            </li>
                        </ul>
                        <div class="flex items-center justify-between">
                            <span class="text-xl font-bold text-blue-400"><%= t('landing.course_free') %></span>
                            <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm"><%= t('landing.level_beginner') %></span>
                        </div>
                    </div>
                </div>

                <!-- JavaScript Advanced Course -->
                <div class="bg-gray-700 border border-gray-600 rounded-lg shadow-lg h-full">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <i class="fab fa-js-square text-4xl text-yellow-500 mr-4"></i>
                            <h3 class="text-xl font-bold text-white"><%= t('course.js_title') %></h3>
                        </div>
                        <p class="text-gray-300 mb-6 text-sm">
                            <%= t('course.js_desc') %>
                        </p>
                        <ul class="space-y-2 mb-6">
                            <li class="flex items-center text-gray-300 text-sm">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                20 <%= t('landing.course_levels') %>
                            </li>
                            <li class="flex items-center text-gray-300 text-sm">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <%= t('course.feature_es6') %>
                            </li>
                            <li class="flex items-center text-gray-300 text-sm">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <%= t('course.feature_async') %>
                            </li>
                            <li class="flex items-center text-gray-300 text-sm">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <%= t('course.feature_complex') %>
                            </li>
                        </ul>
                        <div class="flex items-center justify-between">
                            <span class="text-xl font-bold text-blue-400"><%= t('landing.course_free') %></span>
                            <span class="bg-yellow-600 text-white px-3 py-1 rounded-full text-sm"><%= t('landing.level_intermediate') %></span>
                        </div>
                    </div>
                </div>

                <!-- PHP Course -->
                <div class="bg-gray-700 border border-gray-600 rounded-lg shadow-lg h-full">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <i class="fab fa-php text-4xl text-purple-500 mr-4"></i>
                            <h3 class="text-xl font-bold text-white"><%= t('course.php_title') %></h3>
                        </div>
                        <p class="text-gray-300 mb-6 text-sm">
                            <%= t('course.php_desc') %>
                        </p>
                        <ul class="space-y-2 mb-6">
                            <li class="flex items-center text-gray-300 text-sm">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                20 <%= t('landing.course_levels') %>
                            </li>
                            <li class="flex items-center text-gray-300 text-sm">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <%= t('course.feature_mysql') %>
                            </li>
                            <li class="flex items-center text-gray-300 text-sm">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <%= t('course.feature_laravel') %>
                            </li>
                            <li class="flex items-center text-gray-300 text-sm">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <%= t('course.feature_apis') %>
                            </li>
                        </ul>
                        <div class="flex items-center justify-between">
                            <span class="text-xl font-bold text-purple-400"><%= t('landing.course_premium') %></span>
                            <span class="bg-purple-600 text-white px-3 py-1 rounded-full text-sm"><%= t('landing.level_intermediate') %></span>
                        </div>
                    </div>
                </div>

                <!-- Python Course -->
                <div class="bg-gray-700 border border-gray-600 rounded-lg shadow-lg h-full">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <i class="fab fa-python text-4xl text-green-500 mr-4"></i>
                            <h3 class="text-xl font-bold text-white"><%= t('course.python_title') %></h3>
                        </div>
                        <p class="text-gray-300 mb-6 text-sm">
                            <%= t('course.python_desc') %>
                        </p>
                        <ul class="space-y-2 mb-6">
                            <li class="flex items-center text-gray-300 text-sm">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                20 <%= t('landing.course_levels') %>
                            </li>
                            <li class="flex items-center text-gray-300 text-sm">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <%= t('course.feature_data_science') %>
                            </li>
                            <li class="flex items-center text-gray-300 text-sm">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <%= t('course.feature_ml') %>
                            </li>
                            <li class="flex items-center text-gray-300 text-sm">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <%= t('landing.course_practical') %>
                            </li>
                        </ul>
                        <div class="flex items-center justify-between">
                            <span class="text-xl font-bold text-purple-400"><%= t('landing.course_premium') %></span>
                            <span class="bg-green-600 text-white px-3 py-1 rounded-full text-sm"><%= t('landing.level_intermediate') %></span>
                        </div>
                    </div>
                </div>

                <!-- Go Course -->
                <div class="bg-gray-700 border border-gray-600 rounded-lg shadow-lg h-full">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <i class="fab fa-golang text-4xl text-cyan-500 mr-4"></i>
                            <h3 class="text-xl font-bold text-white"><%= t('course.go_title') %></h3>
                        </div>
                        <p class="text-gray-300 mb-6 text-sm">
                            <%= t('course.go_desc') %>
                        </p>
                        <ul class="space-y-2 mb-6">
                            <li class="flex items-center text-gray-300 text-sm">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                20 <%= t('landing.course_levels') %>
                            </li>
                            <li class="flex items-center text-gray-300 text-sm">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <%= t('course.feature_concurrency') %>
                            </li>
                            <li class="flex items-center text-gray-300 text-sm">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <%= t('course.feature_microservices') %>
                            </li>
                            <li class="flex items-center text-gray-300 text-sm">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <%= t('course.feature_cloud') %>
                            </li>
                        </ul>
                        <div class="flex items-center justify-between">
                            <span class="text-xl font-bold text-purple-400"><%= t('landing.course_premium') %></span>
                            <span class="bg-cyan-600 text-white px-3 py-1 rounded-full text-sm"><%= t('landing.level_advanced') %></span>
                        </div>
                    </div>
                </div>

                <!-- Java Course -->
                <div class="bg-gray-700 border border-gray-600 rounded-lg shadow-lg h-full">
                    <div class="p-6">
                        <div class="flex items-center mb-4">
                            <i class="fab fa-java text-4xl text-orange-500 mr-4"></i>
                            <h3 class="text-xl font-bold text-white"><%= t('course.java_title') %></h3>
                        </div>
                        <p class="text-gray-300 mb-6 text-sm">
                            <%= t('course.java_desc') %>
                        </p>
                        <ul class="space-y-2 mb-6">
                            <li class="flex items-center text-gray-300 text-sm">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                20 <%= t('landing.course_levels') %>
                            </li>
                            <li class="flex items-center text-gray-300 text-sm">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <%= t('course.feature_oop') %>
                            </li>
                            <li class="flex items-center text-gray-300 text-sm">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <%= t('course.feature_enterprise') %>
                            </li>
                            <li class="flex items-center text-gray-300 text-sm">
                                <i class="fas fa-check text-green-500 mr-3"></i>
                                <%= t('landing.course_practical') %>
                            </li>
                        </ul>
                        <div class="flex items-center justify-between">
                            <span class="text-xl font-bold text-purple-400"><%= t('landing.course_premium') %></span>
                            <span class="bg-orange-600 text-white px-3 py-1 rounded-full text-sm"><%= t('landing.level_advanced') %></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section style="margin-bottom: -32px;" class="py-20 bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 relative overflow-hidden">
        <!-- Background Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute top-0 left-0 w-full h-full" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23ffffff" fill-opacity="0.4"><circle cx="30" cy="30" r="2"/></g></svg>'); background-size: 60px 60px;"></div>
        </div>

        <div class="relative max-w-screen-xl mx-auto px-4 text-center">
            <div class="mb-8">
                <div class="inline-flex items-center justify-center w-16 h-16 bg-white bg-opacity-20 rounded-full mb-6">
                    <i class="fas fa-graduation-cap text-white text-2xl"></i>
                </div>
                <h2 class="text-5xl font-extrabold text-white mb-6"><%= t('landing.cta_title') %></h2>
                <p class="text-xl text-blue-100 mb-8 max-w-2xl mx-auto leading-relaxed"><%= t('landing.cta_subtitle') %></p>
            </div>

            <a href="/register" class="group inline-flex items-center justify-center px-10 py-5 text-xl font-bold text-blue-600 bg-white rounded-2xl hover:bg-gray-100 focus:ring-4 focus:ring-blue-300 transform hover:scale-105 transition-all duration-300 shadow-2xl">
                <i class="fas fa-rocket mr-4 group-hover:animate-bounce"></i>
                <%= t('landing.cta_button') %>
                <svg class="w-5 h-5 ml-3 group-hover:translate-x-1 transition-transform" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </a>
        </div>
    </section>

<%- include('partials/footer') %>
