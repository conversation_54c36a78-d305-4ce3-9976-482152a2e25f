<%- include('../partials/header') %>
<%- include('../partials/navbar') %>

    <div class="max-w-screen-xl mx-auto p-4">
        <!-- Breadcrumb -->
        <nav class="flex mb-8" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="/dashboard" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                        <%= t('nav.dashboard') %>
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                        </svg>
                        <a href="/course/<%= course.slug %>" class="text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white"><%= course.name %></a>
                    </div>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400"><%= t('common.level') %> <%= level.level_number %></span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Level Header -->
        <div class="bg-gray-800 rounded-lg p-6 mb-8 border border-gray-700">
            <div class="flex items-center justify-between mb-4">
                <h1 class="text-3xl font-bold text-white flex items-center">
                    <i class="fas fa-graduation-cap text-blue-500 mr-3"></i>
                    <%= t('common.level') %> <%= level.level_number %>: <%= level.title %>
                </h1>
                <div class="text-right">
                    <div class="text-sm text-gray-400 mb-1"><%= t('courses.progress') %></div>
                    <div class="text-lg font-bold text-white"><%= level.level_number %> / <%= course.total_levels %></div>
                </div>
            </div>

            <!-- Progress Bar -->
            <div class="w-full bg-gray-700 rounded-full h-3 mb-4">
                <div class="bg-blue-600 h-3 rounded-full" style="width: <%= Math.round(level.level_number / course.total_levels * 100) %>%"></div>
            </div>

            <!-- Progress Info -->
            <div class="flex items-center justify-between text-sm">
                <div class="flex items-center space-x-4">
                    <% if (progress) { %>
                        <% if (progress.completed) { %>
                            <span class="flex items-center text-green-400">
                                <i class="fas fa-check-circle mr-1"></i>
                                <%= t('progress.completed') %>
                            </span>
                        <% } else { %>
                            <span class="flex items-center text-blue-400">
                                <i class="fas fa-play-circle mr-1"></i>
                                <%= t('progress.in_progress') %>
                            </span>
                        <% } %>
                        <span class="text-gray-400"><%= t('text.attempts') %>: <%= progress.attempts || 0 %></span>
                        <span class="text-gray-400"><%= t('common.points') %>: <%= progress.score || 0 %></span>
                    <% } else { %>
                        <span class="flex items-center text-gray-400">
                            <i class="fas fa-play-circle mr-1"></i>
                            <%= t('progress.not_started') %>
                        </span>
                    <% } %>
                </div>
                <div class="text-gray-400">
                    <%= Math.round(level.level_number / course.total_levels * 100) %>% <%= t('text.course_completed') %>
                </div>
            </div>
        </div>

        <!-- Navigation Buttons Top -->
        <div class="flex justify-between mb-8">
            <div>
                <% if (level.level_number > 1) { %>
                    <a href="/course/<%= course.slug %>/level/<%= level.level_number - 1 %>"
                       class="text-gray-400 bg-gray-700 hover:bg-gray-600 focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-4 py-2 inline-flex items-center">
                        <i class="fas fa-chevron-left mr-2"></i>
                        <%= t('common.previous') %>
                    </a>
                <% } %>
            </div>
            <div>
                <% if (progress && progress.completed && level.level_number < course.total_levels) { %>
                    <a href="/course/<%= course.slug %>/level/<%= level.level_number + 1 %>"
                       class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 inline-flex items-center">
                        <%= t("level.next_level") %>
                        <i class="fas fa-chevron-right ml-2"></i>
                    </a>
                <% } else if (level.level_number < course.total_levels) { %>
                    <button class="text-gray-400 bg-gray-700 font-medium rounded-lg text-sm px-4 py-2 inline-flex items-center cursor-not-allowed" disabled>
                        <%= t("level.next_level") %>
                        <i class="fas fa-lock ml-2"></i>
                    </button>
                <% } %>
            </div>
        </div>

        <!-- Level Content -->
        <div class="bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl p-8 mb-8 border border-gray-600 shadow-xl">
            <div class="flex items-center mb-6">
                <div class="bg-blue-600 p-3 rounded-lg mr-4">
                    <i class="fas fa-book text-white text-xl"></i>
                </div>
                <div>
                    <h2 class="text-2xl font-bold text-white mb-1">
                        <%= t('level.instructions') %>
                    </h2>
                    <p class="text-blue-300 text-sm">
                        <%= t('level.read_carefully') %>
                    </p>
                </div>
            </div>

            <div class="bg-gray-900 bg-opacity-50 rounded-lg p-6 border-l-4 border-blue-500">
                <div class="text-gray-200 prose prose-invert max-w-none leading-relaxed">
                    <%- level.content %>
                </div>
            </div>

            <!-- Learning Tips -->
            <div class="mt-6 bg-blue-900 bg-opacity-30 border border-blue-700 rounded-lg p-4">
                <div class="flex items-start">
                    <div class="bg-blue-600 p-2 rounded-full mr-3 mt-1">
                        <i class="fas fa-lightbulb text-white text-sm"></i>
                    </div>
                    <div>
                        <h4 class="text-blue-300 font-semibold mb-2"><%= t('level.tip') %></h4>
                        <p class="text-blue-200 text-sm">
                            <%= t('level.tip_text') %>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Exercise Section -->
        <div class="bg-gray-800 rounded-lg p-6 mb-8 border border-gray-700">
            <% if (level.exercise_type === 'project') { %>
                <h2 class="text-2xl font-semibold text-white mb-4 flex items-center">
                    <i class="fas fa-project-diagram text-purple-500 mr-3"></i>
                    <%= t('level.instructions') %>
                </h2>
                <div class="bg-blue-900 bg-opacity-30 border border-blue-700 rounded-lg p-4 mb-4">
                    <div class="flex items-center text-blue-300">
                        <i class="fas fa-lightbulb mr-2"></i>
                        <span><%= level.exercise_data || t('text.follow_instructions') %></span>
                    </div>
                </div>
            <% } else { %>
                <h2 class="text-2xl font-semibold text-white mb-4 flex items-center">
                    <i class="fas fa-code text-yellow-500 mr-3"></i>
                    <%= t('level.code_editor') %>
                </h2>
            <% } %>
        </div>

        <!-- Code Editor Section -->
        <div class="bg-gray-800 rounded-lg border border-gray-700 mb-8">
            <div class="p-6 border-b border-gray-700">
                <h3 class="text-xl font-semibold text-white flex items-center">
                    <i class="fas fa-code text-green-500 mr-3"></i>
                    <%= t('level.code_editor') %>
                </h3>
            </div>
            
            <div class="p-6">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Code Input -->
                    <div class="order-1">
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg font-medium text-white flex items-center">
                                <i class="fas fa-code mr-2"></i>
                                <%= t('level.code_editor') %>
                            </h3>
                            <!-- Programming Language Badge -->
                            <div class="flex items-center">
                                <%
                                let langInfo = {};
                                switch(course.slug) {
                                    case 'html-css-js':
                                        langInfo = { name: 'HTML/CSS/JS', color: 'bg-orange-600', icon: 'fab fa-html5' };
                                        break;
                                    case 'javascript-advanced':
                                        langInfo = { name: 'JavaScript', color: 'bg-yellow-600', icon: 'fab fa-js-square' };
                                        break;
                                    case 'php':
                                        langInfo = { name: 'PHP', color: 'bg-purple-600', icon: 'fab fa-php' };
                                        break;
                                    case 'python':
                                        langInfo = { name: 'Python', color: 'bg-blue-600', icon: 'fab fa-python' };
                                        break;
                                    case 'go':
                                        langInfo = { name: 'Go', color: 'bg-cyan-600', icon: 'fas fa-bolt' };
                                        break;
                                    case 'java':
                                        langInfo = { name: 'Java', color: 'bg-red-600', icon: 'fab fa-java' };
                                        break;
                                    default:
                                        langInfo = { name: 'Code', color: 'bg-gray-600', icon: 'fas fa-code' };
                                }
                                %>
                                <span class="<%= langInfo.color %> text-white px-3 py-1 rounded-full text-sm font-medium flex items-center">
                                    <i class="<%= langInfo.icon %> mr-2"></i>
                                    <%= langInfo.name %>
                                </span>
                            </div>
                        </div>
                        <div class="mb-4 relative">
                            <textarea id="codeInput"
                                      class="w-full h-48 lg:h-64 bg-gray-900 border border-gray-600 text-white text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 p-4 font-mono resize-none transition-all duration-200 hover:border-gray-500"
                                      placeholder="<%= t('text.write_code_here') %>..."
                                      spellcheck="false"><%= lastSubmission ? lastSubmission.submitted_code : '' %></textarea>
                            <!-- Line numbers could be added here in the future -->
                        </div>
                        <div class="flex flex-wrap gap-3">
                            <button type="button"
                                    class="text-white bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 focus:ring-4 focus:ring-green-300 font-medium rounded-lg text-sm px-6 py-3 inline-flex items-center transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                                    onclick="runCode()">
                                <i class="fas fa-play mr-2"></i>
                                <%= t("level.run_code") %>
                            </button>
                            <button type="button"
                                    class="text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-6 py-3 inline-flex items-center transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
                                    onclick="submitCode()">
                                <i class="fas fa-paper-plane mr-2"></i>
                                <%= t("level.submit_code") %>
                            </button>
                            <button type="button"
                                    class="text-gray-300 bg-gray-700 hover:bg-gray-600 focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-4 py-3 inline-flex items-center transition-all duration-200"
                                    onclick="clearCode()">
                                <i class="fas fa-trash mr-2"></i>
                                <%= t("level.clear_code") %>
                            </button>
                        </div>
                    </div>

                    <!-- Output/Preview -->
                    <div class="order-2">
                        <h3 class="text-lg font-medium text-white mb-4 flex items-center">
                            <i class="fas fa-terminal mr-2 text-green-500"></i>
                            <%= t('level.output') %>
                        </h3>
                        <div class="bg-gray-900 border border-gray-600 rounded-lg p-4 h-48 lg:h-64 overflow-auto relative">
                            <!-- Terminal-like header -->
                            <div class="absolute top-2 right-2 flex space-x-1">
                                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            </div>
                            <!-- HTML/CSS Preview -->
                            <iframe id="htmlPreview" class="w-full h-full border-0 rounded hidden"></iframe>

                            <!-- JavaScript Output -->
                            <div id="codeOutput" class="text-green-400 font-mono text-sm">
                                <%= lastSubmission ? lastSubmission.output : t('text.run_code_to_see_output') %>
                            </div>
                        </div>

                        <!-- Expected Output -->
                        <% if (level.expected_output) { %>
                            <div class="mt-4">
                                <h4 class="text-md font-medium text-white mb-2"><%= t("level.expected_output") %>:</h4>
                                <div class="bg-gray-700 border border-gray-600 rounded-lg p-3">
                                    <code class="text-yellow-400 text-sm"><%= level.expected_output %></code>
                                </div>
                            </div>
                        <% } %>

                        <!-- Feedback Area -->
                        <div id="feedback" class="mt-4 hidden">
                            <div id="feedbackContent" class="p-4 rounded-lg border"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Buttons Bottom -->
        <div class="flex justify-between mt-8">
            <div>
                <% if (level.level_number > 1) { %>
                    <a href="/course/<%= course.slug %>/level/<%= level.level_number - 1 %>"
                       class="text-gray-400 bg-gray-700 hover:bg-gray-600 focus:ring-4 focus:ring-gray-300 font-medium rounded-lg text-sm px-4 py-2 inline-flex items-center">
                        <i class="fas fa-chevron-left mr-2"></i>
                        <%= t('common.previous') %>
                    </a>
                <% } %>
            </div>
            <div>
                <% if (progress && progress.completed && level.level_number < course.total_levels) { %>
                    <a href="/course/<%= course.slug %>/level/<%= level.level_number + 1 %>"
                       class="text-white bg-blue-700 hover:bg-blue-800 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-4 py-2 inline-flex items-center">
                        <%= t("level.next_level") %>
                        <i class="fas fa-chevron-right ml-2"></i>
                    </a>
                <% } else if (level.level_number < course.total_levels) { %>
                    <button class="text-gray-400 bg-gray-700 font-medium rounded-lg text-sm px-4 py-2 inline-flex items-center cursor-not-allowed" disabled>
                        <%= t("level.next_level") %>
                        <i class="fas fa-lock ml-2"></i>
                    </button>
                <% } %>
            </div>
        </div>
    </div>

<%- include('../partials/footer') %>
    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.5.1/dist/flowbite.min.js"></script>
    <script src="/js/level.js"></script>
    <script>
        // Initialize level with data from server
        document.addEventListener('DOMContentLoaded', function() {
            initializeLevel(<%- course.id %>, <%- level.id %>, '<%- course.slug %>', <%- level.level_number %>);
        });
    </script>
