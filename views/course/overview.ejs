<%- include('../partials/header') %>
    <%- include('../partials/navbar') %>

    <div class="max-w-screen-xl mx-auto p-4">
        <!-- Breadcrumb -->
        <nav class="flex mb-8" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-2 rtl:space-x-reverse">
                <li class="inline-flex items-center">
                    <a href="/dashboard" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600 dark:text-gray-400 dark:hover:text-white">
                        <%= t('nav.dashboard') %>
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <svg class="rtl:rotate-180 w-3 h-3 text-gray-400 mx-1" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 6 10">
                            <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m1 9 4-4-4-4"/>
                        </svg>
                        <span class="ms-1 text-sm font-medium text-gray-500 md:ms-2 dark:text-gray-400"><%= t('course.name.' + course.slug) %></span>
                    </div>
                </li>
            </ol>
        </nav>

        <!-- Course Header -->
        <div class="bg-gray-800 rounded-lg p-6 mb-8 border border-gray-700">
            <div class="flex items-center mb-4">
                <% if (course.slug === 'html-css-js') { %>
                    <i class="fab fa-html5 text-6xl text-orange-500 mr-6"></i>
                <% } else if (course.slug === 'javascript-advanced') { %>
                    <i class="fab fa-js-square text-6xl text-yellow-500 mr-6"></i>
                <% } %>
                <div>
                    <h1 class="text-4xl font-bold text-white mb-2"><%= t('course.name.' + course.slug) %></h1>
                    <p class="text-gray-300 text-lg"><%= t('course.desc.' + course.slug) %></p>
                </div>
            </div>
        </div>

        <!-- Progress Overview -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
            <div class="lg:col-span-2">
                <div class="bg-gray-800 rounded-lg border border-gray-700">
                    <div class="p-6 border-b border-gray-700">
                        <h3 class="text-xl font-bold text-white flex items-center">
                            <i class="fas fa-chart-line text-blue-500 mr-3"></i>
                            <%= t('text.course_progress') %>
                        </h3>
                    </div>
                    <div class="p-6">
                        <%
                            const completedLevels = levels.filter(l => l.progress && l.progress.completed).length;
                            const totalLevels = levels.length;
                            const progressPercentage = totalLevels > 0 ? (completedLevels / totalLevels * 100) : 0;
                        %>
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-gray-300"><%= t('stats.completed_levels') %></span>
                            <span class="text-white font-bold text-lg"><%= completedLevels %> / <%= totalLevels %></span>
                        </div>
                        <div class="w-full bg-gray-700 rounded-full h-3 mb-6">
                            <div class="bg-blue-600 h-3 rounded-full" style="width: <%= progressPercentage %>%"></div>
                        </div>
                        <div class="grid grid-cols-3 gap-4">
                            <div class="text-center p-4 bg-gray-700 rounded-lg">
                                <div class="text-2xl font-bold text-blue-400"><%= totalLevels %></div>
                                <div class="text-sm text-gray-400"><%= t('profile.total_levels') %></div>
                            </div>
                            <div class="text-center p-4 bg-gray-700 rounded-lg">
                                <div class="text-2xl font-bold text-green-400"><%= completedLevels %></div>
                                <div class="text-sm text-gray-400"><%= t('progress.completed') %></div>
                            </div>
                            <div class="text-center p-4 bg-gray-700 rounded-lg">
                                <% const totalScore = levels.reduce((sum, l) => sum + (l.progress ? l.progress.score : 0), 0); %>
                                <div class="text-2xl font-bold text-yellow-400"><%= totalScore %></div>
                                <div class="text-sm text-gray-400"><%= t('common.points') %></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Next Goal Card -->
            <div class="bg-blue-600 rounded-lg text-white">
                <div class="p-6 text-center">
                    <i class="fas fa-trophy text-4xl mb-4"></i>
                    <h3 class="text-xl font-bold mb-4"><%= t('text.next_goal') %></h3>
                    <% const nextLevel = levels.find(l => !l.progress || !l.progress.completed); %>
                    <% if (nextLevel) { %>
                        <p class="mb-4"><%= t('common.level') %> <%= nextLevel.level_number %>: <%= nextLevel.title %></p>
                        <a href="/course/<%= course.slug %>/level/<%= nextLevel.level_number %>" class="bg-white text-blue-600 hover:bg-gray-100 focus:ring-4 focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center">
                            <i class="fas fa-play mr-2"></i>
                            <%= t('common.continue') %>
                        </a>
                    <% } else { %>
                        <p class="mb-4"><%= t('text.course_completed') %>!</p>
                        <div class="bg-white text-blue-600 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center opacity-50">
                            <i class="fas fa-check mr-2"></i>
                            <%= t('common.finish') %>
                        </div>
                    <% } %>
                </div>
            </div>
        </div>

        <!-- Level Grid -->
        <div class="mb-8">
            <h2 class="text-3xl font-bold text-white mb-6 flex items-center">
                <i class="fas fa-list text-blue-500 mr-3"></i>
                <%= t('courses.levels') %>
            </h2>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <% levels.forEach((level, index) => { %>
                    <div class="bg-gray-800 rounded-lg border <%= level.progress && level.progress.completed ? 'border-green-500' : (level.isUnlocked ? 'border-blue-500' : 'border-gray-600') %> h-full">
                        <div class="p-4 border-b border-gray-700 <%= level.progress && level.progress.completed ? 'bg-green-600' : (level.isUnlocked ? 'bg-blue-600' : 'bg-gray-700') %> rounded-t-lg">
                            <div class="flex justify-between items-center">
                                <span class="font-bold text-white"><%= t('common.level') %> <%= level.level_number %></span>
                                <% if (level.progress && level.progress.completed) { %>
                                    <i class="fas fa-check-circle text-white text-xl"></i>
                                <% } else if (level.isUnlocked) { %>
                                    <i class="fas fa-play-circle text-white text-xl"></i>
                                <% } else { %>
                                    <i class="fas fa-lock text-white text-xl"></i>
                                <% } %>
                            </div>
                        </div>
                        <div class="p-4 flex-grow">
                            <h4 class="text-lg font-semibold text-white mb-2"><%= level.title %></h4>
                            <p class="text-gray-400 text-sm mb-4"><%= level.description || t('text.loading_courses') %></p>

                            <% if (level.progress) { %>
                                <div class="mb-4 text-sm text-gray-400">
                                    <%= t('text.attempts') %>: <%= level.progress.attempts || 0 %> |
                                    <%= t('common.points') %>: <%= level.progress.score || 0 %>
                                </div>
                            <% } %>
                        </div>
                        <div class="p-4 pt-0">
                            <% if (level.isUnlocked) { %>
                                <a href="/course/<%= course.slug %>/level/<%= level.level_number %>"
                                   class="w-full text-white <%= level.progress && level.progress.completed ? 'bg-green-700 hover:bg-green-800 focus:ring-green-300' : 'bg-blue-700 hover:bg-blue-800 focus:ring-blue-300' %> focus:ring-4 focus:outline-none font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center justify-center">
                                    <% if (level.progress && level.progress.completed) { %>
                                        <i class="fas fa-redo mr-2"></i>
                                        <%= t('text.repeat') %>
                                    <% } else { %>
                                        <i class="fas fa-play mr-2"></i>
                                        <%= t('common.start') %>
                                    <% } %>
                                </a>
                            <% } else { %>
                                <button class="w-full text-gray-400 bg-gray-700 font-medium rounded-lg text-sm px-5 py-2.5 text-center inline-flex items-center justify-center cursor-not-allowed" disabled>
                                    <i class="fas fa-lock mr-2"></i>
                                    <%= t('progress.locked') %>
                                </button>
                            <% } %>
                        </div>
                    </div>
                <% }); %>
            </div>
        </div>
    </div>

<%- include('../partials/footer') %>
