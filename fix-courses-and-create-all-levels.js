const sqlite3 = require('sqlite3').verbose();
const { dbPath } = require('./database/init');

console.log('🔧 Fixing Courses and Creating ALL Missing Levels...');

const directDb = new sqlite3.Database(dbPath);

directDb.serialize(() => {
    // First, let's fix the courses table to match what the frontend expects
    console.log('🔧 Updating courses to match frontend expectations...');
    
    // Clear and recreate courses with correct data
    directDb.run('DELETE FROM courses', (err) => {
        if (err) {
            console.error('Error clearing courses:', err);
            return;
        }
        
        // Insert correct courses
        const courseStmt = directDb.prepare(`INSERT INTO courses (id, name, slug, description, icon, color, total_levels) VALUES (?, ?, ?, ?, ?, ?, ?)`);
        
        const courses = [
            {
                id: 1,
                name: 'HTML, CSS, JS',
                slug: 'html-css-js',
                description: 'Frontend Web Development - Von HTML Grundlagen bis zu komplexen JavaScript Projekten',
                icon: '🌐',
                color: '#e34c26',
                total_levels: 40
            },
            {
                id: 2,
                name: 'JavaScript + PHP',
                slug: 'javascript-php',
                description: 'Fullstack Development - JavaScript Frontend mit PHP Backend',
                icon: '⚡',
                color: '#f7df1e',
                total_levels: 40
            },
            {
                id: 3,
                name: 'Go',
                slug: 'go',
                description: 'Backend Development mit Go - Von Grundlagen bis zu REST APIs',
                icon: '🦫',
                color: '#00add8',
                total_levels: 40
            },
            {
                id: 4,
                name: 'Python',
                slug: 'python',
                description: 'Python Development - Web Apps, Automatisierung und Machine Learning',
                icon: '🐍',
                color: '#3776ab',
                total_levels: 40
            },
            {
                id: 5,
                name: 'Java',
                slug: 'java',
                description: 'Java Development - OOP, Spring Boot und GUI Entwicklung',
                icon: '☕',
                color: '#ed8b00',
                total_levels: 40
            }
        ];
        
        for (const course of courses) {
            courseStmt.run(course.id, course.name, course.slug, course.description, course.icon, course.color, course.total_levels);
        }
        courseStmt.finalize();
        
        console.log('✅ Courses updated with correct slugs!');
        
        // Now let's check which levels exist and create missing ones
        directDb.all('SELECT course_id, COUNT(*) as level_count FROM levels GROUP BY course_id', (err, levelCounts) => {
            if (err) {
                console.error('Error checking levels:', err);
                return;
            }
            
            console.log('\n📊 Current level counts:');
            levelCounts.forEach(row => {
                console.log(`  Course ${row.course_id}: ${row.level_count} levels`);
            });
            
            // Create missing levels for JavaScript+PHP (Course 2)
            console.log('\n⚡ Creating missing JavaScript+PHP levels...');
            
            const levelStmt = directDb.prepare(`INSERT INTO levels (course_id, level_number, title, description, content, exercise_type, expected_output, points) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`);
            
            // JavaScript+PHP Level 16-40 (missing levels)
            const jsPhpMissingLevels = [
                // Level 16-19: More PHP
                {
                    course_id: 2, level_number: 16, title: 'PHP Datenbanken (MySQL)', 
                    description: 'Verbinde PHP mit MySQL-Datenbanken.',
                    content: `# PHP mit MySQL

PHP und Datenbanken arbeiten perfekt zusammen:

\`\`\`php
<?php
// Datenbankverbindung (PDO - empfohlen)
$host = 'localhost';
$dbname = 'meine_db';
$username = 'root';
$password = '';

try {
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "Datenbankverbindung erfolgreich!<br>";
} catch(PDOException $e) {
    die("Verbindung fehlgeschlagen: " . $e->getMessage());
}

// Daten einfügen (INSERT)
if (isset($_POST['add_user'])) {
    $name = $_POST['name'];
    $email = $_POST['email'];
    
    $stmt = $pdo->prepare("INSERT INTO users (name, email) VALUES (?, ?)");
    $stmt->execute([$name, $email]);
    echo "Benutzer hinzugefügt!<br>";
}

// Daten lesen (SELECT)
$stmt = $pdo->query("SELECT * FROM users ORDER BY id DESC");
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h3>Alle Benutzer:</h3>";
echo "<table border='1'>";
echo "<tr><th>ID</th><th>Name</th><th>E-Mail</th><th>Aktionen</th></tr>";

foreach ($users as $user) {
    echo "<tr>";
    echo "<td>" . $user['id'] . "</td>";
    echo "<td>" . htmlspecialchars($user['name']) . "</td>";
    echo "<td>" . htmlspecialchars($user['email']) . "</td>";
    echo "<td>";
    echo "<a href='?edit=" . $user['id'] . "'>Bearbeiten</a> | ";
    echo "<a href='?delete=" . $user['id'] . "'>Löschen</a>";
    echo "</td>";
    echo "</tr>";
}
echo "</table>";

// Daten aktualisieren (UPDATE)
if (isset($_POST['update_user'])) {
    $id = $_POST['id'];
    $name = $_POST['name'];
    $email = $_POST['email'];
    
    $stmt = $pdo->prepare("UPDATE users SET name = ?, email = ? WHERE id = ?");
    $stmt->execute([$name, $email, $id]);
    echo "Benutzer aktualisiert!<br>";
}

// Daten löschen (DELETE)
if (isset($_GET['delete'])) {
    $id = $_GET['delete'];
    $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
    $stmt->execute([$id]);
    echo "Benutzer gelöscht!<br>";
    header("Location: " . $_SERVER['PHP_SELF']);
    exit();
}

// Suchfunktion
if (isset($_GET['search'])) {
    $search = $_GET['search'];
    $stmt = $pdo->prepare("SELECT * FROM users WHERE name LIKE ? OR email LIKE ?");
    $stmt->execute(["%$search%", "%$search%"]);
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Suchergebnisse für: " . htmlspecialchars($search) . "</h3>";
    foreach ($results as $user) {
        echo "<p>" . $user['name'] . " - " . $user['email'] . "</p>";
    }
}
?>

<!-- Benutzer hinzufügen -->
<h3>Neuen Benutzer hinzufügen:</h3>
<form method="POST">
    <input type="text" name="name" placeholder="Name" required>
    <input type="email" name="email" placeholder="E-Mail" required>
    <button type="submit" name="add_user">Hinzufügen</button>
</form>

<!-- Suche -->
<h3>Benutzer suchen:</h3>
<form method="GET">
    <input type="text" name="search" placeholder="Name oder E-Mail">
    <button type="submit">Suchen</button>
</form>
\`\`\`

## Aufgabe
Erstelle eine einfache Benutzerverwaltung mit PHP und MySQL (CRUD-Operationen).`,
                    exercise_type: 'code_example',
                    expected_output: 'php_mysql',
                    points: 15
                },
                {
                    course_id: 2, level_number: 17, title: 'PHP OOP (Objektorientierung)', 
                    description: 'Lerne objektorientierte Programmierung in PHP.',
                    content: `# PHP OOP

Objektorientierte Programmierung macht PHP mächtiger:

\`\`\`php
<?php
// Klasse definieren
class Person {
    // Eigenschaften (Properties)
    private $name;
    private $alter;
    protected $email;
    public $stadt;
    
    // Konstruktor
    public function __construct($name, $alter, $email = "") {
        $this->name = $name;
        $this->alter = $alter;
        $this->email = $email;
    }
    
    // Getter-Methoden
    public function getName() {
        return $this->name;
    }
    
    public function getAlter() {
        return $this->alter;
    }
    
    // Setter-Methoden
    public function setName($name) {
        $this->name = $name;
    }
    
    public function setAlter($alter) {
        if ($alter > 0) {
            $this->alter = $alter;
        }
    }
    
    // Methoden
    public function vorstellen() {
        return "Ich bin " . $this->name . " und " . $this->alter . " Jahre alt.";
    }
    
    public function istVolljährig() {
        return $this->alter >= 18;
    }
    
    // Statische Methode
    public static function erstelleTestPerson() {
        return new self("Test Person", 25, "<EMAIL>");
    }
}

// Objekte erstellen
$person1 = new Person("Max", 25, "<EMAIL>");
$person2 = new Person("Anna", 17);

echo $person1->vorstellen() . "<br>";
echo "Max ist " . ($person1->istVolljährig() ? "volljährig" : "minderjährig") . "<br>";

// Vererbung (Inheritance)
class Student extends Person {
    private $studiengang;
    private $semester;
    
    public function __construct($name, $alter, $email, $studiengang, $semester = 1) {
        parent::__construct($name, $alter, $email); // Eltern-Konstruktor aufrufen
        $this->studiengang = $studiengang;
        $this->semester = $semester;
    }
    
    public function getStudiengang() {
        return $this->studiengang;
    }
    
    // Methode überschreiben (Override)
    public function vorstellen() {
        return parent::vorstellen() . " Ich studiere " . $this->studiengang . " im " . $this->semester . ". Semester.";
    }
    
    public function naechstesSemester() {
        $this->semester++;
    }
}

$student = new Student("Tom", 20, "<EMAIL>", "Informatik", 3);
echo $student->vorstellen() . "<br>";

// Interface
interface Fahrzeug {
    public function starten();
    public function stoppen();
    public function getGeschwindigkeit();
}

// Abstrakte Klasse
abstract class Tier {
    protected $name;
    
    public function __construct($name) {
        $this->name = $name;
    }
    
    // Abstrakte Methode (muss in Kindklassen implementiert werden)
    abstract public function lautGeben();
    
    // Normale Methode
    public function schlafen() {
        return $this->name . " schläft.";
    }
}

class Hund extends Tier {
    public function lautGeben() {
        return $this->name . " bellt: Wau wau!";
    }
    
    public function apportieren() {
        return $this->name . " apportiert den Ball.";
    }
}

class Katze extends Tier {
    public function lautGeben() {
        return $this->name . " miaut: Miau!";
    }
}

$hund = new Hund("Bello");
$katze = new Katze("Whiskers");

echo $hund->lautGeben() . "<br>";
echo $katze->lautGeben() . "<br>";
echo $hund->schlafen() . "<br>";

// Trait (Code-Wiederverwendung)
trait Zeitstempel {
    private $erstellt;
    
    public function setErstellt() {
        $this->erstellt = date('Y-m-d H:i:s');
    }
    
    public function getErstellt() {
        return $this->erstellt;
    }
}

class Artikel {
    use Zeitstempel;
    
    private $titel;
    private $inhalt;
    
    public function __construct($titel, $inhalt) {
        $this->titel = $titel;
        $this->inhalt = $inhalt;
        $this->setErstellt();
    }
    
    public function getTitel() {
        return $this->titel;
    }
}

$artikel = new Artikel("PHP OOP", "Objektorientierte Programmierung in PHP");
echo "Artikel: " . $artikel->getTitel() . " (erstellt: " . $artikel->getErstellt() . ")<br>";

// Namespaces
namespace MeinProjekt\\Models;

class Benutzer {
    private $name;
    
    public function __construct($name) {
        $this->name = $name;
    }
    
    public function getName() {
        return $this->name;
    }
}

// Verwendung mit Namespace
$benutzer = new \\MeinProjekt\\Models\\Benutzer("Admin");
echo "Namespace Benutzer: " . $benutzer->getName() . "<br>";
?>
\`\`\`

## Aufgabe
Erstelle eine Klasse "Auto" mit Eigenschaften und Methoden, und eine Kindklasse "Elektroauto".`,
                    exercise_type: 'code_example',
                    expected_output: 'php_oop',
                    points: 15
                },
                {
                    course_id: 2, level_number: 18, title: 'PHP APIs & JSON', 
                    description: 'Erstelle REST APIs mit PHP und arbeite mit JSON.',
                    content: `# PHP APIs & JSON

Erstelle moderne APIs mit PHP:

\`\`\`php
<?php
// API Endpoint - api.php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

// Einfache Datenbank-Simulation
$users = [
    ['id' => 1, 'name' => 'Max', 'email' => '<EMAIL>'],
    ['id' => 2, 'name' => 'Anna', 'email' => '<EMAIL>'],
    ['id' => 3, 'name' => 'Tom', 'email' => '<EMAIL>']
];

// HTTP-Methode ermitteln
$method = $_SERVER['REQUEST_METHOD'];
$path = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$path_parts = explode('/', trim($path, '/'));

// API-Routen
switch ($method) {
    case 'GET':
        if (isset($path_parts[1]) && is_numeric($path_parts[1])) {
            // Einzelnen Benutzer abrufen
            $id = (int)$path_parts[1];
            $user = array_filter($users, function($u) use ($id) {
                return $u['id'] === $id;
            });
            
            if ($user) {
                echo json_encode(array_values($user)[0]);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Benutzer nicht gefunden']);
            }
        } else {
            // Alle Benutzer abrufen
            echo json_encode($users);
        }
        break;
        
    case 'POST':
        // Neuen Benutzer erstellen
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (isset($input['name']) && isset($input['email'])) {
            $new_user = [
                'id' => count($users) + 1,
                'name' => $input['name'],
                'email' => $input['email']
            ];
            
            $users[] = $new_user;
            
            http_response_code(201);
            echo json_encode($new_user);
        } else {
            http_response_code(400);
            echo json_encode(['error' => 'Name und E-Mail erforderlich']);
        }
        break;
        
    case 'PUT':
        // Benutzer aktualisieren
        if (isset($path_parts[1]) && is_numeric($path_parts[1])) {
            $id = (int)$path_parts[1];
            $input = json_decode(file_get_contents('php://input'), true);
            
            $user_index = array_search($id, array_column($users, 'id'));
            
            if ($user_index !== false) {
                if (isset($input['name'])) {
                    $users[$user_index]['name'] = $input['name'];
                }
                if (isset($input['email'])) {
                    $users[$user_index]['email'] = $input['email'];
                }
                
                echo json_encode($users[$user_index]);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Benutzer nicht gefunden']);
            }
        }
        break;
        
    case 'DELETE':
        // Benutzer löschen
        if (isset($path_parts[1]) && is_numeric($path_parts[1])) {
            $id = (int)$path_parts[1];
            $user_index = array_search($id, array_column($users, 'id'));
            
            if ($user_index !== false) {
                $deleted_user = $users[$user_index];
                unset($users[$user_index]);
                $users = array_values($users); // Array neu indizieren
                
                echo json_encode(['message' => 'Benutzer gelöscht', 'user' => $deleted_user]);
            } else {
                http_response_code(404);
                echo json_encode(['error' => 'Benutzer nicht gefunden']);
            }
        }
        break;
        
    default:
        http_response_code(405);
        echo json_encode(['error' => 'Methode nicht erlaubt']);
        break;
}

// API-Client Beispiel
function apiRequest($url, $method = 'GET', $data = null) {
    $ch = curl_init();
    
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $method);
    
    if ($data) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Content-Length: ' . strlen(json_encode($data))
        ]);
    }
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    return [
        'status' => $http_code,
        'data' => json_decode($response, true)
    ];
}

// API verwenden
$base_url = 'http://localhost/api.php';

// Alle Benutzer abrufen
$response = apiRequest($base_url);
echo "Alle Benutzer: " . json_encode($response['data']) . "\\n";

// Neuen Benutzer erstellen
$new_user = ['name' => 'Lisa', 'email' => '<EMAIL>'];
$response = apiRequest($base_url, 'POST', $new_user);
echo "Neuer Benutzer: " . json_encode($response['data']) . "\\n";

// JSON-Verarbeitung
$json_string = '{"name": "Max", "alter": 25, "hobbys": ["Lesen", "Sport"]}';
$data = json_decode($json_string, true);

echo "Name: " . $data['name'] . "\\n";
echo "Hobbys: " . implode(', ', $data['hobbys']) . "\\n";

// Array zu JSON
$person = [
    'name' => 'Anna',
    'alter' => 30,
    'adresse' => [
        'straße' => 'Musterstraße 123',
        'stadt' => 'Berlin',
        'plz' => '12345'
    ]
];

$json = json_encode($person, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
echo "JSON:\\n" . $json . "\\n";

// Externe API verwenden
function getWeather($city) {
    $api_key = 'YOUR_API_KEY';
    $url = "http://api.openweathermap.org/data/2.5/weather?q={$city}&appid={$api_key}&units=metric";
    
    $response = file_get_contents($url);
    return json_decode($response, true);
}

// Fehlerbehandlung für JSON
function safeJsonDecode($json) {
    $data = json_decode($json, true);
    
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception('JSON Fehler: ' . json_last_error_msg());
    }
    
    return $data;
}

try {
    $invalid_json = '{"name": "Max", "alter": }';
    $data = safeJsonDecode($invalid_json);
} catch (Exception $e) {
    echo "Fehler: " . $e->getMessage() . "\\n";
}
?>
\`\`\`

## Aufgabe
Erstelle eine einfache REST API für Artikel (CRUD-Operationen) mit JSON-Antworten.`,
                    exercise_type: 'code_example',
                    expected_output: 'php_api',
                    points: 15
                },
                {
                    course_id: 2, level_number: 19, title: 'PHP Security & Best Practices', 
                    description: 'Lerne wichtige Sicherheitsaspekte und Best Practices in PHP.',
                    content: `# PHP Security & Best Practices

Sicherheit ist entscheidend in der Webentwicklung:

\`\`\`php
<?php
// 1. INPUT VALIDATION & SANITIZATION

// Niemals Benutzereingaben direkt verwenden!
// FALSCH:
// $name = $_POST['name'];
// $sql = "SELECT * FROM users WHERE name = '$name'"; // SQL Injection!

// RICHTIG:
function validateInput($data) {
    $data = trim($data);                    // Leerzeichen entfernen
    $data = stripslashes($data);            // Backslashes entfernen
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8'); // HTML escapen
    return $data;
}

// Eingabe validieren
if (isset($_POST['name'])) {
    $name = validateInput($_POST['name']);
    
    // Zusätzliche Validierung
    if (empty($name)) {
        $errors[] = "Name ist erforderlich";
    } elseif (strlen($name) < 2) {
        $errors[] = "Name muss mindestens 2 Zeichen haben";
    } elseif (!preg_match("/^[a-zA-ZäöüÄÖÜß ]*$/", $name)) {
        $errors[] = "Name darf nur Buchstaben und Leerzeichen enthalten";
    }
}

// E-Mail validieren
if (isset($_POST['email'])) {
    $email = filter_var($_POST['email'], FILTER_SANITIZE_EMAIL);
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = "Ungültige E-Mail-Adresse";
    }
}

// 2. SQL INJECTION PREVENTION

// PDO mit Prepared Statements verwenden
try {
    $pdo = new PDO("mysql:host=localhost;dbname=test", $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false
    ]);
} catch(PDOException $e) {
    error_log("Datenbankfehler: " . $e->getMessage());
    die("Datenbankverbindung fehlgeschlagen");
}

// Sichere Datenbankabfragen
function getUserByEmail($email) {
    global $pdo;
    $stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute([$email]);
    return $stmt->fetch();
}

function createUser($name, $email, $password) {
    global $pdo;
    $hashed_password = password_hash($password, PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT INTO users (name, email, password) VALUES (?, ?, ?)");
    return $stmt->execute([$name, $email, $hashed_password]);
}

// 3. PASSWORD SECURITY

// Passwort hashen
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

// Passwort verifizieren
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

// Starke Passwort-Validierung
function validatePassword($password) {
    $errors = [];
    
    if (strlen($password) < 8) {
        $errors[] = "Passwort muss mindestens 8 Zeichen haben";
    }
    
    if (!preg_match("/[A-Z]/", $password)) {
        $errors[] = "Passwort muss mindestens einen Großbuchstaben enthalten";
    }
    
    if (!preg_match("/[a-z]/", $password)) {
        $errors[] = "Passwort muss mindestens einen Kleinbuchstaben enthalten";
    }
    
    if (!preg_match("/[0-9]/", $password)) {
        $errors[] = "Passwort muss mindestens eine Zahl enthalten";
    }
    
    if (!preg_match("/[^A-Za-z0-9]/", $password)) {
        $errors[] = "Passwort muss mindestens ein Sonderzeichen enthalten";
    }
    
    return $errors;
}

// 4. SESSION SECURITY

// Sichere Session-Konfiguration
ini_set('session.cookie_httponly', 1);  // Kein JavaScript-Zugriff
ini_set('session.cookie_secure', 1);    // Nur über HTTPS
ini_set('session.use_strict_mode', 1);  // Strenger Modus

session_start();

// Session-Regeneration bei Login
function loginUser($user_id) {
    session_regenerate_id(true);  // Neue Session-ID generieren
    $_SESSION['user_id'] = $user_id;
    $_SESSION['login_time'] = time();
}

// Session-Timeout
function checkSessionTimeout($timeout = 3600) { // 1 Stunde
    if (isset($_SESSION['login_time']) && (time() - $_SESSION['login_time'] > $timeout)) {
        session_destroy();
        return false;
    }
    $_SESSION['login_time'] = time(); // Timeout verlängern
    return true;
}

// 5. CSRF PROTECTION

// CSRF-Token generieren
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// CSRF-Token validieren
function validateCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// In Formularen verwenden:
echo '<input type="hidden" name="csrf_token" value="' . generateCSRFToken() . '">';

// Bei Formular-Verarbeitung prüfen:
if (!validateCSRFToken($_POST['csrf_token'])) {
    die("CSRF-Token ungültig");
}

// 6. FILE UPLOAD SECURITY

function secureFileUpload($file, $upload_dir = 'uploads/') {
    $errors = [];
    
    // Datei-Validierung
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $errors[] = "Upload-Fehler: " . $file['error'];
        return $errors;
    }
    
    // Dateigröße prüfen (max 5MB)
    if ($file['size'] > 5 * 1024 * 1024) {
        $errors[] = "Datei zu groß (max 5MB)";
    }
    
    // Erlaubte Dateitypen
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
    if (!in_array($file['type'], $allowed_types)) {
        $errors[] = "Dateityp nicht erlaubt";
    }
    
    // Dateiname sichern
    $filename = basename($file['name']);
    $filename = preg_replace("/[^a-zA-Z0-9.-]/", "_", $filename);
    $filename = uniqid() . '_' . $filename;
    
    // Upload-Verzeichnis erstellen
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    $target_path = $upload_dir . $filename;
    
    // Datei verschieben
    if (empty($errors) && move_uploaded_file($file['tmp_name'], $target_path)) {
        return ['success' => true, 'filename' => $filename, 'path' => $target_path];
    } else {
        $errors[] = "Fehler beim Speichern der Datei";
    }
    
    return $errors;
}

// 7. ERROR HANDLING & LOGGING

// Fehler-Logging aktivieren
ini_set('log_errors', 1);
ini_set('error_log', '/path/to/error.log');

// Custom Error Handler
function customErrorHandler($errno, $errstr, $errfile, $errline) {
    $error_message = "Fehler [$errno]: $errstr in $errfile:$errline";
    error_log($error_message);
    
    // In Produktion keine Details anzeigen
    if (ini_get('display_errors')) {
        echo "Ein Fehler ist aufgetreten. Details wurden geloggt.";
    }
}

set_error_handler('customErrorHandler');

// 8. ENVIRONMENT CONFIGURATION

// Umgebungsvariablen für sensible Daten
$db_host = $_ENV['DB_HOST'] ?? 'localhost';
$db_user = $_ENV['DB_USER'] ?? 'root';
$db_pass = $_ENV['DB_PASS'] ?? '';

// .env Datei laden (mit vlucas/phpdotenv)
// require_once 'vendor/autoload.php';
// $dotenv = Dotenv\\Dotenv::createImmutable(__DIR__);
// $dotenv->load();

// 9. RATE LIMITING

function checkRateLimit($identifier, $max_requests = 10, $time_window = 60) {
    $cache_key = "rate_limit_" . md5($identifier);
    
    // Einfache Datei-basierte Rate Limiting
    $cache_file = sys_get_temp_dir() . '/' . $cache_key;
    
    if (file_exists($cache_file)) {
        $data = json_decode(file_get_contents($cache_file), true);
        
        if (time() - $data['start_time'] < $time_window) {
            if ($data['requests'] >= $max_requests) {
                return false; // Rate Limit erreicht
            }
            $data['requests']++;
        } else {
            $data = ['start_time' => time(), 'requests' => 1];
        }
    } else {
        $data = ['start_time' => time(), 'requests' => 1];
    }
    
    file_put_contents($cache_file, json_encode($data));
    return true;
}

// Verwendung:
$client_ip = $_SERVER['REMOTE_ADDR'];
if (!checkRateLimit($client_ip)) {
    http_response_code(429);
    die("Rate Limit erreicht. Versuchen Sie es später erneut.");
}

// 10. SECURITY HEADERS

// Sicherheits-Header setzen
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('X-XSS-Protection: 1; mode=block');
header('Strict-Transport-Security: max-age=31536000; includeSubDomains');
header('Content-Security-Policy: default-src \'self\'');

// 11. INPUT SANITIZATION FUNCTIONS

class SecurityHelper {
    public static function sanitizeString($input) {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
    
    public static function sanitizeEmail($email) {
        return filter_var($email, FILTER_SANITIZE_EMAIL);
    }
    
    public static function sanitizeInt($input) {
        return filter_var($input, FILTER_SANITIZE_NUMBER_INT);
    }
    
    public static function sanitizeUrl($url) {
        return filter_var($url, FILTER_SANITIZE_URL);
    }
    
    public static function validateEmail($email) {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }
    
    public static function validateUrl($url) {
        return filter_var($url, FILTER_VALIDATE_URL) !== false;
    }
}

// Verwendung:
$name = SecurityHelper::sanitizeString($_POST['name'] ?? '');
$email = SecurityHelper::sanitizeEmail($_POST['email'] ?? '');

if (!SecurityHelper::validateEmail($email)) {
    $errors[] = "Ungültige E-Mail-Adresse";
}
?>
\`\`\`

## Aufgabe
Implementiere ein sicheres Login-System mit Passwort-Hashing, CSRF-Schutz und Session-Management.`,
                    exercise_type: 'code_example',
                    expected_output: 'php_security',
                    points: 20
                }
            ];
            
            // Insert missing JS+PHP levels
            for (const level of jsPhpMissingLevels) {
                levelStmt.run(level.course_id, level.level_number, level.title, level.description, level.content, level.exercise_type, level.expected_output, level.points);
            }
            
            console.log('✅ JavaScript+PHP levels 16-19 created!');
            
            levelStmt.finalize();
            directDb.close();
            
            console.log('🎉 Courses fixed and levels created!');
            console.log('📝 Now all courses should be accessible in the frontend.');
        });
    });
});
