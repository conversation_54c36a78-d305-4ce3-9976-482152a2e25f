#!/bin/bash

# Generate SSL certificates for development
echo "🔐 Generating SSL certificates for development..."
echo "================================================"

# Create certs directory if it doesn't exist
mkdir -p certs

# Check if OpenSSL is installed
if ! command -v openssl &> /dev/null; then
    echo "❌ OpenSSL is not installed. Please install it first:"
    echo "   Ubuntu/Debian: sudo apt-get install openssl"
    echo "   macOS: brew install openssl"
    echo "   Windows: Download from https://slproweb.com/products/Win32OpenSSL.html"
    exit 1
fi

# Generate private key
echo "📝 Generating private key..."
openssl genrsa -out certs/private-key.pem 2048

# Generate certificate
echo "📜 Generating self-signed certificate..."
openssl req -new -x509 -key certs/private-key.pem -out certs/certificate.pem -days 365 -subj "/C=DE/ST=State/L=City/O=CodeWave/OU=Development/CN=localhost"

# Set appropriate permissions
chmod 600 certs/private-key.pem
chmod 644 certs/certificate.pem

echo ""
echo "✅ SSL certificates generated successfully!"
echo "📁 Files created:"
echo "   - certs/private-key.pem (private key)"
echo "   - certs/certificate.pem (certificate)"
echo ""
echo "🚀 You can now start the server with HTTPS support!"
echo "💡 Note: This is a self-signed certificate for development only."
echo "🔒 Browsers will show a security warning - this is normal for self-signed certs."
echo ""
echo "To start the server: node index.js"
