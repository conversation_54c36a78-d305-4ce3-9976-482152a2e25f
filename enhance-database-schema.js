const Database = require('./database/db');

console.log('🔧 Enhancing Database Schema for Requirements-Based Validation...\n');

const db = new Database();

async function enhanceDatabaseSchema() {
    try {
        console.log('1. Adding requirements column to levels table...');
        
        // Add requirements column to levels table
        await new Promise((resolve, reject) => {
            db.db.run(`ALTER TABLE levels ADD COLUMN requirements TEXT DEFAULT NULL`, (err) => {
                if (err && !err.message.includes('duplicate column name')) {
                    reject(err);
                } else {
                    resolve();
                }
            });
        });
        
        console.log('   ✅ Requirements column added to levels table');

        console.log('\n2. Adding validation_metadata column for enhanced validation...');
        
        // Add validation metadata column
        await new Promise((resolve, reject) => {
            db.db.run(`ALTER TABLE levels ADD COLUMN validation_metadata TEXT DEFAULT NULL`, (err) => {
                if (err && !err.message.includes('duplicate column name')) {
                    reject(err);
                } else {
                    resolve();
                }
            });
        });
        
        console.log('   ✅ Validation metadata column added');

        console.log('\n3. Adding learning_objectives column...');
        
        // Add learning objectives column
        await new Promise((resolve, reject) => {
            db.db.run(`ALTER TABLE levels ADD COLUMN learning_objectives TEXT DEFAULT NULL`, (err) => {
                if (err && !err.message.includes('duplicate column name')) {
                    reject(err);
                } else {
                    resolve();
                }
            });
        });
        
        console.log('   ✅ Learning objectives column added');

        console.log('\n4. Creating validation_requirements table for requirement definitions...');
        
        // Create validation requirements table
        await new Promise((resolve, reject) => {
            db.db.run(`
                CREATE TABLE IF NOT EXISTS validation_requirements (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    requirement_key VARCHAR(100) UNIQUE NOT NULL,
                    requirement_name VARCHAR(200) NOT NULL,
                    description TEXT,
                    detection_pattern TEXT NOT NULL,
                    requirement_type VARCHAR(50) NOT NULL, -- 'syntax', 'structure', 'output', 'concept'
                    language VARCHAR(20) NOT NULL, -- 'javascript', 'php', 'html', 'css'
                    difficulty_level INTEGER DEFAULT 1, -- 1-5 scale
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            `, (err) => {
                if (err) reject(err);
                else resolve();
            });
        });
        
        console.log('   ✅ Validation requirements table created');

        console.log('\n5. Populating validation requirements with comprehensive definitions...');
        
        // Define comprehensive validation requirements
        const requirements = [
            // JavaScript/Node.js Requirements
            {
                key: 'variable_declaration',
                name: 'Variable Declaration',
                description: 'Declares variables using let, const, or var',
                pattern: '\\b(let|const|var)\\s+\\w+',
                type: 'syntax',
                language: 'javascript',
                difficulty: 1
            },
            {
                key: 'console_log_usage',
                name: 'Console Output',
                description: 'Uses console.log() to output information',
                pattern: 'console\\.log\\s*\\(',
                type: 'syntax',
                language: 'javascript',
                difficulty: 1
            },
            {
                key: 'function_declaration',
                name: 'Function Declaration',
                description: 'Declares a function using function keyword',
                pattern: 'function\\s+\\w+\\s*\\(',
                type: 'structure',
                language: 'javascript',
                difficulty: 2
            },
            {
                key: 'function_expression',
                name: 'Function Expression',
                description: 'Creates function using expression syntax',
                pattern: '(const|let|var)\\s+\\w+\\s*=\\s*function',
                type: 'structure',
                language: 'javascript',
                difficulty: 2
            },
            {
                key: 'arrow_function',
                name: 'Arrow Function',
                description: 'Uses ES6 arrow function syntax',
                pattern: '\\w+\\s*=>|\\(.*\\)\\s*=>',
                type: 'syntax',
                language: 'javascript',
                difficulty: 3
            },
            {
                key: 'function_call',
                name: 'Function Call',
                description: 'Calls a function with parentheses',
                pattern: '\\w+\\s*\\([^)]*\\)',
                type: 'structure',
                language: 'javascript',
                difficulty: 1
            },
            {
                key: 'return_statement',
                name: 'Return Statement',
                description: 'Uses return statement in function',
                pattern: '\\breturn\\b',
                type: 'structure',
                language: 'javascript',
                difficulty: 2
            },
            {
                key: 'require_statement',
                name: 'Require Module',
                description: 'Uses require() to import modules',
                pattern: 'require\\s*\\(',
                type: 'structure',
                language: 'javascript',
                difficulty: 2
            },
            {
                key: 'module_exports',
                name: 'Module Exports',
                description: 'Exports functionality using module.exports',
                pattern: 'module\\.exports',
                type: 'structure',
                language: 'javascript',
                difficulty: 2
            },
            {
                key: 'async_await',
                name: 'Async/Await',
                description: 'Uses async/await for asynchronous operations',
                pattern: '\\b(async|await)\\b',
                type: 'concept',
                language: 'javascript',
                difficulty: 4
            },
            {
                key: 'template_literals',
                name: 'Template Literals',
                description: 'Uses template literals with backticks',
                pattern: '`[^`]*\\$\\{[^}]+\\}[^`]*`',
                type: 'syntax',
                language: 'javascript',
                difficulty: 3
            },
            {
                key: 'destructuring',
                name: 'Destructuring Assignment',
                description: 'Uses destructuring to extract values',
                pattern: '(const|let|var)\\s*\\{[^}]+\\}\\s*=|\\[[^\\]]+\\]\\s*=',
                type: 'syntax',
                language: 'javascript',
                difficulty: 3
            },
            {
                key: 'for_loop',
                name: 'For Loop',
                description: 'Uses for loop for iteration',
                pattern: '\\bfor\\s*\\(',
                type: 'structure',
                language: 'javascript',
                difficulty: 2
            },
            {
                key: 'while_loop',
                name: 'While Loop',
                description: 'Uses while loop for iteration',
                pattern: '\\bwhile\\s*\\(',
                type: 'structure',
                language: 'javascript',
                difficulty: 2
            },
            {
                key: 'forEach_method',
                name: 'forEach Method',
                description: 'Uses forEach method for array iteration',
                pattern: '\\.forEach\\s*\\(',
                type: 'concept',
                language: 'javascript',
                difficulty: 3
            },
            
            // PHP Requirements
            {
                key: 'php_opening_tag',
                name: 'PHP Opening Tag',
                description: 'Uses <?php opening tag',
                pattern: '<\\?php',
                type: 'syntax',
                language: 'php',
                difficulty: 1
            },
            {
                key: 'echo_statement',
                name: 'Echo Statement',
                description: 'Uses echo to output content',
                pattern: '\\becho\\b',
                type: 'syntax',
                language: 'php',
                difficulty: 1
            },
            {
                key: 'print_statement',
                name: 'Print Statement',
                description: 'Uses print to output content',
                pattern: '\\bprint\\b',
                type: 'syntax',
                language: 'php',
                difficulty: 1
            },
            {
                key: 'php_variable',
                name: 'PHP Variable',
                description: 'Declares PHP variable with $ prefix',
                pattern: '\\$\\w+',
                type: 'syntax',
                language: 'php',
                difficulty: 1
            },
            {
                key: 'array_declaration',
                name: 'Array Declaration',
                description: 'Declares an array using array() or []',
                pattern: '(array\\s*\\(|\\[)',
                type: 'structure',
                language: 'php',
                difficulty: 2
            },
            {
                key: 'array_access',
                name: 'Array Access',
                description: 'Accesses array elements using brackets',
                pattern: '\\$\\w+\\[[^\\]]+\\]',
                type: 'structure',
                language: 'php',
                difficulty: 2
            },
            {
                key: 'foreach_loop',
                name: 'Foreach Loop',
                description: 'Uses foreach loop for array iteration',
                pattern: '\\bforeach\\s*\\(',
                type: 'structure',
                language: 'php',
                difficulty: 2
            },
            {
                key: 'php_function',
                name: 'PHP Function',
                description: 'Declares PHP function',
                pattern: '\\bfunction\\s+\\w+\\s*\\(',
                type: 'structure',
                language: 'php',
                difficulty: 2
            },
            {
                key: 'class_declaration',
                name: 'Class Declaration',
                description: 'Declares a PHP class',
                pattern: '\\bclass\\s+\\w+',
                type: 'structure',
                language: 'php',
                difficulty: 3
            },
            {
                key: 'mysqli_connection',
                name: 'MySQLi Connection',
                description: 'Establishes database connection using MySQLi',
                pattern: 'mysqli_connect|new\\s+mysqli',
                type: 'concept',
                language: 'php',
                difficulty: 4
            }
        ];

        // Insert requirements
        const stmt = db.db.prepare(`
            INSERT OR REPLACE INTO validation_requirements 
            (requirement_key, requirement_name, description, detection_pattern, requirement_type, language, difficulty_level) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `);

        for (const req of requirements) {
            stmt.run([req.key, req.name, req.description, req.pattern, req.type, req.language, req.difficulty]);
        }
        
        stmt.finalize();
        
        console.log(`   ✅ ${requirements.length} validation requirements populated`);

        console.log('\n6. Verifying schema enhancements...');
        
        // Verify the schema
        const tableInfo = await new Promise((resolve, reject) => {
            db.db.all("PRAGMA table_info(levels)", (err, rows) => {
                if (err) reject(err);
                else resolve(rows);
            });
        });
        
        const hasRequirements = tableInfo.some(col => col.name === 'requirements');
        const hasValidationMetadata = tableInfo.some(col => col.name === 'validation_metadata');
        const hasLearningObjectives = tableInfo.some(col => col.name === 'learning_objectives');
        
        console.log(`   ✅ Requirements column: ${hasRequirements ? 'Present' : 'Missing'}`);
        console.log(`   ✅ Validation metadata column: ${hasValidationMetadata ? 'Present' : 'Missing'}`);
        console.log(`   ✅ Learning objectives column: ${hasLearningObjectives ? 'Present' : 'Missing'}`);

        console.log('\n🎉 Database schema enhancement complete!');
        console.log('\n📋 SUMMARY:');
        console.log('✅ Requirements column added to levels table');
        console.log('✅ Validation metadata column added');
        console.log('✅ Learning objectives column added');
        console.log('✅ Validation requirements table created');
        console.log(`✅ ${requirements.length} requirement definitions populated`);
        console.log('✅ Schema ready for requirements-based validation');

    } catch (error) {
        console.error('❌ Error enhancing database schema:', error);
    } finally {
        db.close();
    }
}

// Run the schema enhancement
enhanceDatabaseSchema();
