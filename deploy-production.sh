#!/bin/bash

# CodeWave Production Deployment Script
# For lxnd.cloud with Let's Encrypt certificates

echo "🚀 CodeWave Production Deployment"
echo "=================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    print_error "This script must be run as root for Let's Encrypt certificate access"
    echo "Usage: sudo ./deploy-production.sh"
    exit 1
fi

print_status "Starting production deployment for lxnd.cloud..."

# Check if Let's Encrypt certificates exist
CERT_DIR="/etc/letsencrypt/live/lxnd.cloud"
PRIVKEY="$CERT_DIR/privkey.pem"
FULLCHAIN="$CERT_DIR/fullchain.pem"

print_status "Checking Let's Encrypt certificates..."

if [ ! -f "$PRIVKEY" ]; then
    print_error "Private key not found: $PRIVKEY"
    exit 1
fi

if [ ! -f "$FULLCHAIN" ]; then
    print_error "Certificate chain not found: $FULLCHAIN"
    exit 1
fi

print_success "Let's Encrypt certificates found"

# Check certificate validity
print_status "Checking certificate validity..."
CERT_EXPIRY=$(openssl x509 -in "$FULLCHAIN" -noout -enddate | cut -d= -f2)
print_status "Certificate expires: $CERT_EXPIRY"

# Check if certificate expires in less than 30 days
EXPIRY_TIMESTAMP=$(date -d "$CERT_EXPIRY" +%s)
CURRENT_TIMESTAMP=$(date +%s)
DAYS_UNTIL_EXPIRY=$(( (EXPIRY_TIMESTAMP - CURRENT_TIMESTAMP) / 86400 ))

if [ $DAYS_UNTIL_EXPIRY -lt 30 ]; then
    print_warning "Certificate expires in $DAYS_UNTIL_EXPIRY days"
    print_status "Consider renewing: sudo certbot renew"
else
    print_success "Certificate is valid for $DAYS_UNTIL_EXPIRY days"
fi

# Check Node.js installation
print_status "Checking Node.js installation..."
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed"
    print_status "Install Node.js: curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash - && sudo apt-get install -y nodejs"
    exit 1
fi

NODE_VERSION=$(node --version)
print_success "Node.js version: $NODE_VERSION"

# Check npm dependencies
print_status "Checking npm dependencies..."
if [ ! -d "node_modules" ]; then
    print_status "Installing npm dependencies..."
    npm install
    if [ $? -eq 0 ]; then
        print_success "Dependencies installed successfully"
    else
        print_error "Failed to install dependencies"
        exit 1
    fi
else
    print_success "Dependencies already installed"
fi

# Check database
print_status "Checking database..."
if [ ! -f "database/learning_platform.db" ]; then
    print_warning "Database not found, initializing..."
    node database/init.js
    if [ $? -eq 0 ]; then
        print_success "Database initialized"
    else
        print_error "Failed to initialize database"
        exit 1
    fi
else
    print_success "Database found"
fi

# Set up systemd service (optional)
print_status "Setting up systemd service..."
SERVICE_FILE="/etc/systemd/system/codewave.service"

if [ ! -f "$SERVICE_FILE" ]; then
    cat > "$SERVICE_FILE" << EOF
[Unit]
Description=CodeWave Learning Platform
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=$(pwd)
ExecStart=/usr/bin/node index.js
Restart=always
RestartSec=10
Environment=NODE_ENV=production
Environment=HTTPS_PORT=443

[Install]
WantedBy=multi-user.target
EOF

    systemctl daemon-reload
    systemctl enable codewave
    print_success "Systemd service created and enabled"
else
    print_success "Systemd service already exists"
fi

# Set up firewall rules
print_status "Checking firewall configuration..."
if command -v ufw &> /dev/null; then
    ufw allow 80/tcp
    ufw allow 443/tcp
    print_success "Firewall rules updated (ports 80, 443)"
else
    print_warning "UFW not found, ensure ports 80 and 443 are open"
fi

# Final deployment options
echo ""
print_success "🎉 Production deployment ready!"
echo ""
echo "Choose deployment method:"
echo "1. Start with systemd service (recommended)"
echo "2. Start manually with HTTPS auto-detection"
echo "3. Start manually and choose HTTP/HTTPS"
echo ""
read -p "Enter choice (1-3): " choice

case $choice in
    1)
        print_status "Starting CodeWave service..."
        systemctl start codewave
        systemctl status codewave --no-pager
        print_success "Service started! Check status with: sudo systemctl status codewave"
        print_status "View logs with: sudo journalctl -u codewave -f"
        ;;
    2)
        print_status "Starting with HTTPS auto-detection..."
        echo "Y" | node index.js
        ;;
    3)
        print_status "Starting interactive mode..."
        node index.js
        ;;
    *)
        print_warning "Invalid choice. Start manually with: sudo node index.js"
        ;;
esac

echo ""
print_success "🌐 CodeWave should now be available at:"
print_success "   - https://lxnd.cloud (HTTPS with Let's Encrypt)"
print_success "   - http://lxnd.cloud (HTTP fallback)"
echo ""
print_status "📋 Useful commands:"
print_status "   - Check service: sudo systemctl status codewave"
print_status "   - View logs: sudo journalctl -u codewave -f"
print_status "   - Restart service: sudo systemctl restart codewave"
print_status "   - Renew certificates: sudo certbot renew"
echo ""
