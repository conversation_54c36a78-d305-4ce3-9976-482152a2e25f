const sqlite3 = require('sqlite3').verbose();
const { dbPath } = require('./database/init');

console.log('⚡ Creating JavaScript Levels 11-19...');

const directDb = new sqlite3.Database(dbPath);

directDb.serialize(() => {
    const levelStmt = directDb.prepare(`INSERT INTO levels (course_id, level_number, title, description, content, exercise_type, expected_output, points) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`);
    
    // Level 11-19: JavaScript Grundlagen
    const jsLevels = [
        {
            course_id: 1, level_number: 11, title: 'Erste Variablen & Datentypen', 
            description: 'Lerne JavaScript-Variablen und die verschiedenen Datentypen.',
            content: `# Erste Variablen & Datentypen

JavaScript ist die Programmiersprache des Webs:

\`\`\`javascript
// Variablen deklarieren
var altesFormat = "Nicht mehr empfohlen";
let veraenderlich = "Kann geändert werden";
const unveraenderlich = "Bleibt konstant";

// Datentypen
let zahl = 42;                    // Number
let kommazahl = 3.14159;          // Number (auch Dezimalzahlen)
let text = "Hallo Welt";          // String
let wahrheit = true;              // Boolean
let nichts = null;                // Null
let undefiniert;                  // Undefined
let symbol = Symbol("id");        // Symbol (ES6)

// Strings
let vorname = "Max";
let nachname = 'Mustermann';
let vollername = \`\${vorname} \${nachname}\`;  // Template Literal

// Zahlen
let ganzzahl = 100;
let negativ = -50;
let wissenschaftlich = 2.5e6;    // 2.500.000
let unendlich = Infinity;
let keinezahl = NaN;              // Not a Number

// Boolean
let istWahr = true;
let istFalsch = false;
let vergleich = 5 > 3;            // true

// Arrays (Listen)
let fruechte = ["Apfel", "Banane", "Orange"];
let zahlen = [1, 2, 3, 4, 5];
let gemischt = [1, "Text", true, null];

// Objekte
let person = {
    name: "Anna",
    alter: 25,
    istStudent: true,
    hobbys: ["Lesen", "Sport"]
};

// Variablen ausgeben
console.log("Hallo Welt!");
console.log("Name:", vorname);
console.log("Alter:", person.alter);

// Typeof-Operator
console.log(typeof zahl);         // "number"
console.log(typeof text);         // "string"
console.log(typeof wahrheit);     // "boolean"
console.log(typeof fruechte);     // "object" (Arrays sind Objekte)
console.log(typeof person);       // "object"

// Variablen ändern
let counter = 0;
counter = counter + 1;
counter += 1;                     // Kurzform
counter++;                        // Noch kürzer

// String-Methoden
let nachricht = "  Hallo JavaScript!  ";
console.log(nachricht.length);           // Länge
console.log(nachricht.toUpperCase());    // Großbuchstaben
console.log(nachricht.toLowerCase());    // Kleinbuchstaben
console.log(nachricht.trim());           // Leerzeichen entfernen
console.log(nachricht.includes("Java")); // Enthält Text?

// Zahlen-Methoden
let pi = 3.14159;
console.log(pi.toFixed(2));       // "3.14"
console.log(Math.round(pi));      // 3
console.log(Math.floor(pi));      // 3
console.log(Math.ceil(pi));       // 4
\`\`\`

## Aufgabe
Erstelle Variablen für deinen Namen (String), dein Alter (Number) und ob du Student bist (Boolean). Gib sie in der Konsole aus.`,
            exercise_type: 'code_example',
            expected_output: 'js_variables',
            points: 10
        },
        {
            course_id: 1, level_number: 12, title: 'Operatoren & Bedingungen', 
            description: 'Verwende Operatoren und if-else Bedingungen in JavaScript.',
            content: `# Operatoren & Bedingungen

JavaScript bietet viele Operatoren für Berechnungen und Vergleiche:

\`\`\`javascript
// Arithmetische Operatoren
let a = 10;
let b = 3;

console.log(a + b);    // 13 (Addition)
console.log(a - b);    // 7  (Subtraktion)
console.log(a * b);    // 30 (Multiplikation)
console.log(a / b);    // 3.333... (Division)
console.log(a % b);    // 1  (Modulo - Rest)
console.log(a ** b);   // 1000 (Potenz)

// Zuweisungsoperatoren
let x = 5;
x += 3;    // x = x + 3 → 8
x -= 2;    // x = x - 2 → 6
x *= 2;    // x = x * 2 → 12
x /= 3;    // x = x / 3 → 4
x++;       // x = x + 1 → 5
x--;       // x = x - 1 → 4

// Vergleichsoperatoren
let alter = 18;
console.log(alter == 18);   // true  (gleich, mit Typkonvertierung)
console.log(alter === 18);  // true  (strikt gleich)
console.log(alter != 20);   // true  (ungleich)
console.log(alter !== "18"); // true  (strikt ungleich)
console.log(alter > 16);    // true  (größer)
console.log(alter < 21);    // true  (kleiner)
console.log(alter >= 18);   // true  (größer gleich)
console.log(alter <= 18);   // true  (kleiner gleich)

// Logische Operatoren
let istStudent = true;
let hatAusweis = false;

console.log(istStudent && hatAusweis);  // false (UND)
console.log(istStudent || hatAusweis);  // true  (ODER)
console.log(!istStudent);               // false (NICHT)

// If-else Bedingungen
if (alter >= 18) {
    console.log("Du bist volljährig!");
} else {
    console.log("Du bist noch minderjährig.");
}

// Mehrere Bedingungen
let note = 85;

if (note >= 90) {
    console.log("Sehr gut!");
} else if (note >= 80) {
    console.log("Gut!");
} else if (note >= 70) {
    console.log("Befriedigend");
} else if (note >= 60) {
    console.log("Ausreichend");
} else {
    console.log("Ungenügend");
}

// Ternärer Operator (Kurzform)
let status = alter >= 18 ? "volljährig" : "minderjährig";
console.log("Status:", status);

// Switch-Statement
let tag = "Montag";

switch (tag) {
    case "Montag":
        console.log("Wochenstart!");
        break;
    case "Freitag":
        console.log("Fast Wochenende!");
        break;
    case "Samstag":
    case "Sonntag":
        console.log("Wochenende!");
        break;
    default:
        console.log("Normaler Wochentag");
}

// Truthy und Falsy Werte
// Falsy: false, 0, "", null, undefined, NaN
// Alles andere ist truthy

if ("") {
    console.log("Wird nicht ausgeführt");
}

if ("Hallo") {
    console.log("Wird ausgeführt");
}

// Praktische Beispiele
let benutzername = prompt("Wie heißt du?");

if (benutzername) {
    console.log("Hallo " + benutzername + "!");
} else {
    console.log("Du hast keinen Namen eingegeben.");
}

// Altersgruppe bestimmen
let benutzerAlter = 25;
let gruppe;

if (benutzerAlter < 13) {
    gruppe = "Kind";
} else if (benutzerAlter < 18) {
    gruppe = "Jugendlicher";
} else if (benutzerAlter < 65) {
    gruppe = "Erwachsener";
} else {
    gruppe = "Senior";
}

console.log("Du bist ein " + gruppe);
\`\`\`

## Aufgabe
Erstelle eine if-else Bedingung, die prüft, ob eine Zahl positiv, negativ oder null ist.`,
            exercise_type: 'code_example',
            expected_output: 'js_conditions',
            points: 10
        },
        {
            course_id: 1, level_number: 13, title: 'Schleifen (for, while)', 
            description: 'Lerne verschiedene Schleifentypen in JavaScript.',
            content: `# Schleifen (for, while)

Schleifen wiederholen Code automatisch:

\`\`\`javascript
// For-Schleife (klassisch)
console.log("Zahlen von 1 bis 5:");
for (let i = 1; i <= 5; i++) {
    console.log(i);
}

// Rückwärts zählen
console.log("Countdown:");
for (let i = 5; i >= 1; i--) {
    console.log(i);
}
console.log("Start!");

// While-Schleife
let counter = 0;
while (counter < 3) {
    console.log("Counter:", counter);
    counter++;
}

// Do-While-Schleife (wird mindestens einmal ausgeführt)
let zahl = 0;
do {
    console.log("Zahl:", zahl);
    zahl++;
} while (zahl < 3);

// Arrays durchlaufen
let fruechte = ["Apfel", "Banane", "Orange", "Erdbeere"];

// Mit klassischer for-Schleife
console.log("Früchte (klassisch):");
for (let i = 0; i < fruechte.length; i++) {
    console.log(i + ": " + fruechte[i]);
}

// Mit for...of (moderne Variante)
console.log("Früchte (modern):");
for (let frucht of fruechte) {
    console.log(frucht);
}

// Mit for...in (für Indizes)
console.log("Früchte mit Index:");
for (let index in fruechte) {
    console.log(index + ": " + fruechte[index]);
}

// Objekte durchlaufen
let person = {
    name: "Max",
    alter: 25,
    stadt: "Berlin",
    beruf: "Entwickler"
};

console.log("Person-Eigenschaften:");
for (let eigenschaft in person) {
    console.log(eigenschaft + ": " + person[eigenschaft]);
}

// Break und Continue
console.log("Zahlen mit break/continue:");
for (let i = 1; i <= 10; i++) {
    if (i === 5) {
        continue; // Überspringt 5
    }
    if (i === 8) {
        break; // Stoppt bei 8
    }
    console.log(i);
}

// Verschachtelte Schleifen
console.log("Multiplikationstabelle:");
for (let i = 1; i <= 3; i++) {
    for (let j = 1; j <= 3; j++) {
        console.log(i + " × " + j + " = " + (i * j));
    }
}

// Praktische Beispiele

// 1. Summe berechnen
let zahlen = [1, 2, 3, 4, 5];
let summe = 0;

for (let zahl of zahlen) {
    summe += zahl;
}
console.log("Summe:", summe);

// 2. Gerade Zahlen finden
console.log("Gerade Zahlen von 1-10:");
for (let i = 1; i <= 10; i++) {
    if (i % 2 === 0) {
        console.log(i);
    }
}

// 3. String umkehren
let text = "Hallo";
let umgekehrt = "";

for (let i = text.length - 1; i >= 0; i--) {
    umgekehrt += text[i];
}
console.log("Umgekehrt:", umgekehrt);

// 4. Array filtern
let alleZahlen = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
let geradeZahlen = [];

for (let zahl of alleZahlen) {
    if (zahl % 2 === 0) {
        geradeZahlen.push(zahl);
    }
}
console.log("Gerade Zahlen:", geradeZahlen);

// 5. Fakultät berechnen
function fakultaet(n) {
    let ergebnis = 1;
    for (let i = 1; i <= n; i++) {
        ergebnis *= i;
    }
    return ergebnis;
}

console.log("5! =", fakultaet(5)); // 120

// 6. Fibonacci-Folge
function fibonacci(n) {
    let folge = [0, 1];
    
    for (let i = 2; i < n; i++) {
        folge[i] = folge[i-1] + folge[i-2];
    }
    
    return folge;
}

console.log("Fibonacci (10):", fibonacci(10));

// While-Schleife für Benutzereingabe
let eingabe;
while (eingabe !== "stop") {
    eingabe = prompt("Gib 'stop' ein zum Beenden:");
    if (eingabe !== "stop") {
        console.log("Du hast eingegeben:", eingabe);
    }
}
\`\`\`

## Aufgabe
Erstelle eine for-Schleife, die die Zahlen von 1 bis 10 ausgibt, aber nur die geraden Zahlen.`,
            exercise_type: 'code_example',
            expected_output: 'js_loops',
            points: 10
        },
        {
            course_id: 1, level_number: 14, title: 'Funktionen', 
            description: 'Erstelle wiederverwendbare JavaScript-Funktionen.',
            content: `# Funktionen

Funktionen machen deinen Code organisiert und wiederverwendbar:

\`\`\`javascript
// Einfache Funktion
function begruessung() {
    console.log("Hallo Welt!");
}

// Funktion aufrufen
begruessung();

// Funktion mit Parametern
function begruesseBenutzer(name) {
    console.log("Hallo " + name + "!");
}

begruesseBenutzer("Max");
begruesseBenutzer("Anna");

// Funktion mit Rückgabewert
function addieren(a, b) {
    return a + b;
}

let ergebnis = addieren(5, 3);
console.log("5 + 3 =", ergebnis);

// Funktion mit mehreren Parametern
function vorstellen(name, alter, stadt) {
    return "Ich bin " + name + ", " + alter + " Jahre alt und komme aus " + stadt + ".";
}

console.log(vorstellen("Lisa", 28, "München"));

// Default-Parameter (ES6)
function begruessung2(name = "Gast", zeit = "Tag") {
    return "Guten " + zeit + ", " + name + "!";
}

console.log(begruessung2());                    // "Guten Tag, Gast!"
console.log(begruessung2("Max"));               // "Guten Tag, Max!"
console.log(begruessung2("Anna", "Morgen"));    // "Guten Morgen, Anna!"

// Arrow Functions (ES6) - Moderne Syntax
const multiplizieren = (a, b) => {
    return a * b;
};

// Kurze Arrow Function (bei einem Ausdruck)
const quadrat = x => x * x;
const istGerade = n => n % 2 === 0;

console.log(multiplizieren(4, 5));  // 20
console.log(quadrat(6));            // 36
console.log(istGerade(7));          // false

// Funktionen als Variablen
const dividieren = function(a, b) {
    if (b === 0) {
        return "Division durch Null nicht möglich!";
    }
    return a / b;
};

console.log(dividieren(10, 2));     // 5
console.log(dividieren(10, 0));     // "Division durch Null nicht möglich!"

// Lokale vs. Globale Variablen
let globalVar = "Ich bin global";

function scopeTest() {
    let lokalVar = "Ich bin lokal";
    console.log(globalVar);  // Funktioniert
    console.log(lokalVar);   // Funktioniert
}

scopeTest();
console.log(globalVar);     // Funktioniert
// console.log(lokalVar);   // Fehler! Variable nicht verfügbar

// Rekursive Funktionen
function countdown(n) {
    if (n <= 0) {
        console.log("Fertig!");
        return;
    }
    console.log(n);
    countdown(n - 1);
}

countdown(5);

// Praktische Beispiele

// 1. Temperatur umrechnen
function celsiusToFahrenheit(celsius) {
    return (celsius * 9/5) + 32;
}

function fahrenheitToCelsius(fahrenheit) {
    return (fahrenheit - 32) * 5/9;
}

console.log("20°C =", celsiusToFahrenheit(20), "°F");
console.log("68°F =", fahrenheitToCelsius(68), "°C");

// 2. Array-Funktionen
function findeMaximum(zahlen) {
    let max = zahlen[0];
    for (let zahl of zahlen) {
        if (zahl > max) {
            max = zahl;
        }
    }
    return max;
}

function berechneSum(zahlen) {
    let summe = 0;
    for (let zahl of zahlen) {
        summe += zahl;
    }
    return summe;
}

function berechneDurchschnitt(zahlen) {
    return berechneSum(zahlen) / zahlen.length;
}

let testZahlen = [1, 5, 3, 9, 2, 7];
console.log("Maximum:", findeMaximum(testZahlen));
console.log("Summe:", berechneSum(testZahlen));
console.log("Durchschnitt:", berechneDurchschnitt(testZahlen));

// 3. String-Funktionen
function istPalindrom(text) {
    let bereinigt = text.toLowerCase().replace(/[^a-z]/g, '');
    let umgekehrt = bereinigt.split('').reverse().join('');
    return bereinigt === umgekehrt;
}

function zähleWörter(text) {
    return text.trim().split(/\\s+/).length;
}

console.log(istPalindrom("Anna"));           // true
console.log(istPalindrom("Hallo"));          // false
console.log(zähleWörter("Hallo schöne Welt")); // 3

// 4. Validierungsfunktionen
function istGültigeEmail(email) {
    return email.includes('@') && email.includes('.');
}

function istStarkesPasswort(passwort) {
    return passwort.length >= 8 && 
           /[A-Z]/.test(passwort) && 
           /[a-z]/.test(passwort) && 
           /[0-9]/.test(passwort);
}

console.log(istGültigeEmail("<EMAIL>"));  // true
console.log(istStarkesPasswort("Hallo123"));       // true

// 5. Callback-Funktionen
function verarbeiteArray(array, callback) {
    let ergebnis = [];
    for (let element of array) {
        ergebnis.push(callback(element));
    }
    return ergebnis;
}

let zahlen = [1, 2, 3, 4, 5];
let quadrate = verarbeiteArray(zahlen, x => x * x);
let verdoppelt = verarbeiteArray(zahlen, x => x * 2);

console.log("Quadrate:", quadrate);      // [1, 4, 9, 16, 25]
console.log("Verdoppelt:", verdoppelt);  // [2, 4, 6, 8, 10]
\`\`\`

## Aufgabe
Erstelle eine Funktion, die zwei Zahlen multipliziert und eine andere, die prüft, ob eine Zahl gerade ist.`,
            exercise_type: 'code_example',
            expected_output: 'js_functions',
            points: 10
        }
    ];
    
    // Insert JavaScript levels 11-14
    for (const level of jsLevels) {
        levelStmt.run(level.course_id, level.level_number, level.title, level.description, level.content, level.exercise_type, level.expected_output, level.points);
    }
    
    console.log('✅ JavaScript levels 11-14 created!');
    
    levelStmt.finalize();
    directDb.close();
    
    console.log('🎉 JavaScript levels 11-14 completed! Continue with 15-19...');
});
