const Database = require('./database/db');
const db = new Database();

const authTranslations = [
    // Login page
    ['auth.login_title', 'de', 'Willkommen zurück'],
    ['auth.login_title', 'en', 'Welcome back'],
    ['auth.email', 'de', 'E-Mail'],
    ['auth.email', 'en', 'Email'],
    ['auth.password_placeholder', 'de', 'Passwort'],
    ['auth.password_placeholder', 'en', 'Password'],
    ['auth.forgot_password', 'de', 'Passwort vergessen?'],
    ['auth.forgot_password', 'en', 'Forgot Password?'],
    ['auth.no_account_yet', 'de', 'Noch kein Konto?'],
    ['auth.no_account_yet', 'en', "Don't have an account yet?"],
    ['auth.register_now', 'de', 'Jetzt registrieren'],
    ['auth.register_now', 'en', 'Register now'],
    ['auth.back_to_home', 'de', 'Zurück zur Startseite'],
    ['auth.back_to_home', 'en', 'Back to Home'],
    
    // Register page
    ['auth.register_title', 'de', 'Bei CodeWave anmelden'],
    ['auth.register_title', 'en', 'Join CodeWave'],
    ['auth.confirm_password', 'de', 'Passwort bestätigen'],
    ['auth.confirm_password', 'en', 'Confirm Password'],
    ['auth.confirm_password_placeholder', 'de', 'Passwort wiederholen'],
    ['auth.confirm_password_placeholder', 'en', 'Repeat Password'],
    ['auth.already_have_account', 'de', 'Bereits ein Konto?'],
    ['auth.already_have_account', 'en', 'Already have an account?'],
    ['auth.login_now', 'de', 'Jetzt anmelden'],
    ['auth.login_now', 'en', 'Login now'],
    ['auth.password_mismatch', 'de', 'Die Passwörter stimmen nicht überein!'],
    ['auth.password_mismatch', 'en', 'Passwords do not match!'],
    
    // Forgot password page
    ['auth.forgot_password_title', 'de', 'Passwort vergessen?'],
    ['auth.forgot_password_title', 'en', 'Forgot Password?'],
    ['auth.forgot_password_description', 'de', 'Gib deine E-Mail-Adresse ein und wir senden dir einen Link zum Zurücksetzen deines Passworts.'],
    ['auth.forgot_password_description', 'en', "Enter your email address and we'll send you a link to reset your password."],
    ['auth.email_address', 'de', 'E-Mail-Adresse'],
    ['auth.email_address', 'en', 'Email Address'],
    ['auth.email_placeholder', 'de', '<EMAIL>'],
    ['auth.email_placeholder', 'en', '<EMAIL>'],
    ['auth.send_reset_link', 'de', 'Reset-Link senden'],
    ['auth.send_reset_link', 'en', 'Send Reset Link'],
    ['auth.back_to_login', 'de', 'Zurück zur Anmeldung'],
    ['auth.back_to_login', 'en', 'Back to Login'],
    ['auth.success', 'de', 'Erfolgreich!'],
    ['auth.success', 'en', 'Success!'],
    ['auth.to_login', 'de', 'Zur Anmeldung'],
    ['auth.to_login', 'en', 'To Login'],
    
    // Reset password page
    ['auth.create_new_password', 'de', 'Neues Passwort erstellen'],
    ['auth.create_new_password', 'en', 'Create New Password'],
    ['auth.password_requirements', 'de', 'Dein Passwort muss mindestens 6 Zeichen enthalten.'],
    ['auth.password_requirements', 'en', 'Your password must contain at least 6 characters.'],
    ['auth.new_password', 'de', 'Neues Passwort'],
    ['auth.new_password', 'en', 'New Password'],
    ['auth.new_password_placeholder', 'de', 'Mindestens 6 Zeichen'],
    ['auth.new_password_placeholder', 'en', 'At least 6 characters'],
    ['auth.minimum_characters', 'de', 'Mindestens 6 Zeichen'],
    ['auth.minimum_characters', 'en', 'Minimum 6 characters'],
    ['auth.password_strength', 'de', 'Passwort-Stärke:'],
    ['auth.password_strength', 'en', 'Password Strength:'],
    ['auth.strength_very_low', 'de', 'Sehr schwach'],
    ['auth.strength_very_low', 'en', 'Very Low'],
    ['auth.strength_low', 'de', 'Schwach'],
    ['auth.strength_low', 'en', 'Low'],
    ['auth.strength_medium', 'de', 'Mittel'],
    ['auth.strength_medium', 'en', 'Medium'],
    ['auth.strength_high', 'de', 'Hoch'],
    ['auth.strength_high', 'en', 'High'],
    ['auth.strength_very_high', 'de', 'Sehr hoch'],
    ['auth.strength_very_high', 'en', 'Very High'],
    ['auth.save_new_password', 'de', 'Neues Passwort speichern'],
    ['auth.save_new_password', 'en', 'Save New Password'],
    ['auth.security_tips', 'de', 'Sicherheitstipps'],
    ['auth.security_tips', 'en', 'Security Tips'],
    ['auth.tip_8_characters', 'de', 'Verwende mindestens 8 Zeichen'],
    ['auth.tip_8_characters', 'en', 'Use at least 8 characters'],
    ['auth.tip_mixed_case', 'de', 'Verwende Groß- und Kleinbuchstaben'],
    ['auth.tip_mixed_case', 'en', 'Use a mix of uppercase and lowercase letters'],
    ['auth.tip_numbers_symbols', 'de', 'Verwende Zahlen und Symbole'],
    ['auth.tip_numbers_symbols', 'en', 'Include numbers and symbols'],
    
    // Reset password result page
    ['auth.password_reset_success', 'de', 'Passwort erfolgreich zurückgesetzt! 🎉'],
    ['auth.password_reset_success', 'en', 'Password successfully reset! 🎉'],
    ['auth.password_reset_failed', 'de', 'Passwort-Reset fehlgeschlagen ❌'],
    ['auth.password_reset_failed', 'en', 'Password Reset Failed ❌'],
    ['auth.successfully_reset', 'de', 'Erfolgreich zurückgesetzt!'],
    ['auth.successfully_reset', 'en', 'Successfully reset!'],
    ['auth.password_reset_complete', 'de', 'Dein Passwort wurde erfolgreich zurückgesetzt.'],
    ['auth.password_reset_complete', 'en', 'Your password has been successfully reset.'],
    ['auth.error_resetting', 'de', 'Fehler beim Zurücksetzen'],
    ['auth.error_resetting', 'en', 'Error resetting'],
    ['auth.probable_causes', 'de', 'Mögliche Ursachen:'],
    ['auth.probable_causes', 'en', 'Probable Causes:'],
    ['auth.link_expired', 'de', 'Der Reset-Link ist abgelaufen (1 Stunde gültig)'],
    ['auth.link_expired', 'en', 'The reset link has expired (valid for 1 hour)'],
    ['auth.link_already_used', 'de', 'Der Link wurde bereits verwendet'],
    ['auth.link_already_used', 'en', 'The link has already been used'],
    ['auth.link_invalid', 'de', 'Der Link ist ungültig oder beschädigt'],
    ['auth.link_invalid', 'en', 'The link is invalid or corrupted'],
    ['auth.new_reset_link', 'de', 'Neuen Reset-Link anfordern'],
    ['auth.new_reset_link', 'en', 'New Reset Link'],
    ['auth.security_notice', 'de', 'Sicherheitshinweis'],
    ['auth.security_notice', 'en', 'Security Notice'],
    ['auth.sessions_ended', 'de', 'Aus Sicherheitsgründen werden alle aktiven Sessions nach einer Passwort-Änderung beendet. Falls du verdächtige Aktivitäten bemerkst, kontaktiere sofort unser Support-Team.'],
    ['auth.sessions_ended', 'en', 'For security reasons, all active sessions are terminated after a password change. If you notice suspicious activities, contact our support team immediately.'],
    
    // Email verification result page
    ['auth.email_verified', 'de', 'E-Mail bestätigt! 🎉'],
    ['auth.email_verified', 'en', 'Email verified! 🎉'],
    ['auth.verification_failed', 'de', 'Bestätigung fehlgeschlagen ❌'],
    ['auth.verification_failed', 'en', 'Verification failed ❌'],
    ['auth.successfully_verified', 'de', 'Erfolgreich bestätigt!'],
    ['auth.successfully_verified', 'en', 'Successfully verified!'],
    ['auth.can_now_login', 'de', 'Du kannst dich jetzt anmelden und mit dem Lernen beginnen.'],
    ['auth.can_now_login', 'en', 'You can now log in and start learning.'],
    ['auth.login_now_button', 'de', 'Jetzt anmelden'],
    ['auth.login_now_button', 'en', 'Login now'],
    ['auth.to_homepage', 'de', 'Zur Startseite'],
    ['auth.to_homepage', 'en', 'To Homepage'],
    ['auth.verification_error', 'de', 'Fehler bei der Bestätigung'],
    ['auth.verification_error', 'en', 'Verification error'],
    ['auth.possible_reasons', 'de', 'Mögliche Gründe:'],
    ['auth.possible_reasons', 'en', 'Possible reasons:'],
    ['auth.verification_expired', 'de', 'Der Bestätigungslink ist abgelaufen (24 Stunden Gültigkeit)'],
    ['auth.verification_expired', 'en', 'The verification link has expired (valid for 24 hours)'],
    ['auth.verification_used', 'de', 'Der Link wurde bereits verwendet'],
    ['auth.verification_used', 'en', 'The link has already been used'],
    ['auth.verification_invalid', 'de', 'Der Link ist ungültig oder beschädigt'],
    ['auth.verification_invalid', 'en', 'The link is invalid or corrupted'],
    ['auth.register_again', 'de', 'Erneut registrieren'],
    ['auth.register_again', 'en', 'Register again'],
    
    // Register success page
    ['auth.registration_successful', 'de', 'Registrierung erfolgreich! 🎉'],
    ['auth.registration_successful', 'en', 'Registration successful! 🎉'],
    ['auth.registration_thanks', 'de', 'Vielen Dank für deine Registrierung bei CodeWave!'],
    ['auth.registration_thanks', 'en', 'Thank you for registering with CodeWave!'],
    ['auth.verification_email_sent', 'de', 'Wir haben eine Bestätigungs-E-Mail gesendet.'],
    ['auth.verification_email_sent', 'en', 'We have sent a verification email.'],
    ['auth.important_notice', 'de', 'Wichtiger Hinweis'],
    ['auth.important_notice', 'en', 'Important Notice'],
    ['auth.must_verify_email', 'de', 'Du musst deine E-Mail-Adresse bestätigen, bevor du dich anmelden kannst.'],
    ['auth.must_verify_email', 'en', 'You must verify your email address before you can log in.'],
    ['auth.next_steps', 'de', 'Nächste Schritte:'],
    ['auth.next_steps', 'en', 'Next Steps:'],
    ['auth.step_check_email', 'de', 'Überprüfe dein E-Mail-Postfach (auch den Spam-Ordner)'],
    ['auth.step_check_email', 'en', 'Check your email inbox (including spam folder)'],
    ['auth.step_click_link', 'de', 'Klicke auf den Bestätigungslink in der E-Mail'],
    ['auth.step_click_link', 'en', 'Click on the verification link in the email'],
    ['auth.step_return_login', 'de', 'Kehre zur Anmeldeseite zurück'],
    ['auth.step_return_login', 'en', 'Return to the login page'],
    
    // Common help and support
    ['common.need_help', 'de', 'Brauchst du Hilfe? Kontaktiere unser Support-Team.'],
    ['common.need_help', 'en', 'Need help? Contact our support team.'],
    ['common.email', 'de', 'E-Mail'],
    ['common.email', 'en', 'Email'],
    ['common.faq', 'de', 'FAQ'],
    ['common.faq', 'en', 'FAQ'],
    ['common.home', 'de', 'Startseite'],
    ['common.home', 'en', 'Home'],
    ['common.login', 'de', 'Anmelden'],
    ['common.login', 'en', 'Login']
];

let completed = 0;
const total = authTranslations.length;

console.log(`Adding ${total} auth translation keys...`);

authTranslations.forEach(([key, lang, value]) => {
    db.db.run('INSERT OR REPLACE INTO translations (key, language, value) VALUES (?, ?, ?)', [key, lang, value], (err) => {
        if (err) {
            console.error('Error inserting translation:', key, lang, err);
        } else {
            console.log(`✅ Added: ${key} (${lang})`);
        }
        completed++;
        if (completed === total) {
            console.log(`\n🎉 Successfully added all ${total} auth translations!`);
            process.exit(0);
        }
    });
});
