-- Komplette Content-Updates für alle verbleibenden Level

-- HTML-CSS-JS Level 19-20
UPDATE levels SET content = '
            <h3>CSS Transitions</h3>
            <p>CSS Transitions ermöglichen sanfte Übergänge zwischen verschiedenen CSS-Zuständen.</p>
            
            <h4>Grundlegende Transitions:</h4>
            <pre><code>.button {
    background-color: blue;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    
    /* Transition definieren */
    transition: background-color 0.3s ease;
}

.button:hover {
    background-color: darkblue;
}

/* Mehrere Eigenschaften */
.card {
    transform: scale(1);
    opacity: 1;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.card:hover {
    transform: scale(1.05);
    opacity: 0.9;
}</code></pre>

            <h4>Transition-Eigenschaften:</h4>
            <pre><code>/* Alle Eigenschaften einzeln */
.element {
    transition-property: width, height, background-color;
    transition-duration: 0.5s, 0.3s, 0.2s;
    transition-timing-function: ease, ease-in, ease-out;
    transition-delay: 0s, 0.1s, 0.2s;
}

/* Kurzschreibweise */
.element {
    transition: width 0.5s ease 0s,
                height 0.3s ease-in 0.1s,
                background-color 0.2s ease-out 0.2s;
}</code></pre>
        ' WHERE course_id = 1 AND level_number = 19;

UPDATE levels SET content = '
            <h3>JavaScript Module</h3>
            <p>Module ermöglichen es dir, Code in separate Dateien zu organisieren und zwischen ihnen zu importieren/exportieren.</p>
            
            <h4>Export (math.js):</h4>
            <pre><code>// Named Exports
export function add(a, b) {
    return a + b;
}

export function multiply(a, b) {
    return a * b;
}

export const PI = 3.14159;

// Default Export
export default function subtract(a, b) {
    return a - b;
}

// Oder alles auf einmal
function divide(a, b) {
    return a / b;
}

const E = 2.71828;

export { divide, E };</code></pre>

            <h4>Import (main.js):</h4>
            <pre><code>// Named Imports
import { add, multiply, PI } from "./math.js";

// Default Import
import subtract from "./math.js";

// Alles importieren
import * as math from "./math.js";

// Verwendung
console.log(add(5, 3));           // 8
console.log(subtract(10, 4));     // 6
console.log(math.multiply(3, 4)); // 12
console.log(PI);                  // 3.14159

// Import mit Umbenennung
import { add as addition, PI as pi } from "./math.js";</code></pre>

            <h4>Dynamic Imports:</h4>
            <pre><code>// Dynamisches Laden zur Laufzeit
async function loadMath() {
    const mathModule = await import("./math.js");
    
    console.log(mathModule.add(2, 3));
    console.log(mathModule.default(10, 5)); // Default export
}

// Oder mit .then()
import("./math.js")
    .then(module => {
        console.log(module.add(1, 2));
    })
    .catch(error => {
        console.error("Fehler beim Laden:", error);
    });</code></pre>
        ' WHERE course_id = 1 AND level_number = 20;

-- JavaScript Level 9-20
UPDATE levels SET content = '
            <h3>Promises</h3>
            <p>Promises sind ein Weg, um mit asynchronem Code in JavaScript umzugehen und Callback-Hell zu vermeiden.</p>
            
            <h4>Promise erstellen:</h4>
            <pre><code>// Einfaches Promise
const myPromise = new Promise((resolve, reject) => {
    const success = true;
    
    setTimeout(() => {
        if (success) {
            resolve("Operation erfolgreich!");
        } else {
            reject("Fehler aufgetreten!");
        }
    }, 2000);
});

// Promise verwenden
myPromise
    .then(result => {
        console.log(result); // "Operation erfolgreich!"
    })
    .catch(error => {
        console.error(error);
    });</code></pre>

            <h4>Promise-Ketten:</h4>
            <pre><code>function fetchUser(id) {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve({ id: id, name: "Max" });
        }, 1000);
    });
}

function fetchUserPosts(userId) {
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve(["Post 1", "Post 2", "Post 3"]);
        }, 1000);
    });
}

// Promise-Kette
fetchUser(1)
    .then(user => {
        console.log("User:", user);
        return fetchUserPosts(user.id);
    })
    .then(posts => {
        console.log("Posts:", posts);
    })
    .catch(error => {
        console.error("Fehler:", error);
    });</code></pre>

            <h4>Promise.all und Promise.race:</h4>
            <pre><code>const promise1 = Promise.resolve(3);
const promise2 = new Promise(resolve => setTimeout(() => resolve("foo"), 1000));
const promise3 = Promise.resolve(42);

// Alle Promises warten
Promise.all([promise1, promise2, promise3])
    .then(values => {
        console.log(values); // [3, "foo", 42]
    });

// Erstes Promise das fertig wird
Promise.race([promise1, promise2, promise3])
    .then(value => {
        console.log(value); // 3 (das schnellste)
    });</code></pre>
        ' WHERE course_id = 2 AND level_number = 9;

-- Go Level 1-5
UPDATE levels SET content = '
            <h3>Go Grundlagen</h3>
            <p>Go ist eine moderne, kompilierte Programmiersprache von Google, die für Einfachheit und Effizienz entwickelt wurde.</p>
            
            <h4>Erstes Go-Programm:</h4>
            <pre><code>package main

import "fmt"

func main() {
    fmt.Println("Hallo Welt!")
    fmt.Println("Willkommen bei Go!")
}</code></pre>

            <h4>Variablen und Datentypen:</h4>
            <pre><code>package main

import "fmt"

func main() {
    // Variablen deklarieren
    var name string = "Max"
    var age int = 25
    var height float64 = 1.80
    var isStudent bool = true
    
    // Kurze Deklaration
    city := "Berlin"
    score := 95.5
    
    // Mehrere Variablen
    var (
        firstName = "Anna"
        lastName  = "Müller"
        year      = 2024
    )
    
    fmt.Printf("Name: %s, Alter: %d\n", name, age)
    fmt.Printf("Größe: %.2f, Student: %t\n", height, isStudent)
    fmt.Printf("Stadt: %s, Punkte: %.1f\n", city, score)
}</code></pre>

            <h4>Konstanten und iota:</h4>
            <pre><code>package main

import "fmt"

// Konstanten
const PI = 3.14159
const (
    StatusOK    = 200
    StatusError = 500
)

// iota für aufeinanderfolgende Werte
const (
    Monday = iota    // 0
    Tuesday          // 1
    Wednesday        // 2
    Thursday         // 3
    Friday           // 4
)

func main() {
    fmt.Printf("PI: %.5f\n", PI)
    fmt.Printf("Montag: %d, Dienstag: %d\n", Monday, Tuesday)
}</code></pre>
        ' WHERE course_id = 5 AND level_number = 1;

-- Java Level 1-3
UPDATE levels SET content = '
            <h3>Java Grundlagen</h3>
            <p>Java ist eine objektorientierte, plattformunabhängige Programmiersprache, die in vielen Unternehmen eingesetzt wird.</p>

            <h4>Erstes Java-Programm:</h4>
            <pre><code>public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hallo Welt!");
        System.out.println("Willkommen bei Java!");
    }
}</code></pre>

            <h4>Variablen und Datentypen:</h4>
            <pre><code>public class Variables {
    public static void main(String[] args) {
        // Primitive Datentypen
        int age = 25;
        double height = 1.80;
        boolean isStudent = true;
        char grade = ''A'';

        // String (Referenztyp)
        String name = "Max Mustermann";

        // Konstanten
        final double PI = 3.14159;
        final int MAX_SCORE = 100;

        // Ausgabe
        System.out.println("Name: " + name);
        System.out.println("Alter: " + age);
        System.out.printf("Größe: %.2f m%n", height);
        System.out.println("Student: " + isStudent);
        System.out.println("Note: " + grade);
    }
}</code></pre>

            <h4>Operatoren:</h4>
            <pre><code>public class Operators {
    public static void main(String[] args) {
        int a = 10, b = 3;

        // Arithmetische Operatoren
        System.out.println("Addition: " + (a + b));      // 13
        System.out.println("Subtraktion: " + (a - b));   // 7
        System.out.println("Multiplikation: " + (a * b)); // 30
        System.out.println("Division: " + (a / b));       // 3
        System.out.println("Modulo: " + (a % b));         // 1

        // Vergleichsoperatoren
        System.out.println("a > b: " + (a > b));          // true
        System.out.println("a == b: " + (a == b));        // false

        // Logische Operatoren
        boolean x = true, y = false;
        System.out.println("x && y: " + (x && y));        // false
        System.out.println("x || y: " + (x || y));        // true
        System.out.println("!x: " + (!x));                // false
    }
}</code></pre>
        ' WHERE course_id = 6 AND level_number = 1;
