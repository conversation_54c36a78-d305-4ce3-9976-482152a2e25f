-- Update existing achievements with language classifications

-- General achievements (1-6)
UPDATE achievements SET language = 'general' WHERE id IN (1, 2, 3, 4);

-- Course completion achievements
UPDATE achievements SET language = 'html-css-js' WHERE id = 5;
UPDATE achievements SET language = 'javascript' WHERE id = 6;

-- HTML/CSS/JS specific (7-16)
UPDATE achievements SET language = 'html-css-js' WHERE id IN (7, 8, 9, 10, 11, 12, 13, 14, 15, 16);

-- JavaScript specific (17-26)
UPDATE achievements SET language = 'javascript' WHERE id IN (17, 18, 19, 20, 21, 22, 23, 24, 25, 26);

-- PHP specific (27-36)
UPDATE achievements SET language = 'php' WHERE id IN (27, 28, 29, 30, 31, 32, 33, 34, 35, 36);

-- Python specific (37-46)
UPDATE achievements SET language = 'python' WHERE id IN (37, 38, 39, 40, 41, 42, 43, 44, 45, 46);

-- Go specific (47-52)
UPDATE achievements SET language = 'go' WHERE id IN (47, 48, 49, 50, 51, 52);

-- Java specific (53-56)
UPDATE achievements SET language = 'java' WHERE id IN (53, 54, 55, 56);
