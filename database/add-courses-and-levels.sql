-- Neue Kurse hinzufügen
INSERT OR IGNORE INTO courses (id, name, description, slug, total_levels) VALUES
(3, 'PHP Webentwicklung', 'Lerne PHP für moderne Webentwicklung mit MySQL, Laravel und APIs', 'php', 20),
(4, 'Python Programmierung', 'Von Grundlagen bis Data Science und Machine Learning mit Python', 'python', 20),
(5, 'Go Programming', 'Moderne Systemprogrammierung mit Go - Concurrency, Microservices und Cloud', 'go', 20),
(6, 'Java Enterprise', 'Objektorientierte Programmierung und Enterprise-Entwicklung mit Java', 'java', 20);

-- HTML-CSS-JS Level 11-20 hinzufügen
INSERT OR IGNORE INTO levels (course_id, level_number, title, description, content, exercise_type, expected_output) VALUES
(1, 11, 'CSS Grid Layout', 'Erstelle komplexe Layouts mit CSS Grid', 'Lerne CSS Grid für moderne Layouts:\n\n```css\n.container {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 20px;\n}\n```\n\nErstelle ein Grid-Layout mit 3 Spalten.', 'code_example', 'grid-template-columns'),
(1, 12, 'JavaScript Events', 'Interaktive Webseiten mit Event Handling', 'Event Listener in JavaScript:\n\n```javascript\ndocument.getElementById("button").addEventListener("click", function() {\n  alert("Button geklickt!");\n});\n```\n\nFüge einen Click-Event zu einem Button hinzu.', 'addEventListener', 'medium'),
(1, 13, 'CSS Animations', 'Animationen mit CSS erstellen', 'CSS Animationen:\n\n```css\n@keyframes slideIn {\n  from { transform: translateX(-100%); }\n  to { transform: translateX(0); }\n}\n\n.animated {\n  animation: slideIn 0.5s ease-in-out;\n}\n```', 'keyframes', 'medium'),
(1, 14, 'JavaScript DOM Manipulation', 'Dynamische Inhalte mit JavaScript', 'DOM Manipulation:\n\n```javascript\nconst element = document.createElement("div");\nelement.textContent = "Neuer Inhalt";\ndocument.body.appendChild(element);\n```', 'createElement', 'medium'),
(1, 15, 'Responsive Design', 'Mobile-first Design mit Media Queries', 'Media Queries für responsive Design:\n\n```css\n@media (max-width: 768px) {\n  .container {\n    flex-direction: column;\n  }\n}\n```', 'media', 'medium'),
(1, 16, 'JavaScript Fetch API', 'Daten von APIs laden', 'Fetch API verwenden:\n\n```javascript\nfetch("https://api.example.com/data")\n  .then(response => response.json())\n  .then(data => console.log(data));\n```', 'fetch', 'hard'),
(1, 17, 'CSS Flexbox Advanced', 'Erweiterte Flexbox Techniken', 'Erweiterte Flexbox:\n\n```css\n.flex-container {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  flex-wrap: wrap;\n}\n```', 'justify-content', 'hard'),
(1, 18, 'JavaScript Local Storage', 'Daten im Browser speichern', 'Local Storage verwenden:\n\n```javascript\nlocalStorage.setItem("username", "Max");\nconst username = localStorage.getItem("username");\n```', 'localStorage', 'hard'),
(1, 19, 'CSS Custom Properties', 'CSS Variablen verwenden', 'CSS Custom Properties:\n\n```css\n:root {\n  --primary-color: #3498db;\n  --font-size: 16px;\n}\n\n.element {\n  color: var(--primary-color);\n}\n```', 'var(--', 'hard'),
(1, 20, 'JavaScript Modules', 'Code in Module organisieren', 'ES6 Module:\n\n```javascript\n// math.js\nexport function add(a, b) {\n  return a + b;\n}\n\n// main.js\nimport { add } from "./math.js";\n```', 'export', 'hard');

-- JavaScript Level 6-20 hinzufügen
INSERT OR IGNORE INTO levels (course_id, level_number, title, description, content, expected_output, difficulty) VALUES
(2, 6, 'Arrow Functions', 'Moderne Funktionssyntax mit Arrow Functions', 'Arrow Functions:\n\n```javascript\nconst add = (a, b) => a + b;\nconst numbers = [1, 2, 3].map(n => n * 2);\n```', '=>', 'easy'),
(2, 7, 'Destructuring', 'Objekte und Arrays destructuring', 'Destructuring Assignment:\n\n```javascript\nconst {name, age} = person;\nconst [first, second] = array;\n```', 'const {', 'easy'),
(2, 8, 'Template Literals', 'String-Interpolation mit Template Literals', 'Template Literals:\n\n```javascript\nconst name = "Max";\nconst message = `Hallo ${name}!`;\n```', '`', 'easy'),
(2, 9, 'Spread Operator', 'Arrays und Objekte mit Spread Operator', 'Spread Operator:\n\n```javascript\nconst newArray = [...oldArray, newItem];\nconst newObject = {...oldObject, newProperty: value};\n```', '...', 'easy'),
(2, 10, 'Promises', 'Asynchrone Programmierung mit Promises', 'Promises verwenden:\n\n```javascript\nconst promise = new Promise((resolve, reject) => {\n  setTimeout(() => resolve("Fertig!"), 1000);\n});\n```', 'Promise', 'medium'),
(2, 11, 'Async/Await', 'Moderne asynchrone Programmierung', 'Async/Await:\n\n```javascript\nasync function fetchData() {\n  const response = await fetch("/api/data");\n  const data = await response.json();\n  return data;\n}\n```', 'async', 'medium'),
(2, 12, 'Classes', 'Objektorientierte Programmierung mit Classes', 'ES6 Classes:\n\n```javascript\nclass Person {\n  constructor(name) {\n    this.name = name;\n  }\n  \n  greet() {\n    return `Hallo, ich bin ${this.name}`;\n  }\n}\n```', 'class', 'medium'),
(2, 13, 'Modules Import/Export', 'Module System in JavaScript', 'Module System:\n\n```javascript\n// utils.js\nexport const helper = () => {};\nexport default class MyClass {}\n\n// main.js\nimport MyClass, { helper } from "./utils.js";\n```', 'import', 'medium'),
(2, 14, 'Error Handling', 'Fehlerbehandlung mit try/catch', 'Error Handling:\n\n```javascript\ntry {\n  const result = riskyOperation();\n} catch (error) {\n  console.error("Fehler:", error.message);\n} finally {\n  cleanup();\n}\n```', 'try', 'medium'),
(2, 15, 'Regular Expressions', 'Pattern Matching mit RegEx', 'Regular Expressions:\n\n```javascript\nconst emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\nconst isValid = emailRegex.test(email);\n```', 'RegExp', 'hard'),
(2, 16, 'Closures', 'Closures und Scope verstehen', 'Closures:\n\n```javascript\nfunction createCounter() {\n  let count = 0;\n  return function() {\n    return ++count;\n  };\n}\n```', 'closure', 'hard'),
(2, 17, 'Prototypes', 'Prototype-basierte Vererbung', 'Prototypes:\n\n```javascript\nfunction Person(name) {\n  this.name = name;\n}\n\nPerson.prototype.greet = function() {\n  return `Hallo ${this.name}`;\n};\n```', 'prototype', 'hard'),
(2, 18, 'Generators', 'Generator Functions verwenden', 'Generators:\n\n```javascript\nfunction* numberGenerator() {\n  let i = 0;\n  while (true) {\n    yield i++;\n  }\n}\n```', 'function*', 'hard'),
(2, 19, 'Proxy Objects', 'Meta-Programmierung mit Proxy', 'Proxy Objects:\n\n```javascript\nconst proxy = new Proxy(target, {\n  get(obj, prop) {\n    return prop in obj ? obj[prop] : "Default";\n  }\n});\n```', 'Proxy', 'hard'),
(2, 20, 'Web Workers', 'Multi-Threading im Browser', 'Web Workers:\n\n```javascript\nconst worker = new Worker("worker.js");\nworker.postMessage({data: "Hello"});\nworker.onmessage = (e) => console.log(e.data);\n```', 'Worker', 'hard');

-- PHP Level 1-20
INSERT OR IGNORE INTO levels (course_id, level_number, title, description, content, expected_output, difficulty) VALUES
(3, 1, 'PHP Grundlagen', 'Erste Schritte mit PHP', 'PHP Basics:\n\n```php\n<?php\necho "Hallo Welt!";\n$name = "Max";\necho "Hallo " . $name;\n?>\n```', 'echo', 'easy'),
(3, 2, 'Variablen und Datentypen', 'PHP Variablen verstehen', 'PHP Variablen:\n\n```php\n<?php\n$string = "Text";\n$number = 42;\n$boolean = true;\n$array = [1, 2, 3];\n?>\n```', '$', 'easy'),
(3, 3, 'Arrays in PHP', 'Mit Arrays arbeiten', 'PHP Arrays:\n\n```php\n<?php\n$fruits = ["Apfel", "Banane", "Orange"];\n$person = ["name" => "Max", "age" => 25];\necho $fruits[0];\n?>\n```', 'array', 'easy'),
(3, 4, 'Kontrollstrukturen', 'If/Else und Schleifen', 'Kontrollstrukturen:\n\n```php\n<?php\nif ($age >= 18) {\n    echo "Erwachsen";\n}\n\nfor ($i = 0; $i < 5; $i++) {\n    echo $i;\n}\n?>\n```', 'if', 'easy'),
(3, 5, 'Funktionen', 'Eigene Funktionen erstellen', 'PHP Funktionen:\n\n```php\n<?php\nfunction greet($name) {\n    return "Hallo " . $name;\n}\n\necho greet("Max");\n?>\n```', 'function', 'easy'),
(3, 6, 'Superglobals', '$_GET, $_POST und $_SESSION', 'Superglobals:\n\n```php\n<?php\n$name = $_GET["name"];\n$_SESSION["user"] = "Max";\necho $_POST["message"];\n?>\n```', '$_GET', 'medium'),
(3, 7, 'Formulare verarbeiten', 'HTML Formulare mit PHP', 'Formular-Verarbeitung:\n\n```php\n<?php\nif ($_POST["submit"]) {\n    $name = $_POST["name"];\n    echo "Hallo " . $name;\n}\n?>\n```', '$_POST', 'medium'),
(3, 8, 'Datei-Operationen', 'Dateien lesen und schreiben', 'Datei-Operationen:\n\n```php\n<?php\n$content = file_get_contents("data.txt");\nfile_put_contents("output.txt", "Neuer Inhalt");\n?>\n```', 'file_get_contents', 'medium'),
(3, 9, 'MySQL Verbindung', 'Datenbank-Verbindung mit PDO', 'MySQL mit PDO:\n\n```php\n<?php\n$pdo = new PDO("mysql:host=localhost;dbname=test", $user, $pass);\n$stmt = $pdo->query("SELECT * FROM users");\n?>\n```', 'PDO', 'medium'),
(3, 10, 'SQL Queries', 'Daten abfragen und einfügen', 'SQL Queries:\n\n```php\n<?php\n$stmt = $pdo->prepare("INSERT INTO users (name) VALUES (?)");\n$stmt->execute([$name]);\n?>\n```', 'prepare', 'medium'),
(3, 11, 'Sessions', 'Session-Management', 'PHP Sessions:\n\n```php\n<?php\nsession_start();\n$_SESSION["username"] = "Max";\necho $_SESSION["username"];\n?>\n```', 'session_start', 'medium'),
(3, 12, 'Cookies', 'Cookies setzen und lesen', 'PHP Cookies:\n\n```php\n<?php\nsetcookie("username", "Max", time() + 3600);\necho $_COOKIE["username"];\n?>\n```', 'setcookie', 'medium'),
(3, 13, 'Klassen und Objekte', 'OOP in PHP', 'PHP OOP:\n\n```php\n<?php\nclass Person {\n    public $name;\n    \n    public function __construct($name) {\n        $this->name = $name;\n    }\n}\n?>\n```', 'class', 'hard'),
(3, 14, 'Vererbung', 'Klassen-Vererbung', 'PHP Vererbung:\n\n```php\n<?php\nclass Student extends Person {\n    public function study() {\n        return $this->name . " studiert";\n    }\n}\n?>\n```', 'extends', 'hard'),
(3, 15, 'Namespaces', 'Code-Organisation mit Namespaces', 'PHP Namespaces:\n\n```php\n<?php\nnamespace App\\Models;\n\nclass User {\n    // ...\n}\n\nuse App\\Models\\User;\n?>\n```', 'namespace', 'hard'),
(3, 16, 'Composer', 'Dependency Management', 'Composer verwenden:\n\n```php\n<?php\nrequire_once "vendor/autoload.php";\n\nuse Vendor\\Package\\Class;\n?>\n```', 'composer', 'hard'),
(3, 17, 'Laravel Basics', 'Laravel Framework Grundlagen', 'Laravel:\n\n```php\n<?php\nRoute::get("/", function() {\n    return view("welcome");\n});\n\nclass UserController extends Controller {\n    // ...\n}\n?>\n```', 'Route::', 'hard'),
(3, 18, 'API Development', 'REST APIs mit PHP', 'PHP API:\n\n```php\n<?php\nheader("Content-Type: application/json");\n\n$data = ["status" => "success", "data" => $result];\necho json_encode($data);\n?>\n```', 'json_encode', 'hard'),
(3, 19, 'Security', 'Sicherheit in PHP', 'PHP Security:\n\n```php\n<?php\n$password = password_hash($input, PASSWORD_DEFAULT);\n$clean = filter_var($input, FILTER_SANITIZE_STRING);\n?>\n```', 'password_hash', 'hard'),
(3, 20, 'Testing', 'Unit Tests mit PHPUnit', 'PHPUnit Testing:\n\n```php\n<?php\nclass UserTest extends PHPUnit\\Framework\\TestCase {\n    public function testUserCreation() {\n        $this->assertTrue(true);\n    }\n}\n?>\n```', 'PHPUnit', 'hard');
