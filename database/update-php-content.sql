-- PHP Level 1-10 Content Updates
UPDATE levels SET content = '
            <h3>PHP Grundlagen</h3>
            <p>PHP ist eine serverseitige Programmiersprache, die speziell für die Webentwicklung entwickelt wurde.</p>
            
            <h4>PHP-Tags und erste Ausgabe:</h4>
            <pre><code>&lt;?php
// PHP-Code beginnt mit &lt;?php
echo "Hallo Welt!";
print "Das ist PHP!";

// Variablen beginnen mit $
$name = "Max";
$age = 25;

echo "Ich bin " . $name . " und " . $age . " Jahre alt.";
?&gt;</code></pre>

            <h4>Variablen und Datentypen:</h4>
            <pre><code>&lt;?php
// String
$text = "Das ist ein Text";

// Integer
$number = 42;

// Float
$price = 19.99;

// Boolean
$isActive = true;

// Array
$colors = array("rot", "grün", "blau");
$fruits = ["Apfel", "Banane", "Orange"]; // Moderne Syntax

// Ausgabe
var_dump($colors); // Zeigt Typ und Wert
print_r($fruits);  // Lesbare Array-Ausgabe
?&gt;</code></pre>

            <h4>String-Konkatenation:</h4>
            <pre><code>&lt;?php
$firstName = "Max";
$lastName = "Mustermann";

// Mit Punkt-Operator
$fullName = $firstName . " " . $lastName;

// Mit doppelten Anführungszeichen (Variable wird interpretiert)
$greeting = "Hallo $firstName!";

// Mit geschweiften Klammern für Klarheit
$message = "Willkommen {$firstName} {$lastName}!";

echo $fullName;
?&gt;</code></pre>
        ' WHERE course_id = 3 AND level_number = 1;

UPDATE levels SET content = '
            <h3>PHP Kontrollstrukturen</h3>
            <p>Kontrollstrukturen steuern den Programmablauf mit Bedingungen und Schleifen.</p>
            
            <h4>If-Else Anweisungen:</h4>
            <pre><code>&lt;?php
$age = 18;

if ($age >= 18) {
    echo "Du bist volljährig.";
} elseif ($age >= 16) {
    echo "Du darfst Auto fahren.";
} else {
    echo "Du bist noch minderjährig.";
}

// Ternärer Operator
$status = ($age >= 18) ? "Erwachsen" : "Minderjährig";
echo $status;
?&gt;</code></pre>

            <h4>Switch-Anweisung:</h4>
            <pre><code>&lt;?php
$day = "Montag";

switch ($day) {
    case "Montag":
        echo "Start der Woche!";
        break;
    case "Freitag":
        echo "Fast Wochenende!";
        break;
    case "Samstag":
    case "Sonntag":
        echo "Wochenende!";
        break;
    default:
        echo "Ein normaler Wochentag.";
}
?&gt;</code></pre>

            <h4>Schleifen:</h4>
            <pre><code>&lt;?php
// For-Schleife
for ($i = 1; $i <= 5; $i++) {
    echo "Zahl: $i&lt;br&gt;";
}

// While-Schleife
$count = 0;
while ($count < 3) {
    echo "Count: $count&lt;br&gt;";
    $count++;
}

// Foreach für Arrays
$fruits = ["Apfel", "Banane", "Orange"];
foreach ($fruits as $fruit) {
    echo "Frucht: $fruit&lt;br&gt;";
}

// Foreach mit Index
foreach ($fruits as $index => $fruit) {
    echo "$index: $fruit&lt;br&gt;";
}
?&gt;</code></pre>
        ' WHERE course_id = 3 AND level_number = 2;

UPDATE levels SET content = '
            <h3>PHP Funktionen</h3>
            <p>Funktionen ermöglichen es dir, Code zu organisieren und wiederzuverwenden.</p>
            
            <h4>Funktionen definieren und aufrufen:</h4>
            <pre><code>&lt;?php
// Einfache Funktion
function greet() {
    echo "Hallo Welt!";
}

// Funktion aufrufen
greet();

// Funktion mit Parametern
function greetUser($name, $age = 18) {
    echo "Hallo $name, du bist $age Jahre alt.";
}

greetUser("Max");        // Verwendet Standardwert für $age
greetUser("Anna", 25);   // Überschreibt Standardwert

// Funktion mit Rückgabewert
function add($a, $b) {
    return $a + $b;
}

$result = add(5, 3);
echo "Ergebnis: $result"; // Ergebnis: 8
?&gt;</code></pre>

            <h4>Variable Scope:</h4>
            <pre><code>&lt;?php
$globalVar = "Ich bin global";

function testScope() {
    $localVar = "Ich bin lokal";
    
    // Zugriff auf globale Variable
    global $globalVar;
    echo $globalVar;
    
    // Oder mit $GLOBALS
    echo $GLOBALS["globalVar"];
}

// Statische Variablen
function counter() {
    static $count = 0;
    $count++;
    echo "Aufruf Nummer: $count";
}

counter(); // Aufruf Nummer: 1
counter(); // Aufruf Nummer: 2
?&gt;</code></pre>

            <h4>Anonyme Funktionen (Closures):</h4>
            <pre><code>&lt;?php
// Anonyme Funktion
$multiply = function($a, $b) {
    return $a * $b;
};

echo $multiply(4, 5); // 20

// Closure mit use
$factor = 10;
$multiplyByFactor = function($number) use ($factor) {
    return $number * $factor;
};

echo $multiplyByFactor(5); // 50
?&gt;</code></pre>
        ' WHERE course_id = 3 AND level_number = 3;
