const Database = require('./db');

const db = new Database();

// JavaScript Advanced Course Levels
const jsAdvancedLevels = [
    {
        level_number: 1,
        title: "Arra<PERSON> und Schleifen",
        description: "Arbeite mit Listen von Daten",
        content: `
            <h3>Arrays in JavaScript</h3>
            <p>Arrays speichern mehrere Werte in einer Variable.</p>
            
            <h4>Array erstellen:</h4>
            <pre><code>const früchte = ["Apfel", "Banane", "Orange"];
const zahlen = [1, 2, 3, 4, 5];
const gemischt = ["Text", 42, true];</code></pre>

            <h4>Array-Methoden:</h4>
            <pre><code>früchte.push("Traube");        // Hinzufügen
früchte.pop();                 // Letztes entfernen
früchte.length;                // Anzahl Elemente</code></pre>

            <h4>For-Schleife:</h4>
            <pre><code>for (let i = 0; i < früchte.length; i++) {
    console.log(früchte[i]);
}</code></pre>
        `,
        exercise_type: "project",
        exercise_data: "Erstelle ein Array mit 3 Städten und gib alle mit einer Schleife aus.",
        expected_output: "Berlin\nMünchen\nHamburg",
        test_cases: JSON.stringify([])
    },
    {
        level_number: 2,
        title: "Objekte und Eigenschaften",
        description: "Strukturiere Daten mit Objekten",
        content: `
            <h3>JavaScript Objekte</h3>
            <p>Objekte gruppieren zusammengehörige Daten und Funktionen.</p>
            
            <h4>Objekt erstellen:</h4>
            <pre><code>const person = {
    name: "Anna",
    alter: 28,
    stadt: "Berlin",
    grüßen: function() {
        return "Hallo, ich bin " + this.name;
    }
};</code></pre>

            <h4>Auf Eigenschaften zugreifen:</h4>
            <pre><code>console.log(person.name);        // "Anna"
console.log(person["alter"]);    // 28
console.log(person.grüßen());    // "Hallo, ich bin Anna"</code></pre>
        `,
        exercise_type: "project",
        exercise_data: "Erstelle ein Auto-Objekt mit Marke, Modell und einer Methode zum Starten.",
        expected_output: "BMW X5 startet!",
        test_cases: JSON.stringify([])
    },
    {
        level_number: 3,
        title: "Array-Methoden",
        description: "Moderne Array-Funktionen nutzen",
        content: `
            <h3>Moderne Array-Methoden</h3>
            <p>JavaScript bietet viele nützliche Methoden für Arrays.</p>
            
            <h4>map() - Transformiert jedes Element:</h4>
            <pre><code>const zahlen = [1, 2, 3, 4];
const verdoppelt = zahlen.map(x => x * 2);
console.log(verdoppelt); // [2, 4, 6, 8]</code></pre>

            <h4>filter() - Filtert Elemente:</h4>
            <pre><code>const zahlen = [1, 2, 3, 4, 5, 6];
const gerade = zahlen.filter(x => x % 2 === 0);
console.log(gerade); // [2, 4, 6]</code></pre>

            <h4>reduce() - Reduziert zu einem Wert:</h4>
            <pre><code>const zahlen = [1, 2, 3, 4];
const summe = zahlen.reduce((acc, x) => acc + x, 0);
console.log(summe); // 10</code></pre>
        `,
        exercise_type: "project",
        exercise_data: "Verwende map(), um alle Zahlen in einem Array zu quadrieren.",
        expected_output: "1,4,9,16",
        test_cases: JSON.stringify([])
    },
    {
        level_number: 4,
        title: "Event Handling",
        description: "Reagiere auf Benutzerinteraktionen",
        content: `
            <h3>Events in JavaScript</h3>
            <p>Events ermöglichen es, auf Benutzeraktionen zu reagieren.</p>
            
            <h4>Event Listener hinzufügen:</h4>
            <pre><code>const button = document.getElementById("mein-button");

button.addEventListener("click", function() {
    console.log("Button wurde geklickt!");
});

// Oder mit Arrow Function
button.addEventListener("click", () => {
    console.log("Button geklickt!");
});</code></pre>

            <h4>Häufige Events:</h4>
            <ul>
                <li><code>click</code> - Mausklick</li>
                <li><code>mouseover</code> - Maus über Element</li>
                <li><code>keydown</code> - Taste gedrückt</li>
                <li><code>submit</code> - Formular abgesendet</li>
            </ul>
        `,
        exercise_type: "project",
        exercise_data: "Erstelle einen Button, der bei Klick eine Nachricht ausgibt.",
        expected_output: "Button geklickt!",
        test_cases: JSON.stringify([])
    },
    {
        level_number: 5,
        title: "Promises und async/await",
        description: "Asynchrone Programmierung verstehen",
        content: `
            <h3>Asynchrone Programmierung</h3>
            <p>JavaScript kann Aufgaben parallel ausführen, ohne zu blockieren.</p>
            
            <h4>Promise erstellen:</h4>
            <pre><code>const meinPromise = new Promise((resolve, reject) => {
    setTimeout(() => {
        resolve("Aufgabe erledigt!");
    }, 2000);
});

meinPromise.then(result => {
    console.log(result);
});</code></pre>

            <h4>Async/Await (moderne Syntax):</h4>
            <pre><code>async function warteAufErgebnis() {
    try {
        const result = await meinPromise;
        console.log(result);
    } catch (error) {
        console.log("Fehler:", error);
    }
}</code></pre>
        `,
        exercise_type: "project",
        exercise_data: "Erstelle eine async Funktion, die nach 1 Sekunde 'Fertig!' ausgibt.",
        expected_output: "Fertig!",
        test_cases: JSON.stringify([])
    }
];

// Function to seed JavaScript Advanced course levels
function seedJsAdvancedLevels() {
    // Get course ID for JavaScript Advanced
    db.getCourseBySlug('javascript-advanced', (err, course) => {
        if (err || !course) {
            console.error('JavaScript Advanced course not found');
            return;
        }

        console.log('Seeding JavaScript Advanced levels...');
        
        jsAdvancedLevels.forEach((levelData, index) => {
            const sql = `INSERT OR REPLACE INTO levels 
                (course_id, level_number, title, description, content, exercise_type, exercise_data, expected_output, test_cases) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`;
            
            db.db.run(sql, [
                course.id,
                levelData.level_number,
                levelData.title,
                levelData.description,
                levelData.content,
                levelData.exercise_type,
                levelData.exercise_data,
                levelData.expected_output,
                levelData.test_cases
            ], (err) => {
                if (err) {
                    console.error(`Error inserting level ${levelData.level_number}:`, err);
                } else {
                    console.log(`Level ${levelData.level_number} inserted successfully`);
                }
                
                // Close database connection after last level
                if (index === jsAdvancedLevels.length - 1) {
                    setTimeout(() => {
                        db.close();
                        console.log('JavaScript Advanced levels seeded successfully!');
                    }, 100);
                }
            });
        });
    });
}

// Run if called directly
if (require.main === module) {
    seedJsAdvancedLevels();
}

module.exports = { seedJsAdvancedLevels };
