const sqlite3 = require('sqlite3').verbose();
const { dbPath } = require('./init');

class Database {
    constructor() {
        this.db = new sqlite3.Database(dbPath);
    }

    // User methods
    createUser(username, email, passwordHash, callback) {
        const sql = `INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)`;
        this.db.run(sql, [username, email, passwordHash], callback);
    }

    getUserByEmail(email, callback) {
        const sql = `SELECT * FROM users WHERE email = ?`;
        this.db.get(sql, [email], callback);
    }

    getUserByUsername(username, callback) {
        const sql = `SELECT * FROM users WHERE username = ?`;
        this.db.get(sql, [username], callback);
    }

    getUserById(id, callback) {
        const sql = `SELECT * FROM users WHERE id = ?`;
        this.db.get(sql, [id], callback);
    }

    updateLastLogin(userId, callback) {
        const sql = `UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?`;
        this.db.run(sql, [userId], callback);
    }

    // Email verification methods
    createEmailVerificationToken(userId, token, expiresAt, callback) {
        const sql = `INSERT INTO email_verification_tokens (user_id, token, expires_at) VALUES (?, ?, ?)`;
        this.db.run(sql, [userId, token, expiresAt], callback);
    }

    getEmailVerificationToken(token, callback) {
        const sql = `SELECT * FROM email_verification_tokens WHERE token = ? AND expires_at > datetime('now')`;
        this.db.get(sql, [token], callback);
    }

    verifyUserEmail(userId, callback) {
        const sql = `UPDATE users SET email_verified = 1 WHERE id = ?`;
        this.db.run(sql, [userId], callback);
    }

    deleteEmailVerificationToken(token, callback) {
        const sql = `DELETE FROM email_verification_tokens WHERE token = ?`;
        this.db.run(sql, [token], callback);
    }

    // Password reset methods
    createPasswordResetToken(userId, token, expiresAt, callback) {
        const sql = `INSERT INTO password_reset_tokens (user_id, token, expires_at) VALUES (?, ?, ?)`;
        this.db.run(sql, [userId, token, expiresAt], callback);
    }

    getPasswordResetToken(token, callback) {
        const sql = `SELECT * FROM password_reset_tokens WHERE token = ? AND expires_at > datetime('now') AND used = 0`;
        this.db.get(sql, [token], callback);
    }

    markPasswordResetTokenUsed(token, callback) {
        const sql = `UPDATE password_reset_tokens SET used = 1 WHERE token = ?`;
        this.db.run(sql, [token], callback);
    }

    updateUserPassword(userId, passwordHash, callback) {
        const sql = `UPDATE users SET password_hash = ? WHERE id = ?`;
        this.db.run(sql, [passwordHash, userId], callback);
    }

    // Admin methods
    getAllUsers(callback) {
        const sql = `SELECT id, username, email, created_at, is_admin, is_banned FROM users ORDER BY created_at DESC`;
        this.db.all(sql, callback);
    }

    updateUserAdminStatus(userId, isAdmin, adminUserId, callback) {
        const sql = `UPDATE users SET is_admin = ? WHERE id = ?`;
        this.db.run(sql, [isAdmin ? 1 : 0, userId], (err) => {
            if (!err) {
                this.logAdminAction(adminUserId, 'UPDATE_ADMIN_STATUS', userId, null, `Set admin status to ${isAdmin}`, () => {});
            }
            callback(err);
        });
    }

    updateUserBanStatus(userId, isBanned, adminUserId, callback) {
        const sql = `UPDATE users SET is_banned = ? WHERE id = ?`;
        this.db.run(sql, [isBanned ? 1 : 0, userId], (err) => {
            if (!err) {
                this.logAdminAction(adminUserId, 'UPDATE_BAN_STATUS', userId, null, `Set banned status to ${isBanned}`, () => {});
            }
            callback(err);
        });
    }

    deleteUser(userId, adminUserId, callback) {
        const sql = `DELETE FROM users WHERE id = ?`;
        this.db.run(sql, [userId], (err) => {
            if (!err) {
                this.logAdminAction(adminUserId, 'DELETE_USER', userId, null, 'User deleted', () => {});
            }
            callback(err);
        });
    }

    getAdminSetting(key, callback) {
        const sql = `SELECT setting_value FROM admin_settings WHERE setting_key = ?`;
        this.db.get(sql, [key], callback);
    }

    updateAdminSetting(key, value, adminUserId, callback) {
        const sql = `INSERT OR REPLACE INTO admin_settings (setting_key, setting_value, updated_by) VALUES (?, ?, ?)`;
        this.db.run(sql, [key, value, adminUserId], (err) => {
            if (!err) {
                this.logAdminAction(adminUserId, 'UPDATE_SETTING', null, null, `Updated ${key} to ${value}`, () => {});
            }
            callback(err);
        });
    }

    grantCourseAccess(userId, courseId, adminUserId, callback) {
        const sql = `INSERT OR REPLACE INTO user_course_access (user_id, course_id, granted_by) VALUES (?, ?, ?)`;
        this.db.run(sql, [userId, courseId, adminUserId], (err) => {
            if (!err) {
                this.logAdminAction(adminUserId, 'GRANT_COURSE_ACCESS', userId, courseId, 'Course access granted', () => {});
            }
            callback(err);
        });
    }

    revokeCourseAccess(userId, courseId, adminUserId, callback) {
        const sql = `DELETE FROM user_course_access WHERE user_id = ? AND course_id = ?`;
        this.db.run(sql, [userId, courseId], (err) => {
            if (!err) {
                this.logAdminAction(adminUserId, 'REVOKE_COURSE_ACCESS', userId, courseId, 'Course access revoked', () => {});
            }
            callback(err);
        });
    }

    getUserCourseAccess(userId, callback) {
        const sql = `SELECT course_id FROM user_course_access WHERE user_id = ?`;
        this.db.all(sql, [userId], callback);
    }

    logAdminAction(adminUserId, action, targetUserId, targetCourseId, details, callback) {
        const sql = `INSERT INTO admin_audit_log (admin_user_id, action, target_user_id, target_course_id, details) VALUES (?, ?, ?, ?, ?)`;
        this.db.run(sql, [adminUserId, action, targetUserId, targetCourseId, details], callback);
    }

    getAdminAuditLog(limit = 100, callback) {
        const sql = `
            SELECT
                aal.*,
                admin.username as admin_username,
                target.username as target_username,
                c.name as course_name
            FROM admin_audit_log aal
            LEFT JOIN users admin ON aal.admin_user_id = admin.id
            LEFT JOIN users target ON aal.target_user_id = target.id
            LEFT JOIN courses c ON aal.target_course_id = c.id
            ORDER BY aal.timestamp DESC
            LIMIT ?
        `;
        this.db.all(sql, [limit], callback);
    }

    // Course methods
    getAllCourses(callback) {
        const sql = `SELECT * FROM courses ORDER BY id`;
        this.db.all(sql, callback);
    }

    getCourseBySlug(slug, callback) {
        const sql = `SELECT * FROM courses WHERE slug = ?`;
        this.db.get(sql, [slug], callback);
    }

    // Level methods
    getLevelsByCourse(courseId, callback) {
        const sql = `SELECT * FROM levels WHERE course_id = ? ORDER BY level_number`;
        this.db.all(sql, [courseId], callback);
    }

    getLevelById(levelId, callback) {
        const sql = `SELECT * FROM levels WHERE id = ?`;
        this.db.get(sql, [levelId], callback);
    }

    getLevel(courseId, levelNumber, callback) {
        const sql = `SELECT * FROM levels WHERE course_id = ? AND level_number = ?`;
        this.db.get(sql, [courseId, levelNumber], callback);
    }

    // Progress methods
    getUserProgress(userId, courseId, callback) {
        const sql = `
            SELECT up.*, l.level_number, l.title 
            FROM user_progress up 
            JOIN levels l ON up.level_id = l.id 
            WHERE up.user_id = ? AND up.course_id = ? 
            ORDER BY l.level_number
        `;
        this.db.all(sql, [userId, courseId], callback);
    }

    getUserLevelProgress(userId, levelId, callback) {
        const sql = `SELECT * FROM user_progress WHERE user_id = ? AND level_id = ?`;
        this.db.get(sql, [userId, levelId], callback);
    }

    createOrUpdateProgress(userId, courseId, levelId, completed, score, callback) {
        const sql = `
            INSERT OR REPLACE INTO user_progress 
            (user_id, course_id, level_id, completed, score, attempts, completed_at, created_at) 
            VALUES (?, ?, ?, ?, ?, 
                COALESCE((SELECT attempts FROM user_progress WHERE user_id = ? AND level_id = ?), 0) + 1,
                CASE WHEN ? THEN CURRENT_TIMESTAMP ELSE NULL END,
                COALESCE((SELECT created_at FROM user_progress WHERE user_id = ? AND level_id = ?), CURRENT_TIMESTAMP)
            )
        `;
        this.db.run(sql, [userId, courseId, levelId, completed, score, userId, levelId, completed, userId, levelId], callback);
    }

    // Code submission methods
    saveCodeSubmission(userId, levelId, code, output, passed, callback) {
        const sql = `INSERT INTO code_submissions (user_id, level_id, submitted_code, output, passed) VALUES (?, ?, ?, ?, ?)`;
        this.db.run(sql, [userId, levelId, code, output, passed], callback);
    }

    getLastSubmission(userId, levelId, callback) {
        const sql = `SELECT * FROM code_submissions WHERE user_id = ? AND level_id = ? ORDER BY submitted_at DESC LIMIT 1`;
        this.db.get(sql, [userId, levelId], callback);
    }

    // Utility methods
    getNextAvailableLevel(userId, courseId, callback) {
        const sql = `
            SELECT l.* FROM levels l
            LEFT JOIN user_progress up ON l.id = up.level_id AND up.user_id = ?
            WHERE l.course_id = ? AND (up.completed IS NULL OR up.completed = 0)
            ORDER BY l.level_number
            LIMIT 1
        `;
        this.db.get(sql, [userId, courseId], callback);
    }

    isLevelUnlocked(userId, courseId, levelNumber, callback) {
        if (levelNumber === 1) {
            callback(null, true);
            return;
        }

        const sql = `
            SELECT COUNT(*) as completed_count
            FROM user_progress up
            JOIN levels l ON up.level_id = l.id
            WHERE up.user_id = ? AND l.course_id = ? AND l.level_number < ? AND up.completed = 1
        `;
        this.db.get(sql, [userId, courseId, levelNumber], (err, row) => {
            if (err) {
                callback(err);
                return;
            }
            // Level is unlocked if all previous levels are completed
            callback(null, row.completed_count >= levelNumber - 1);
        });
    }

    // Get course statistics for dashboard
    getCourseStats(userId, courseId, callback) {
        const sql = `
            SELECT
                COUNT(*) as total_levels,
                COUNT(CASE WHEN up.completed = 1 THEN 1 END) as completed_levels,
                COALESCE(SUM(up.score), 0) as total_score
            FROM levels l
            LEFT JOIN user_progress up ON l.id = up.level_id AND up.user_id = ?
            WHERE l.course_id = ?
        `;
        this.db.get(sql, [userId, courseId], callback);
    }

    // Achievement methods
    getAllAchievements(callback) {
        const sql = `SELECT * FROM achievements ORDER BY requirement_value ASC`;
        this.db.all(sql, callback);
    }

    getUserAchievements(userId, callback) {
        const sql = `
            SELECT a.*, ua.earned_at
            FROM achievements a
            JOIN user_achievements ua ON a.id = ua.achievement_id
            WHERE ua.user_id = ?
            ORDER BY ua.earned_at DESC
        `;
        this.db.all(sql, [userId], callback);
    }

    checkAndAwardAchievements(userId, callback) {
        // Use the new achievement system
        const AchievementSystem = require('../services/achievementSystem');
        const achievementSystem = new AchievementSystem(this);

        achievementSystem.checkAndAwardAchievements(userId)
            .then(newAchievements => {
                callback(null, newAchievements);
            })
            .catch(err => {
                console.error('Achievement system error:', err);
                callback(err);
            });
    }

    getUserLanguageStats(userId, callback) {
        const sql = `
            SELECT
                c.slug as language,
                COUNT(CASE WHEN up.completed = 1 THEN 1 END) as completed_levels,
                COALESCE(SUM(up.score), 0) as total_score
            FROM courses c
            LEFT JOIN levels l ON c.id = l.course_id
            LEFT JOIN user_progress up ON l.id = up.level_id AND up.user_id = ?
            GROUP BY c.id, c.slug
        `;

        this.db.all(sql, [userId], (err, rows) => {
            if (err) {
                callback(err);
                return;
            }

            // Convert to object with language as key
            const languageStats = {};
            rows.forEach(row => {
                // Map course slugs to achievement languages
                let language = row.language;
                if (language === 'html-css-js') {
                    language = 'html-css-js';
                } else if (language === 'javascript-advanced') {
                    language = 'javascript';
                }

                languageStats[language] = {
                    completed_levels: row.completed_levels,
                    total_score: row.total_score
                };
            });

            callback(null, languageStats);
        });
    }

    awardAchievement(userId, achievementId, callback) {
        const sql = `INSERT OR IGNORE INTO user_achievements (user_id, achievement_id) VALUES (?, ?)`;
        this.db.run(sql, [userId, achievementId], callback);
    }

    getUserTotalStats(userId, callback) {
        const sql = `
            SELECT
                COALESCE(COUNT(CASE WHEN up.completed = 1 THEN 1 END), 0) as total_completed_levels,
                (COALESCE(SUM(up.score), 0) + COALESCE((SELECT SUM(a.points) FROM user_achievements ua JOIN achievements a ON ua.achievement_id = a.id WHERE ua.user_id = ?), 0)) as total_score,
                COALESCE(COUNT(DISTINCT CASE WHEN course_completed.completed_levels = course_completed.total_levels THEN up.course_id END), 0) as completed_courses
            FROM (SELECT 1) dummy
            LEFT JOIN user_progress up ON up.user_id = ?
            LEFT JOIN (
                SELECT
                    up2.course_id,
                    COUNT(*) as total_levels,
                    COUNT(CASE WHEN up2.completed = 1 THEN 1 END) as completed_levels
                FROM user_progress up2
                JOIN levels l ON up2.level_id = l.id
                WHERE up2.user_id = ?
                GROUP BY up2.course_id
            ) course_completed ON up.course_id = course_completed.course_id
        `;
        this.db.get(sql, [userId, userId, userId], callback);
    }

    getUserTotalScore(userId, callback) {
        const sql = `
            SELECT
                (SELECT COALESCE(SUM(score), 0) FROM user_progress WHERE user_id = ?) as level_score,
                (SELECT COALESCE(SUM(a.points), 0) FROM user_achievements ua JOIN achievements a ON ua.achievement_id = a.id WHERE ua.user_id = ?) as achievement_score
        `;
        this.db.get(sql, [userId, userId], (err, result) => {
            if (err) {
                callback(err);
                return;
            }
            const totalScore = (result.level_score || 0) + (result.achievement_score || 0);
            callback(null, totalScore);
        });
    }

    getRecentActivities(userId, limit = 10, callback) {
        const sql = `
            SELECT
                'level_completed' as activity_type,
                l.title as activity_title,
                c.name as course_name,
                c.slug as course_slug,
                up.score as points,
                up.completed_at as activity_date,
                l.level_number
            FROM user_progress up
            JOIN levels l ON up.level_id = l.id
            JOIN courses c ON l.course_id = c.id
            WHERE up.user_id = ? AND up.completed = 1

            UNION ALL

            SELECT
                'achievement_earned' as activity_type,
                a.name as activity_title,
                'Achievement' as course_name,
                '' as course_slug,
                a.points as points,
                ua.earned_at as activity_date,
                0 as level_number
            FROM user_achievements ua
            JOIN achievements a ON ua.achievement_id = a.id
            WHERE ua.user_id = ?

            ORDER BY activity_date DESC
            LIMIT ?
        `;
        this.db.all(sql, [userId, userId, limit], callback);
    }

    // Translation methods
    getTranslations(language, callback) {
        const sql = "SELECT key, value FROM translations WHERE language = ?";
        this.db.all(sql, [language], (err, rows) => {
            if (err) {
                callback(err, null);
                return;
            }

            const translations = {};
            rows.forEach(row => {
                translations[row.key] = row.value;
            });

            callback(null, translations);
        });
    }

    getTranslation(key, language, callback) {
        const sql = "SELECT value FROM translations WHERE key = ? AND language = ?";
        this.db.get(sql, [key, language], (err, row) => {
            if (err) {
                callback(err, null);
                return;
            }

            callback(null, row ? row.value : key);
        });
    }

    // Support ticket methods
    getAllSupportTickets(callback) {
        const query = `
            SELECT st.*, u.username, u.email,
                   COUNT(sm.id) as message_count,
                   MAX(sm.created_at) as last_message_at
            FROM support_tickets st
            JOIN users u ON st.user_id = u.id
            LEFT JOIN support_messages sm ON st.id = sm.ticket_id
            GROUP BY st.id
            ORDER BY st.created_at DESC
        `;
        this.db.all(query, [], callback);
    }

    getSupportTicketById(ticketId, callback) {
        const query = `
            SELECT st.*, u.username, u.email
            FROM support_tickets st
            JOIN users u ON st.user_id = u.id
            WHERE st.id = ?
        `;
        this.db.get(query, [ticketId], callback);
    }

    getSupportMessages(ticketId, callback) {
        const query = `
            SELECT sm.*, u.username, u.is_admin as is_staff
            FROM support_messages sm
            JOIN users u ON sm.user_id = u.id
            WHERE sm.ticket_id = ?
            ORDER BY sm.created_at ASC
        `;
        this.db.all(query, [ticketId], callback);
    }

    createSupportTicket(userId, title, message, callback) {
        const ticketQuery = `INSERT INTO support_tickets (user_id, title, status) VALUES (?, ?, 'open')`;

        this.db.run(ticketQuery, [userId, title], function(err) {
            if (err) {
                callback(err);
                return;
            }

            const ticketId = this.lastID;
            const messageQuery = `INSERT INTO support_messages (ticket_id, user_id, message) VALUES (?, ?, ?)`;

            this.db.run(messageQuery, [ticketId, userId, message], (err) => {
                callback(err, ticketId);
            });
        }.bind(this));
    }

    addSupportMessage(ticketId, userId, message, callback) {
        const query = `INSERT INTO support_messages (ticket_id, user_id, message) VALUES (?, ?, ?)`;
        this.db.run(query, [ticketId, userId, message], callback);
    }

    updateSupportTicketStatus(ticketId, status, callback) {
        const query = `UPDATE support_tickets SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?`;
        this.db.run(query, [status, ticketId], callback);
    }

    getUserSupportTickets(userId, callback) {
        const query = `
            SELECT st.*, COUNT(sm.id) as message_count
            FROM support_tickets st
            LEFT JOIN support_messages sm ON st.id = sm.ticket_id
            WHERE st.user_id = ?
            GROUP BY st.id
            ORDER BY st.created_at DESC
        `;
        this.db.all(query, [userId], callback);
    }

    // Reset user progress (keeps account data and premium access)
    resetUserProgress(userId, callback) {
        const queries = [
            // Delete user progress
            `DELETE FROM user_progress WHERE user_id = ?`,
            // Delete user achievements
            `DELETE FROM user_achievements WHERE user_id = ?`,
            // Delete code submissions
            `DELETE FROM code_submissions WHERE user_id = ?`,
            // Delete user level completions (if table exists)
            `DELETE FROM user_level_completions WHERE user_id = ?`,
            // Delete user submissions (if table exists)
            `DELETE FROM user_submissions WHERE user_id = ?`
        ];

        let completedQueries = 0;
        let hasError = false;
        let deletedCounts = [];

        queries.forEach((query, index) => {
            this.db.run(query, [userId], function(err) {
                if (err && !err.message.includes('no such table')) {
                    // Only report errors that aren't "table doesn't exist"
                    console.error(`Error in reset query ${index + 1}:`, err);
                    hasError = true;
                } else if (!err) {
                    deletedCounts.push(`Query ${index + 1}: ${this.changes} rows affected`);
                }

                completedQueries++;

                if (completedQueries === queries.length) {
                    if (hasError) {
                        callback(new Error('Some reset operations failed'));
                    } else {
                        console.log(`Successfully reset progress for user ${userId}:`);
                        deletedCounts.forEach(count => console.log(`  - ${count}`));
                        callback(null);
                    }
                }
            });
        });
    }

    close() {
        this.db.close();
    }
}

module.exports = Database;
