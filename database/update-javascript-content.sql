-- JavaScript Level 6-20 Content Updates
UPDATE levels SET content = '
            <h3>Arrow Functions</h3>
            <p>Arrow Functions sind eine moderne, kürzere Syntax für Funktionen in JavaScript. Sie haben auch ein anderes Verhalten bei <code>this</code>.</p>
            
            <h4>Grundlegende Syntax:</h4>
            <pre><code>// Traditionelle Funktion
function add(a, b) {
    return a + b;
}

// Arrow Function
const add = (a, b) => {
    return a + b;
};

// Kurze Form (bei einem Ausdruck)
const add = (a, b) => a + b;

// Ein Parameter (Klammern optional)
const square = x => x * x;

// Keine Parameter
const greet = () => "Hallo Welt!";</code></pre>

            <h4>Praktische Beispiele:</h4>
            <pre><code>// Array-Methoden mit Arrow Functions
const numbers = [1, 2, 3, 4, 5];

const doubled = numbers.map(n => n * 2);
const evens = numbers.filter(n => n % 2 === 0);
const sum = numbers.reduce((acc, n) => acc + n, 0);

console.log("Verdoppelt:", doubled);  // [2, 4, 6, 8, 10]
console.log("Gerade:", evens);        // [2, 4]
console.log("Summe:", sum);           // 15</code></pre>

            <h4>this-Verhalten:</h4>
            <pre><code>const obj = {
    name: "Max",
    
    // Traditionelle Funktion - eigenes this
    sayHello: function() {
        console.log("Hallo, ich bin " + this.name);
    },
    
    // Arrow Function - erbt this vom umgebenden Scope
    sayHi: () => {
        console.log("Hi, ich bin " + this.name); // undefined!
    }
};</code></pre>
        ' WHERE course_id = 2 AND level_number = 6;

UPDATE levels SET content = '
            <h3>Destructuring</h3>
            <p>Destructuring ermöglicht es dir, Werte aus Arrays und Objekten in separate Variablen zu extrahieren.</p>
            
            <h4>Array Destructuring:</h4>
            <pre><code>const colors = ["rot", "grün", "blau", "gelb"];

// Traditionell
const first = colors[0];
const second = colors[1];

// Mit Destructuring
const [first, second, third] = colors;
console.log(first);  // "rot"
console.log(second); // "grün"

// Elemente überspringen
const [, , blue] = colors;
console.log(blue); // "blau"

// Rest-Operator
const [primary, ...others] = colors;
console.log(primary); // "rot"
console.log(others);  // ["grün", "blau", "gelb"]</code></pre>

            <h4>Object Destructuring:</h4>
            <pre><code>const person = {
    name: "Anna",
    age: 25,
    city: "Berlin",
    country: "Deutschland"
};

// Eigenschaften extrahieren
const { name, age } = person;
console.log(name); // "Anna"
console.log(age);  // 25

// Umbenennen
const { name: personName, city: hometown } = person;

// Standardwerte
const { name, profession = "Unbekannt" } = person;

// Nested Destructuring
const user = {
    id: 1,
    profile: {
        name: "Max",
        settings: { theme: "dark" }
    }
};

const { profile: { name, settings: { theme } } } = user;</code></pre>
        ' WHERE course_id = 2 AND level_number = 7;

UPDATE levels SET content = '
            <h3>Template Literals</h3>
            <p>Template Literals ermöglichen es dir, Strings mit eingebetteten Ausdrücken und mehrzeiligen Text zu erstellen.</p>
            
            <h4>Grundlegende Syntax:</h4>
            <pre><code>const name = "Max";
const age = 25;

// Traditionelle String-Konkatenation
const message1 = "Hallo, ich bin " + name + " und " + age + " Jahre alt.";

// Template Literal
const message2 = `Hallo, ich bin ${name} und ${age} Jahre alt.`;

// Ausdrücke in Template Literals
const message3 = `Nächstes Jahr bin ich ${age + 1} Jahre alt.`;

console.log(message2); // "Hallo, ich bin Max und 25 Jahre alt."</code></pre>

            <h4>Mehrzeilige Strings:</h4>
            <pre><code>// Traditionell (umständlich)
const html1 = "&lt;div&gt;\n" +
              "  &lt;h1&gt;Titel&lt;/h1&gt;\n" +
              "  &lt;p&gt;Inhalt&lt;/p&gt;\n" +
              "&lt;/div&gt;";

// Mit Template Literals
const html2 = `
&lt;div&gt;
  &lt;h1&gt;Titel&lt;/h1&gt;
  &lt;p&gt;Inhalt&lt;/p&gt;
&lt;/div&gt;
`;</code></pre>

            <h4>Erweiterte Verwendung:</h4>
            <pre><code>// Funktionen in Template Literals
function formatPrice(price) {
    return `${price.toFixed(2)}€`;
}

const product = { name: "Laptop", price: 999.99 };
const productInfo = `${product.name}: ${formatPrice(product.price)}`;

// Bedingte Inhalte
const user = { name: "Anna", isAdmin: true };
const greeting = `Hallo ${user.name}${user.isAdmin ? " (Administrator)" : ""}!`;

// Tagged Template Literals (erweitert)
function highlight(strings, ...values) {
    return strings.reduce((result, string, i) => {
        return result + string + (values[i] ? `&lt;mark&gt;${values[i]}&lt;/mark&gt;` : "");
    }, "");
}

const searchTerm = "JavaScript";
const text = highlight`Ich lerne ${searchTerm} Programmierung.`;</code></pre>
        ' WHERE course_id = 2 AND level_number = 8;
