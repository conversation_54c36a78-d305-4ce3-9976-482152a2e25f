-- Neue Kurse hinzufügen
INSERT OR IGNORE INTO courses (id, name, description, slug, total_levels) VALUES
(3, 'PHP Webentwicklung', 'Lerne PHP für moderne Webentwicklung mit MySQL, Laravel und APIs', 'php', 20),
(4, 'Python Programmierung', 'Von Grundlagen bis Data Science und Machine Learning mit Python', 'python', 20),
(5, 'Go Programming', 'Moderne Systemprogrammierung mit Go - Concurrency, Microservices und Cloud', 'go', 20),
(6, 'Java Enterprise', 'Objektorientierte Programmierung und Enterprise-Entwicklung mit Java', 'java', 20);

-- HTML-CSS-JS Level 11-20 hinzufügen
INSERT OR IGNORE INTO levels (course_id, level_number, title, description, content, exercise_type, expected_output) VALUES
(1, 11, 'CSS Grid Layout', 'Erstelle komplexe Layouts mit CSS Grid', 'Lerne CSS Grid für moderne Layouts. E<PERSON><PERSON> ein Grid-Layout mit 3 Spalten.', 'code_example', 'grid-template-columns'),
(1, 12, 'JavaScript Events', 'Interaktive Webseiten mit Event Handling', 'Event Listener in JavaScript. Füge einen Click-Event zu einem Button hinzu.', 'code_example', 'addEventListener'),
(1, 13, 'CSS Animations', 'Animationen mit CSS erstellen', 'CSS Animationen mit keyframes erstellen.', 'code_example', 'keyframes'),
(1, 14, 'JavaScript DOM Manipulation', 'Dynamische Inhalte mit JavaScript', 'DOM Manipulation mit createElement und appendChild.', 'code_example', 'createElement'),
(1, 15, 'Responsive Design', 'Mobile-first Design mit Media Queries', 'Media Queries für responsive Design verwenden.', 'code_example', 'media'),
(1, 16, 'JavaScript Fetch API', 'Daten von APIs laden', 'Fetch API für HTTP-Requests verwenden.', 'code_example', 'fetch'),
(1, 17, 'CSS Flexbox Advanced', 'Erweiterte Flexbox Techniken', 'Erweiterte Flexbox-Eigenschaften nutzen.', 'code_example', 'justify-content'),
(1, 18, 'JavaScript Local Storage', 'Daten im Browser speichern', 'Local Storage für persistente Daten verwenden.', 'code_example', 'localStorage'),
(1, 19, 'CSS Custom Properties', 'CSS Variablen verwenden', 'CSS Custom Properties definieren und verwenden.', 'code_example', 'var(--'),
(1, 20, 'JavaScript Modules', 'Code in Module organisieren', 'ES6 Module mit import/export verwenden.', 'code_example', 'export');

-- JavaScript Level 6-20 hinzufügen
INSERT OR IGNORE INTO levels (course_id, level_number, title, description, content, exercise_type, expected_output) VALUES
(2, 6, 'Arrow Functions', 'Moderne Funktionssyntax mit Arrow Functions', 'Arrow Functions für kompakte Funktionsdefinitionen.', 'code_example', '=>'),
(2, 7, 'Destructuring', 'Objekte und Arrays destructuring', 'Destructuring Assignment für elegante Wertzuweisung.', 'code_example', 'const {'),
(2, 8, 'Template Literals', 'String-Interpolation mit Template Literals', 'Template Literals für String-Interpolation verwenden.', 'code_example', '`'),
(2, 9, 'Spread Operator', 'Arrays und Objekte mit Spread Operator', 'Spread Operator für Array- und Objekt-Manipulation.', 'code_example', '...'),
(2, 10, 'Promises', 'Asynchrone Programmierung mit Promises', 'Promises für asynchrone Operationen verwenden.', 'code_example', 'Promise'),
(2, 11, 'Async/Await', 'Moderne asynchrone Programmierung', 'Async/Await für sauberen asynchronen Code.', 'code_example', 'async'),
(2, 12, 'Classes', 'Objektorientierte Programmierung mit Classes', 'ES6 Classes für OOP verwenden.', 'code_example', 'class'),
(2, 13, 'Modules Import/Export', 'Module System in JavaScript', 'Module System mit import/export verwenden.', 'code_example', 'import'),
(2, 14, 'Error Handling', 'Fehlerbehandlung mit try/catch', 'Error Handling mit try/catch/finally.', 'code_example', 'try'),
(2, 15, 'Regular Expressions', 'Pattern Matching mit RegEx', 'Regular Expressions für Pattern Matching.', 'code_example', 'RegExp'),
(2, 16, 'Closures', 'Closures und Scope verstehen', 'Closures für Datenkapselung verwenden.', 'code_example', 'closure'),
(2, 17, 'Prototypes', 'Prototype-basierte Vererbung', 'Prototypes für Vererbung verwenden.', 'code_example', 'prototype'),
(2, 18, 'Generators', 'Generator Functions verwenden', 'Generators für iterative Datenstrukturen.', 'code_example', 'function*'),
(2, 19, 'Proxy Objects', 'Meta-Programmierung mit Proxy', 'Proxy Objects für Meta-Programmierung.', 'code_example', 'Proxy'),
(2, 20, 'Web Workers', 'Multi-Threading im Browser', 'Web Workers für Background-Processing.', 'code_example', 'Worker');

-- PHP Level 1-20
INSERT OR IGNORE INTO levels (course_id, level_number, title, description, content, exercise_type, expected_output) VALUES
(3, 1, 'PHP Grundlagen', 'Erste Schritte mit PHP', 'PHP Basics mit echo und Variablen.', 'code_example', 'echo'),
(3, 2, 'Variablen und Datentypen', 'PHP Variablen verstehen', 'PHP Variablen und Datentypen verwenden.', 'code_example', '$'),
(3, 3, 'Arrays in PHP', 'Mit Arrays arbeiten', 'PHP Arrays erstellen und verwenden.', 'code_example', 'array'),
(3, 4, 'Kontrollstrukturen', 'If/Else und Schleifen', 'Kontrollstrukturen in PHP verwenden.', 'code_example', 'if'),
(3, 5, 'Funktionen', 'Eigene Funktionen erstellen', 'PHP Funktionen definieren und aufrufen.', 'code_example', 'function'),
(3, 6, 'Superglobals', '$_GET, $_POST und $_SESSION', 'Superglobals für Web-Entwicklung verwenden.', 'code_example', '$_GET'),
(3, 7, 'Formulare verarbeiten', 'HTML Formulare mit PHP', 'Formular-Daten mit PHP verarbeiten.', 'code_example', '$_POST'),
(3, 8, 'Datei-Operationen', 'Dateien lesen und schreiben', 'Datei-Operationen in PHP durchführen.', 'code_example', 'file_get_contents'),
(3, 9, 'MySQL Verbindung', 'Datenbank-Verbindung mit PDO', 'MySQL-Datenbank mit PDO verbinden.', 'code_example', 'PDO'),
(3, 10, 'SQL Queries', 'Daten abfragen und einfügen', 'SQL Queries mit prepared statements.', 'code_example', 'prepare'),
(3, 11, 'Sessions', 'Session-Management', 'PHP Sessions für Benutzerverwaltung.', 'code_example', 'session_start'),
(3, 12, 'Cookies', 'Cookies setzen und lesen', 'PHP Cookies für persistente Daten.', 'code_example', 'setcookie'),
(3, 13, 'Klassen und Objekte', 'OOP in PHP', 'Objektorientierte Programmierung in PHP.', 'code_example', 'class'),
(3, 14, 'Vererbung', 'Klassen-Vererbung', 'Vererbung in PHP verwenden.', 'code_example', 'extends'),
(3, 15, 'Namespaces', 'Code-Organisation mit Namespaces', 'Namespaces für Code-Organisation.', 'code_example', 'namespace'),
(3, 16, 'Composer', 'Dependency Management', 'Composer für Package-Management verwenden.', 'code_example', 'composer'),
(3, 17, 'Laravel Basics', 'Laravel Framework Grundlagen', 'Laravel Framework kennenlernen.', 'code_example', 'Route::'),
(3, 18, 'API Development', 'REST APIs mit PHP', 'REST APIs in PHP entwickeln.', 'code_example', 'json_encode'),
(3, 19, 'Security', 'Sicherheit in PHP', 'Sicherheitsaspekte in PHP beachten.', 'code_example', 'password_hash'),
(3, 20, 'Testing', 'Unit Tests mit PHPUnit', 'Unit Tests in PHP schreiben.', 'code_example', 'PHPUnit');

-- Python Level 1-20
INSERT OR IGNORE INTO levels (course_id, level_number, title, description, content, exercise_type, expected_output) VALUES
(4, 1, 'Python Grundlagen', 'Erste Schritte mit Python', 'Python Basics mit print und Variablen.', 'code_example', 'print'),
(4, 2, 'Variablen und Datentypen', 'Python Datentypen', 'Python Variablen und Datentypen verwenden.', 'code_example', 'str'),
(4, 3, 'Listen und Tupel', 'Datenstrukturen in Python', 'Listen und Tupel in Python verwenden.', 'code_example', 'list'),
(4, 4, 'Dictionaries', 'Key-Value Paare', 'Python Dictionaries verwenden.', 'code_example', 'dict'),
(4, 5, 'Kontrollstrukturen', 'If/Else und Schleifen', 'Kontrollstrukturen in Python verwenden.', 'code_example', 'if'),
(4, 6, 'Funktionen', 'Eigene Funktionen definieren', 'Python Funktionen definieren und aufrufen.', 'code_example', 'def'),
(4, 7, 'List Comprehensions', 'Elegante Listen-Erstellung', 'List Comprehensions für kompakte Listen.', 'code_example', 'comprehension'),
(4, 8, 'Klassen und Objekte', 'OOP in Python', 'Objektorientierte Programmierung in Python.', 'code_example', 'class'),
(4, 9, 'Module und Packages', 'Code-Organisation', 'Python Module und Packages verwenden.', 'code_example', 'import'),
(4, 10, 'File I/O', 'Dateien lesen und schreiben', 'Datei-Operationen in Python durchführen.', 'code_example', 'open'),
(4, 11, 'Exception Handling', 'Fehlerbehandlung', 'Exception Handling in Python verwenden.', 'code_example', 'try'),
(4, 12, 'Lambda Functions', 'Anonyme Funktionen', 'Lambda Functions für funktionale Programmierung.', 'code_example', 'lambda'),
(4, 13, 'Decorators', 'Funktions-Dekoratoren', 'Python Decorators verwenden.', 'code_example', 'decorator'),
(4, 14, 'Generators', 'Memory-effiziente Iteratoren', 'Python Generators für effiziente Iteration.', 'code_example', 'yield'),
(4, 15, 'NumPy Basics', 'Numerische Berechnungen', 'NumPy für numerische Berechnungen.', 'code_example', 'numpy'),
(4, 16, 'Pandas Grundlagen', 'Datenanalyse mit Pandas', 'Pandas für Datenanalyse verwenden.', 'code_example', 'pandas'),
(4, 17, 'Matplotlib', 'Datenvisualisierung', 'Matplotlib für Datenvisualisierung.', 'code_example', 'matplotlib'),
(4, 18, 'Web Scraping', 'Daten aus dem Web extrahieren', 'Web Scraping mit Python durchführen.', 'code_example', 'requests'),
(4, 19, 'Machine Learning', 'ML mit scikit-learn', 'Machine Learning mit scikit-learn.', 'code_example', 'sklearn'),
(4, 20, 'Django Basics', 'Web-Framework Django', 'Django Framework kennenlernen.', 'code_example', 'django');

-- Go Level 1-20
INSERT OR IGNORE INTO levels (course_id, level_number, title, description, content, exercise_type, expected_output) VALUES
(5, 1, 'Go Grundlagen', 'Erste Schritte mit Go', 'Go Basics mit fmt.Println und Variablen.', 'code_example', 'fmt.Println'),
(5, 2, 'Variablen und Typen', 'Go Datentypen', 'Go Variablen und Datentypen verwenden.', 'code_example', 'var'),
(5, 3, 'Arrays und Slices', 'Datenstrukturen in Go', 'Arrays und Slices in Go verwenden.', 'code_example', 'slice'),
(5, 4, 'Maps', 'Key-Value Strukturen', 'Go Maps für Key-Value Paare verwenden.', 'code_example', 'map'),
(5, 5, 'Funktionen', 'Funktionen in Go', 'Go Funktionen definieren und aufrufen.', 'code_example', 'func'),
(5, 6, 'Structs', 'Benutzerdefinierte Typen', 'Go Structs für benutzerdefinierte Typen.', 'code_example', 'struct'),
(5, 7, 'Methods', 'Methoden für Structs', 'Go Methods für Structs definieren.', 'code_example', 'method'),
(5, 8, 'Interfaces', 'Polymorphismus mit Interfaces', 'Go Interfaces für Polymorphismus verwenden.', 'code_example', 'interface'),
(5, 9, 'Error Handling', 'Fehlerbehandlung in Go', 'Error Handling in Go durchführen.', 'code_example', 'error'),
(5, 10, 'Goroutines', 'Concurrency Grundlagen', 'Goroutines für Concurrency verwenden.', 'code_example', 'goroutine'),
(5, 11, 'Channels', 'Kommunikation zwischen Goroutines', 'Go Channels für Kommunikation verwenden.', 'code_example', 'channel'),
(5, 12, 'Select Statement', 'Channel-Multiplexing', 'Select Statement für Channel-Multiplexing.', 'code_example', 'select'),
(5, 13, 'Packages', 'Code-Organisation', 'Go Packages für Code-Organisation verwenden.', 'code_example', 'package'),
(5, 14, 'HTTP Server', 'Web-Server erstellen', 'HTTP Server in Go erstellen.', 'code_example', 'http'),
(5, 15, 'JSON Handling', 'JSON verarbeiten', 'JSON in Go verarbeiten.', 'code_example', 'json'),
(5, 16, 'Database', 'Datenbank-Zugriff', 'Datenbank-Zugriff in Go implementieren.', 'code_example', 'database'),
(5, 17, 'Testing', 'Unit Tests in Go', 'Unit Tests in Go schreiben.', 'code_example', 'testing'),
(5, 18, 'Context', 'Context für Cancellation', 'Go Context für Cancellation verwenden.', 'code_example', 'context'),
(5, 19, 'Microservices', 'Service-Architektur', 'Microservices in Go entwickeln.', 'code_example', 'microservice'),
(5, 20, 'Docker Deployment', 'Go Apps containerisieren', 'Go Apps mit Docker containerisieren.', 'code_example', 'docker');

-- Java Level 1-20
INSERT OR IGNORE INTO levels (course_id, level_number, title, description, content, exercise_type, expected_output) VALUES
(6, 1, 'Java Grundlagen', 'Erste Schritte mit Java', 'Java Basics mit System.out.println und Variablen.', 'code_example', 'System.out.println'),
(6, 2, 'Variablen und Datentypen', 'Java Datentypen', 'Java Variablen und Datentypen verwenden.', 'code_example', 'int'),
(6, 3, 'Arrays', 'Arrays in Java', 'Java Arrays erstellen und verwenden.', 'code_example', 'array'),
(6, 4, 'Kontrollstrukturen', 'If/Else und Schleifen', 'Kontrollstrukturen in Java verwenden.', 'code_example', 'if'),
(6, 5, 'Methoden', 'Eigene Methoden erstellen', 'Java Methoden definieren und aufrufen.', 'code_example', 'public static'),
(6, 6, 'Klassen und Objekte', 'OOP Grundlagen', 'Objektorientierte Programmierung in Java.', 'code_example', 'class'),
(6, 7, 'Vererbung', 'Klassen-Vererbung', 'Vererbung in Java verwenden.', 'code_example', 'extends'),
(6, 8, 'Interfaces', 'Abstrakte Verträge', 'Java Interfaces verwenden.', 'code_example', 'interface'),
(6, 9, 'Collections', 'ArrayList und HashMap', 'Java Collections verwenden.', 'code_example', 'ArrayList'),
(6, 10, 'Exception Handling', 'Fehlerbehandlung', 'Exception Handling in Java verwenden.', 'code_example', 'try'),
(6, 11, 'File I/O', 'Dateien lesen und schreiben', 'Datei-Operationen in Java durchführen.', 'code_example', 'Files'),
(6, 12, 'Generics', 'Typsichere Collections', 'Java Generics verwenden.', 'code_example', 'generics'),
(6, 13, 'Lambda Expressions', 'Funktionale Programmierung', 'Lambda Expressions in Java verwenden.', 'code_example', 'lambda'),
(6, 14, 'Streams API', 'Datenverarbeitung mit Streams', 'Streams API für Datenverarbeitung verwenden.', 'code_example', 'stream'),
(6, 15, 'Multithreading', 'Parallele Programmierung', 'Multithreading in Java implementieren.', 'code_example', 'Thread'),
(6, 16, 'Annotations', 'Metadaten für Code', 'Java Annotations verwenden.', 'code_example', 'annotation'),
(6, 17, 'Spring Framework', 'Dependency Injection', 'Spring Framework verwenden.', 'code_example', 'Spring'),
(6, 18, 'JPA/Hibernate', 'Object-Relational Mapping', 'JPA/Hibernate für ORM verwenden.', 'code_example', 'JPA'),
(6, 19, 'REST APIs', 'Web Services mit Spring Boot', 'REST APIs mit Spring Boot entwickeln.', 'code_example', 'RestController'),
(6, 20, 'Testing', 'Unit Tests mit JUnit', 'Unit Tests mit JUnit schreiben.', 'code_example', 'JUnit');
