const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'learning_platform.db');

function initDatabase() {
    const db = new sqlite3.Database(dbPath);

    db.serialize(() => {
        // Users table
        db.run(`CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            email_verified BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_login DATETIME
        )`);

        // Courses table
        db.run(`CREATE TABLE IF NOT EXISTS courses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VA<PERSON>HAR(100) NOT NULL,
            description TEXT,
            slug VARCHAR(50) UNIQUE NOT NULL,
            total_levels INTEGER DEFAULT 20,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`);

        // Levels table
        db.run(`CREATE TABLE IF NOT EXISTS levels (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            course_id INTEGER NOT NULL,
            level_number INTEGER NOT NULL,
            title VARCHAR(200) NOT NULL,
            description TEXT,
            content TEXT NOT NULL,
            exercise_type VARCHAR(50) NOT NULL, -- 'fill_blanks', 'code_example', 'project'
            exercise_data TEXT, -- JSON data for exercises
            expected_output TEXT,
            test_cases TEXT, -- JSON array of test cases
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (course_id) REFERENCES courses(id),
            UNIQUE(course_id, level_number)
        )`);

        // User progress table
        db.run(`CREATE TABLE IF NOT EXISTS user_progress (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            course_id INTEGER NOT NULL,
            level_id INTEGER NOT NULL,
            completed BOOLEAN DEFAULT FALSE,
            score INTEGER DEFAULT 0,
            attempts INTEGER DEFAULT 0,
            completed_at DATETIME,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id),
            FOREIGN KEY (course_id) REFERENCES courses(id),
            FOREIGN KEY (level_id) REFERENCES levels(id),
            UNIQUE(user_id, level_id)
        )`);

        // Code submissions table
        db.run(`CREATE TABLE IF NOT EXISTS code_submissions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            level_id INTEGER NOT NULL,
            submitted_code TEXT NOT NULL,
            output TEXT,
            passed BOOLEAN DEFAULT FALSE,
            submitted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id),
            FOREIGN KEY (level_id) REFERENCES levels(id)
        )`);

        // Achievements table
        db.run(`CREATE TABLE IF NOT EXISTS achievements (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            icon VARCHAR(50),
            points INTEGER DEFAULT 0,
            requirement_type VARCHAR(50), -- 'levels_completed', 'total_score', 'course_completed'
            requirement_value INTEGER,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )`);

        // User achievements table
        db.run(`CREATE TABLE IF NOT EXISTS user_achievements (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            achievement_id INTEGER NOT NULL,
            earned_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id),
            FOREIGN KEY (achievement_id) REFERENCES achievements(id),
            UNIQUE(user_id, achievement_id)
        )`);

        // Email verification tokens table
        db.run(`CREATE TABLE IF NOT EXISTS email_verification_tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            token VARCHAR(255) UNIQUE NOT NULL,
            expires_at DATETIME NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
        )`);

        // Password reset tokens table
        db.run(`CREATE TABLE IF NOT EXISTS password_reset_tokens (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            token VARCHAR(255) UNIQUE NOT NULL,
            expires_at DATETIME NOT NULL,
            used BOOLEAN DEFAULT FALSE,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
        )`);

        // Insert default courses
        db.run(`INSERT OR IGNORE INTO courses (name, description, slug, total_levels) VALUES
            ('HTML, CSS & JavaScript Grundlagen', 'Lerne die Grundlagen der Webentwicklung mit HTML, CSS und JavaScript', 'html-css-js', 20),
            ('JavaScript Vertiefung', 'Vertiefe deine JavaScript-Kenntnisse mit fortgeschrittenen Konzepten', 'javascript-advanced', 20)
        `);

        // Insert default achievements
        db.run(`INSERT OR IGNORE INTO achievements (id, name, description, icon, points, requirement_type, requirement_value) VALUES
            (1, 'Erste Schritte', 'Erstes Level abgeschlossen', 'fas fa-baby', 50, 'levels_completed', 1),
            (2, 'Auf Kurs', '5 Level abgeschlossen', 'fas fa-fire', 100, 'levels_completed', 5),
            (3, 'Durchstarter', '10 Level abgeschlossen', 'fas fa-rocket', 200, 'levels_completed', 10),
            (4, 'Punktesammler', '1000 Punkte erreicht', 'fas fa-crown', 250, 'total_score', 1000),
            (5, 'HTML Meister', 'HTML/CSS/JS Kurs abgeschlossen', 'fab fa-html5', 500, 'course_completed', 1),
            (6, 'JavaScript Ninja', 'JavaScript Vertiefung abgeschlossen', 'fab fa-js-square', 500, 'course_completed', 2)
        `);

        console.log('Database initialized successfully!');
    });

    db.close();
}

module.exports = { initDatabase, dbPath };
