-- Finale Content-Updates für alle verbleibenden Level

-- Python Level 6-20
UPDATE levels SET content = '<h3>File I/O</h3><p>Dateien lesen und schreiben in Python.</p><pre><code># Datei schreiben with open("test.txt", "w") as file: file.write("Hallo Welt!") # Datei lesen with open("test.txt", "r") as file: content = file.read() print(content) # JSON-Dateien import json data = {"name": "Max", "age": 25} with open("data.json", "w") as file: json.dump(data, file)</code></pre>' WHERE course_id = 4 AND level_number = 6;

UPDATE levels SET content = '<h3>Exception Handling</h3><p>Fehlerbehandlung in Python.</p><pre><code>try: number = int(input("Zahl eingeben: ")) result = 10 / number print(f"Ergebnis: {result}") except ValueError: print("Ungültige Eingabe!") except ZeroDivisionError: print("Division durch Null!") except Exception as e: print(f"Unerwarteter <PERSON>hler: {e}") finally: print("Cleanup")</code></pre>' WHERE course_id = 4 AND level_number = 7;

UPDATE levels SET content = '<h3>Modules und Packages</h3><p>Code in Module organisieren.</p><pre><code># math_utils.py def add(a, b): return a + b def multiply(a, b): return a * b PI = 3.14159 # main.py import math_utils from math_utils import add, PI import math_utils as math result = math.add(5, 3) print(f"PI: {PI}")</code></pre>' WHERE course_id = 4 AND level_number = 8;

UPDATE levels SET content = '<h3>Decorators</h3><p>Funktionen erweitern mit Decorators.</p><pre><code>def timer(func): import time def wrapper(*args, **kwargs): start = time.time() result = func(*args, **kwargs) end = time.time() print(f"{func.__name__} took {end-start:.2f}s") return result return wrapper @timer def slow_function(): time.sleep(1) return "Done" result = slow_function()</code></pre>' WHERE course_id = 4 AND level_number = 9;

UPDATE levels SET content = '<h3>Generators</h3><p>Memory-effiziente Iteratoren.</p><pre><code>def fibonacci(): a, b = 0, 1 while True: yield a a, b = b, a + b # Generator verwenden fib = fibonacci() for i in range(10): print(next(fib)) # Generator Expression squares = (x**2 for x in range(10)) print(list(squares))</code></pre>' WHERE course_id = 4 AND level_number = 10;

-- Go Level 2-10
UPDATE levels SET content = '<h3>Funktionen</h3><p>Funktionen in Go mit mehreren Rückgabewerten.</p><pre><code>package main import "fmt" func add(a, b int) int { return a + b } func divide(a, b float64) (float64, error) { if b == 0 { return 0, fmt.Errorf("division by zero") } return a / b, nil } func main() { sum := add(5, 3) result, err := divide(10, 2) if err != nil { fmt.Println("Error:", err) } else { fmt.Printf("Result: %.2f\n", result) } }</code></pre>' WHERE course_id = 5 AND level_number = 2;

UPDATE levels SET content = '<h3>Arrays und Slices</h3><p>Datenstrukturen in Go.</p><pre><code>package main import "fmt" func main() { // Array (feste Größe) var arr [5]int arr[0] = 10 arr[1] = 20 // Slice (dynamisch) slice := []int{1, 2, 3, 4, 5} slice = append(slice, 6) // Slice-Operationen fmt.Println(slice[1:4]) // [2 3 4] fmt.Println(len(slice)) // 6 fmt.Println(cap(slice)) // Kapazität }</code></pre>' WHERE course_id = 5 AND level_number = 3;

UPDATE levels SET content = '<h3>Maps</h3><p>Schlüssel-Wert-Paare in Go.</p><pre><code>package main import "fmt" func main() { // Map erstellen ages := make(map[string]int) ages["Max"] = 25 ages["Anna"] = 30 // Map literal person := map[string]string{ "name": "Max", "city": "Berlin", } // Iteration for key, value := range ages { fmt.Printf("%s: %d\n", key, value) } // Prüfen ob Key existiert if age, ok := ages["Max"]; ok { fmt.Printf("Max ist %d Jahre alt\n", age) } }</code></pre>' WHERE course_id = 5 AND level_number = 4;

UPDATE levels SET content = '<h3>Structs</h3><p>Benutzerdefinierte Datentypen in Go.</p><pre><code>package main import "fmt" type Person struct { Name string Age int City string } func (p Person) Greet() string { return fmt.Sprintf("Hallo, ich bin %s", p.Name) } func (p *Person) Birthday() { p.Age++ } func main() { person := Person{ Name: "Max", Age: 25, City: "Berlin", } fmt.Println(person.Greet()) person.Birthday() fmt.Printf("Neues Alter: %d\n", person.Age) }</code></pre>' WHERE course_id = 5 AND level_number = 5;

-- Java Level 2-10
UPDATE levels SET content = '<h3>Kontrollstrukturen</h3><p>If-Else, Switch und Schleifen in Java.</p><pre><code>public class Control { public static void main(String[] args) { int age = 18; if (age >= 18) { System.out.println("Volljährig"); } else { System.out.println("Minderjährig"); } // Switch String day = "Montag"; switch (day) { case "Montag": System.out.println("Wochenstart"); break; case "Freitag": System.out.println("Fast Wochenende"); break; default: System.out.println("Normaler Tag"); } // For-Schleife for (int i = 0; i < 5; i++) { System.out.println("Zahl: " + i); } } }</code></pre>' WHERE course_id = 6 AND level_number = 2;

UPDATE levels SET content = '<h3>Arrays</h3><p>Arrays in Java für mehrere Werte.</p><pre><code>public class Arrays { public static void main(String[] args) { // Array deklarieren int[] numbers = new int[5]; numbers[0] = 10; numbers[1] = 20; // Array mit Werten int[] scores = {95, 87, 92, 78, 85}; // Array durchlaufen for (int i = 0; i < scores.length; i++) { System.out.println("Score " + i + ": " + scores[i]); } // Enhanced for loop for (int score : scores) { System.out.println("Score: " + score); } // Mehrdimensionale Arrays int[][] matrix = {{1, 2}, {3, 4}, {5, 6}}; } }</code></pre>' WHERE course_id = 6 AND level_number = 3;
