-- 50 neue Achievements für verschiedene Programmiersprachen

-- HTML/CSS/JS Achievements (7-16)
INSERT OR IGNORE INTO achievements (id, name, description, icon, points, requirement_type, requirement_value) VALUES
(7, 'HTML Grundlagen', '3 HTML Level abgeschlossen', 'fab fa-html5', 75, 'levels_completed', 3),
(8, 'CSS Stylist', '5 CSS Level abgeschlossen', 'fab fa-css3-alt', 100, 'levels_completed', 5),
(9, 'JavaScript Anfänger', '3 JS Level abgeschlossen', 'fab fa-js-square', 75, 'levels_completed', 3),
(10, 'Frontend Entwickler', '15 Level abgeschlossen', 'fas fa-laptop-code', 300, 'levels_completed', 15),
(11, 'Responsive Design', '8 Level abgeschlossen', 'fas fa-mobile-alt', 150, 'levels_completed', 8),
(12, 'DOM Manipulator', '12 Level abgeschlossen', 'fas fa-code', 200, 'levels_completed', 12),
(13, 'Web Designer', '20 Level abgeschlossen', 'fas fa-paint-brush', 400, 'levels_completed', 20),
(14, 'Animation Meister', '25 Level abgeschlossen', 'fas fa-magic', 500, 'levels_completed', 25),
(15, 'Frontend Experte', '30 Level abgeschlossen', 'fas fa-star', 750, 'levels_completed', 30),
(16, 'HTML/CSS/JS Master', '50 Level abgeschlossen', 'fas fa-crown', 1000, 'levels_completed', 50),

-- JavaScript Vertiefung (17-26)
(17, 'ES6 Kenner', '5 JS Level abgeschlossen', 'fab fa-js', 100, 'levels_completed', 5),
(18, 'Async Programmierer', '10 JS Level abgeschlossen', 'fas fa-sync', 200, 'levels_completed', 10),
(19, 'Promise Handler', '15 JS Level abgeschlossen', 'fas fa-handshake', 300, 'levels_completed', 15),
(20, 'React Entwickler', '20 JS Level abgeschlossen', 'fab fa-react', 400, 'levels_completed', 20),
(21, 'Node.js Ninja', '25 JS Level abgeschlossen', 'fab fa-node-js', 500, 'levels_completed', 25),
(22, 'API Meister', '30 JS Level abgeschlossen', 'fas fa-plug', 600, 'levels_completed', 30),
(23, 'Framework Guru', '35 JS Level abgeschlossen', 'fas fa-layer-group', 700, 'levels_completed', 35),
(24, 'Full Stack JS', '40 JS Level abgeschlossen', 'fas fa-server', 800, 'levels_completed', 40),
(25, 'JavaScript Architekt', '45 JS Level abgeschlossen', 'fas fa-building', 900, 'levels_completed', 45),
(26, 'JS Grandmaster', '60 JS Level abgeschlossen', 'fas fa-chess-king', 1200, 'levels_completed', 60),

-- PHP Achievements (27-36)
(27, 'PHP Starter', '3 PHP Level abgeschlossen', 'fab fa-php', 75, 'levels_completed', 3),
(28, 'Web Backend', '5 PHP Level abgeschlossen', 'fas fa-database', 100, 'levels_completed', 5),
(29, 'MySQL Connector', '8 PHP Level abgeschlossen', 'fas fa-link', 150, 'levels_completed', 8),
(30, 'Laravel Lerner', '12 PHP Level abgeschlossen', 'fas fa-gem', 200, 'levels_completed', 12),
(31, 'API Builder', '15 PHP Level abgeschlossen', 'fas fa-cogs', 300, 'levels_completed', 15),
(32, 'Session Manager', '18 PHP Level abgeschlossen', 'fas fa-user-lock', 350, 'levels_completed', 18),
(33, 'Security Expert', '22 PHP Level abgeschlossen', 'fas fa-shield-alt', 450, 'levels_completed', 22),
(34, 'Framework Master', '25 PHP Level abgeschlossen', 'fas fa-tools', 500, 'levels_completed', 25),
(35, 'PHP Architekt', '30 PHP Level abgeschlossen', 'fas fa-drafting-compass', 600, 'levels_completed', 30),
(36, 'Backend Guru', '40 PHP Level abgeschlossen', 'fas fa-server', 800, 'levels_completed', 40),

-- Python Achievements (37-46)
(37, 'Python Beginner', '3 Python Level abgeschlossen', 'fab fa-python', 75, 'levels_completed', 3),
(38, 'Data Analyst', '5 Python Level abgeschlossen', 'fas fa-chart-bar', 100, 'levels_completed', 5),
(39, 'Pandas Pro', '8 Python Level abgeschlossen', 'fas fa-table', 150, 'levels_completed', 8),
(40, 'ML Enthusiast', '12 Python Level abgeschlossen', 'fas fa-brain', 200, 'levels_completed', 12),
(41, 'Django Developer', '15 Python Level abgeschlossen', 'fas fa-globe', 300, 'levels_completed', 15),
(42, 'AI Researcher', '20 Python Level abgeschlossen', 'fas fa-robot', 400, 'levels_completed', 20),
(43, 'Data Scientist', '25 Python Level abgeschlossen', 'fas fa-microscope', 500, 'levels_completed', 25),
(44, 'Deep Learning', '30 Python Level abgeschlossen', 'fas fa-network-wired', 600, 'levels_completed', 30),
(45, 'Python Master', '35 Python Level abgeschlossen', 'fas fa-snake', 700, 'levels_completed', 35),
(46, 'AI Architect', '50 Python Level abgeschlossen', 'fas fa-microchip', 1000, 'levels_completed', 50),

-- GO Achievements (47-52)
(47, 'Go Gopher', '3 Go Level abgeschlossen', 'fas fa-bolt', 75, 'levels_completed', 3),
(48, 'Concurrent Coder', '5 Go Level abgeschlossen', 'fas fa-arrows-alt', 100, 'levels_completed', 5),
(49, 'Goroutine Master', '10 Go Level abgeschlossen', 'fas fa-running', 200, 'levels_completed', 10),
(50, 'Microservice Builder', '15 Go Level abgeschlossen', 'fas fa-cubes', 300, 'levels_completed', 15),
(51, 'Cloud Native', '20 Go Level abgeschlossen', 'fas fa-cloud', 400, 'levels_completed', 20),
(52, 'Go Expert', '30 Go Level abgeschlossen', 'fas fa-rocket', 600, 'levels_completed', 30),

-- Java Achievements (53-56)
(53, 'Java Beginner', '3 Java Level abgeschlossen', 'fab fa-java', 75, 'levels_completed', 3),
(54, 'OOP Master', '8 Java Level abgeschlossen', 'fas fa-object-group', 150, 'levels_completed', 8),
(55, 'Spring Developer', '15 Java Level abgeschlossen', 'fas fa-leaf', 300, 'levels_completed', 15),
(56, 'Enterprise Architect', '25 Java Level abgeschlossen', 'fas fa-building', 500, 'levels_completed', 25);
