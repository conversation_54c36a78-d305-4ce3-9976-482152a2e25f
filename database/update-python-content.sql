-- Python Level 1-10 Content Updates
UPDATE levels SET content = '
            <h3>Python Grundlagen</h3>
            <p>Python ist eine vielseitige, leicht zu erlernende Programmiersprache mit klarer, lesbarer Syntax.</p>
            
            <h4><PERSON><PERSON><PERSON>itte:</h4>
            <pre><code># Kommentare beginnen mit #
print("Hallo Welt!")
print("Willkommen bei Python!")

# Variablen (keine Deklaration nötig)
name = "<PERSON>"
age = 25
height = 1.80
is_student = True

# Ausgabe mit verschiedenen Methoden
print("Name:", name)
print(f"Ich bin {name} und {age} Jahre alt.")  # f-strings
print("Größe: {:.1f}m".format(height))         # .format()

# Mehrere Variablen gleichzeitig
x, y, z = 1, 2, 3
print(x, y, z)</code></pre>

            <h4>Datentypen:</h4>
            <pre><code># Zahlen
integer_zahl = 42
float_zahl = 3.14
complex_zahl = 2 + 3j

# Strings
text = "Das ist ein String"
multiline = """Das ist ein
mehrzeiliger
String"""

# Listen (veränderbar)
fruits = ["Apfel", "Banane", "Orange"]
numbers = [1, 2, 3, 4, 5]
mixed = ["Text", 42, 3.14, True]

# Tupel (unveränderbar)
coordinates = (10, 20)
colors = ("rot", "grün", "blau")

# Dictionary (Schlüssel-Wert-Paare)
person = {
    "name": "Anna",
    "age": 30,
    "city": "Berlin"
}

# Set (eindeutige Werte)
unique_numbers = {1, 2, 3, 4, 5}</code></pre>

            <h4>Typ-Überprüfung:</h4>
            <pre><code># Typ einer Variable prüfen
print(type(name))        # &lt;class "str"&gt;
print(type(age))         # &lt;class "int"&gt;
print(isinstance(age, int))  # True

# Typ-Konvertierung
number_str = "123"
number_int = int(number_str)
number_float = float(number_str)

print(f"String: {number_str}, Int: {number_int}, Float: {number_float}")</code></pre>
        ' WHERE course_id = 4 AND level_number = 1;

UPDATE levels SET content = '
            <h3>Python Listen und Schleifen</h3>
            <p>Listen sind eine der wichtigsten Datenstrukturen in Python. Sie sind veränderbar und können verschiedene Datentypen enthalten.</p>
            
            <h4>Listen erstellen und bearbeiten:</h4>
            <pre><code># Listen erstellen
fruits = ["Apfel", "Banane", "Orange"]
numbers = list(range(1, 6))  # [1, 2, 3, 4, 5]
empty_list = []

# Elemente hinzufügen
fruits.append("Traube")           # Am Ende hinzufügen
fruits.insert(1, "Erdbeere")      # An Position 1 einfügen
fruits.extend(["Kiwi", "Mango"])  # Mehrere Elemente hinzufügen

print(fruits)

# Elemente entfernen
fruits.remove("Banane")    # Erstes Vorkommen entfernen
last_fruit = fruits.pop()  # Letztes Element entfernen und zurückgeben
del fruits[0]              # Element an Index 0 löschen

# Listen-Eigenschaften
print(f"Länge: {len(fruits)}")
print(f"Erstes Element: {fruits[0]}")
print(f"Letztes Element: {fruits[-1]}")
print(f"Teilbereich: {fruits[1:3]}")  # Slicing</code></pre>

            <h4>For-Schleifen:</h4>
            <pre><code># Über Liste iterieren
fruits = ["Apfel", "Banane", "Orange"]

for fruit in fruits:
    print(f"Ich mag {fruit}")

# Mit Index
for i, fruit in enumerate(fruits):
    print(f"{i}: {fruit}")

# Über Zahlenbereich
for i in range(5):
    print(f"Zahl: {i}")

# Range mit Start und Ende
for i in range(2, 8):
    print(f"Zahl: {i}")

# Range mit Schritt
for i in range(0, 10, 2):
    print(f"Gerade Zahl: {i}")

# Über Dictionary
person = {"name": "Max", "age": 25, "city": "Berlin"}

for key in person:
    print(f"{key}: {person[key]}")

# Oder direkter:
for key, value in person.items():
    print(f"{key}: {value}")</code></pre>

            <h4>List Comprehensions:</h4>
            <pre><code># Traditionelle Schleife
squares = []
for x in range(10):
    squares.append(x**2)

# List Comprehension (eleganter)
squares = [x**2 for x in range(10)]

# Mit Bedingung
even_squares = [x**2 for x in range(10) if x % 2 == 0]

# Komplexeres Beispiel
words = ["python", "java", "javascript", "go"]
long_words = [word.upper() for word in words if len(word) > 4]

print(long_words)  # ["PYTHON", "JAVASCRIPT"]</code></pre>
        ' WHERE course_id = 4 AND level_number = 2;

UPDATE levels SET content = '
            <h3>Python Funktionen</h3>
            <p>Funktionen sind wiederverwendbare Codeblöcke, die bestimmte Aufgaben erfüllen.</p>
            
            <h4>Funktionen definieren:</h4>
            <pre><code># Einfache Funktion
def greet():
    print("Hallo Welt!")

# Funktion aufrufen
greet()

# Funktion mit Parametern
def greet_user(name, age=18):
    print(f"Hallo {name}, du bist {age} Jahre alt.")

greet_user("Max")        # Verwendet Standardwert
greet_user("Anna", 25)   # Überschreibt Standardwert

# Funktion mit Rückgabewert
def add(a, b):
    return a + b

result = add(5, 3)
print(f"Ergebnis: {result}")

# Mehrere Rückgabewerte
def get_name_age():
    return "Max", 25

name, age = get_name_age()  # Tuple unpacking
print(f"Name: {name}, Alter: {age}")</code></pre>

            <h4>Erweiterte Parameter:</h4>
            <pre><code># *args für variable Anzahl von Argumenten
def sum_all(*numbers):
    return sum(numbers)

print(sum_all(1, 2, 3, 4, 5))  # 15

# **kwargs für Schlüsselwort-Argumente
def print_info(**info):
    for key, value in info.items():
        print(f"{key}: {value}")

print_info(name="Max", age=25, city="Berlin")

# Kombination
def complex_function(required, *args, **kwargs):
    print(f"Required: {required}")
    print(f"Args: {args}")
    print(f"Kwargs: {kwargs}")

complex_function("test", 1, 2, 3, name="Max", age=25)</code></pre>

            <h4>Lambda-Funktionen:</h4>
            <pre><code># Lambda (anonyme Funktionen)
square = lambda x: x**2
print(square(5))  # 25

# Mit mehreren Parametern
add = lambda a, b: a + b
print(add(3, 4))  # 7

# Verwendung mit map, filter, sorted
numbers = [1, 2, 3, 4, 5]

# map: Funktion auf alle Elemente anwenden
squared = list(map(lambda x: x**2, numbers))
print(squared)  # [1, 4, 9, 16, 25]

# filter: Elemente filtern
evens = list(filter(lambda x: x % 2 == 0, numbers))
print(evens)  # [2, 4]

# sorted: Liste sortieren
words = ["python", "java", "go", "javascript"]
sorted_by_length = sorted(words, key=lambda x: len(x))
print(sorted_by_length)  # ["go", "java", "python", "javascript"]</code></pre>
        ' WHERE course_id = 4 AND level_number = 3;
