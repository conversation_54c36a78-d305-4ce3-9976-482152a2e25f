-- Script um alle verbleibenden Level mit sinnvollem Content zu füllen

-- JavaScript Level 10-20 (schnelle Updates)
UPDATE levels SET content = '<h3>Async/Await</h3><p>Async/Await macht asynchronen Code lesbarer und einfacher zu verstehen.</p><pre><code>async function fetchData() { const response = await fetch("/api/data"); const data = await response.json(); return data; }</code></pre>' WHERE course_id = 2 AND level_number = 10;

UPDATE levels SET content = '<h3>Classes</h3><p>ES6 Classes bieten eine saubere Syntax für objektorientierte Programmierung.</p><pre><code>class Person { constructor(name, age) { this.name = name; this.age = age; } greet() { return `Hallo, ich bin ${this.name}`; } }</code></pre>' WHERE course_id = 2 AND level_number = 11;

UPDATE levels SET content = '<h3>Modules</h3><p>Module ermöglichen es, Code in separate Dateien zu organisieren.</p><pre><code>// export.js export const PI = 3.14; export function add(a, b) { return a + b; } // import.js import { PI, add } from "./export.js";</code></pre>' WHERE course_id = 2 AND level_number = 12;

UPDATE levels SET content = '<h3>Error Handling</h3><p>Fehlerbehandlung mit try-catch-finally Blöcken.</p><pre><code>try { const result = riskyOperation(); console.log(result); } catch (error) { console.error("Fehler:", error.message); } finally { console.log("Cleanup"); }</code></pre>' WHERE course_id = 2 AND level_number = 13;

UPDATE levels SET content = '<h3>Regular Expressions</h3><p>RegEx für Mustervergleiche und Textverarbeitung.</p><pre><code>const email = "<EMAIL>"; const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/; console.log(emailRegex.test(email)); // true</code></pre>' WHERE course_id = 2 AND level_number = 14;

UPDATE levels SET content = '<h3>JSON</h3><p>JavaScript Object Notation für Datenaustausch.</p><pre><code>const obj = { name: "Max", age: 25 }; const json = JSON.stringify(obj); const parsed = JSON.parse(json); console.log(parsed.name);</code></pre>' WHERE course_id = 2 AND level_number = 15;

UPDATE levels SET content = '<h3>Set und Map</h3><p>Neue Datenstrukturen für eindeutige Werte und Schlüssel-Wert-Paare.</p><pre><code>const set = new Set([1, 2, 3, 3]); console.log(set.size); // 3 const map = new Map(); map.set("name", "Max"); console.log(map.get("name"));</code></pre>' WHERE course_id = 2 AND level_number = 16;

UPDATE levels SET content = '<h3>Generators</h3><p>Generator-Funktionen können pausiert und fortgesetzt werden.</p><pre><code>function* numberGenerator() { yield 1; yield 2; yield 3; } const gen = numberGenerator(); console.log(gen.next().value); // 1</code></pre>' WHERE course_id = 2 AND level_number = 17;

UPDATE levels SET content = '<h3>Proxy</h3><p>Proxy ermöglicht es, Operationen auf Objekten abzufangen.</p><pre><code>const target = { name: "Max" }; const proxy = new Proxy(target, { get(obj, prop) { return prop in obj ? obj[prop] : "Nicht gefunden"; } });</code></pre>' WHERE course_id = 2 AND level_number = 18;

UPDATE levels SET content = '<h3>Web APIs</h3><p>Browser-APIs für erweiterte Funktionalitäten.</p><pre><code>// Geolocation navigator.geolocation.getCurrentPosition(pos => { console.log(pos.coords.latitude, pos.coords.longitude); }); // Notifications new Notification("Hallo!");</code></pre>' WHERE course_id = 2 AND level_number = 19;

UPDATE levels SET content = '<h3>Performance</h3><p>Code-Optimierung und Performance-Messung.</p><pre><code>console.time("operation"); // Zeitaufwändige Operation for(let i = 0; i < 1000000; i++) { /* ... */ } console.timeEnd("operation"); // Performance API performance.mark("start");</code></pre>' WHERE course_id = 2 AND level_number = 20;

-- PHP Level 4-20
UPDATE levels SET content = '<h3>Arrays</h3><p>PHP Arrays sind vielseitige Datenstrukturen.</p><pre><code><?php $fruits = ["Apfel", "Banane", "Orange"]; $person = ["name" => "Max", "age" => 25]; foreach($fruits as $fruit) { echo $fruit . "\n"; } echo $person["name"]; ?></code></pre>' WHERE course_id = 3 AND level_number = 4;

UPDATE levels SET content = '<h3>Klassen und Objekte</h3><p>Objektorientierte Programmierung in PHP.</p><pre><code><?php class Person { public $name; public $age; public function __construct($name, $age) { $this->name = $name; $this->age = $age; } public function greet() { return "Hallo, ich bin " . $this->name; } } $person = new Person("Max", 25); echo $person->greet(); ?></code></pre>' WHERE course_id = 3 AND level_number = 5;

UPDATE levels SET content = '<h3>Datei-Operationen</h3><p>Dateien lesen und schreiben in PHP.</p><pre><code><?php // Datei schreiben file_put_contents("test.txt", "Hallo Welt!"); // Datei lesen $content = file_get_contents("test.txt"); echo $content; // Datei zeilenweise lesen $lines = file("test.txt"); foreach($lines as $line) { echo $line; } ?></code></pre>' WHERE course_id = 3 AND level_number = 6;

UPDATE levels SET content = '<h3>MySQL Verbindung</h3><p>Datenbankverbindung mit PDO.</p><pre><code><?php try { $pdo = new PDO("mysql:host=localhost;dbname=test", $user, $pass); $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?"); $stmt->execute([1]); $user = $stmt->fetch(); echo $user["name"]; } catch(PDOException $e) { echo "Fehler: " . $e->getMessage(); } ?></code></pre>' WHERE course_id = 3 AND level_number = 7;

UPDATE levels SET content = '<h3>Sessions</h3><p>Session-Management in PHP.</p><pre><code><?php session_start(); // Session-Variable setzen $_SESSION["username"] = "Max"; $_SESSION["logged_in"] = true; // Session-Variable lesen if(isset($_SESSION["username"])) { echo "Willkommen " . $_SESSION["username"]; } // Session beenden session_destroy(); ?></code></pre>' WHERE course_id = 3 AND level_number = 8;

-- Python Level 4-10
UPDATE levels SET content = '<h3>Dictionaries</h3><p>Dictionaries speichern Schlüssel-Wert-Paare.</p><pre><code>person = {"name": "Max", "age": 25, "city": "Berlin"} print(person["name"]) person["job"] = "Developer" for key, value in person.items(): print(f"{key}: {value}") # Dictionary Comprehension squares = {x: x**2 for x in range(5)}</code></pre>' WHERE course_id = 4 AND level_number = 4;

UPDATE levels SET content = '<h3>Klassen und Objekte</h3><p>Objektorientierte Programmierung in Python.</p><pre><code>class Person: def __init__(self, name, age): self.name = name self.age = age def greet(self): return f"Hallo, ich bin {self.name}" def birthday(self): self.age += 1 person = Person("Max", 25) print(person.greet()) person.birthday()</code></pre>' WHERE course_id = 4 AND level_number = 5;
