-- Extended Levels for All Courses (Level 21-40)
-- Each course gets 20 additional levels with Boss Levels every 10 levels

-- HTML-CSS-JS Course Extended Levels (21-40)
INSERT OR IGNORE INTO levels (course_id, level_number, title, description, content, exercise_type, expected_output) VALUES
-- Advanced HTML (21-23)
(1, 21, 'HTML5 Semantic Elements', 'Moderne HTML5 Semantik verwenden', 'Verwende semantic HTML5 Elemente wie <header>, <nav>, <main>, <section>, <article>, <aside>, <footer>.', 'code_example', 'semantic'),
(1, 22, 'HTML5 Semantic Elements - Praxis', 'Semantic Elements in der Praxis', 'Erstelle eine Webseite mit mindestens 4 verschiedenen semantic HTML5 Elementen.', 'code_example', 'semantic'),
(1, 23, 'HTML5 Input Types', 'Moderne Input-Typen verwenden', 'Verwende HTML5 Input-Typen wie email, date, number, range, color.', 'code_example', 'input'),

-- Advanced CSS (24-26)
(1, 24, 'CSS Flexbox Grundlagen', 'Flexbox Layout System', 'Verwende CSS Flexbox mit display: flex, justify-content, align-items.', 'code_example', 'flex'),
(1, 25, 'CSS Flexbox - Erweitert', 'Erweiterte Flexbox Eigenschaften', 'Nutze flex-direction, flex-wrap, align-self für komplexe Layouts.', 'code_example', 'flex'),
(1, 26, 'CSS Grid Grundlagen', 'CSS Grid Layout System', 'Verwende CSS Grid mit display: grid, grid-template-columns, grid-template-rows.', 'code_example', 'grid'),

-- Advanced JavaScript (27-29)
(1, 27, 'JavaScript DOM Manipulation', 'DOM Elemente manipulieren', 'Verwende document.getElementById, querySelector, innerHTML, addEventListener.', 'code_example', 'document'),
(1, 28, 'JavaScript Events', 'Event Handling in JavaScript', 'Implementiere Event Listener für click, mouseover, keydown Events.', 'code_example', 'addEventListener'),
(1, 29, 'JavaScript Local Storage', 'Daten lokal speichern', 'Verwende localStorage.setItem, localStorage.getItem für Datenpersistierung.', 'code_example', 'localStorage'),

-- BOSS LEVEL 30
(1, 30, '🏆 BOSS: Interactive Todo App', 'Vollständige Todo-Anwendung erstellen', 'Erstelle eine interaktive Todo-App mit HTML5, CSS Flexbox/Grid und JavaScript DOM Manipulation. Features: Todos hinzufügen, löschen, als erledigt markieren, localStorage Persistierung.', 'project', 'todo-app'),

-- Responsive Design (31-33)
(1, 31, 'CSS Media Queries', 'Responsive Design Grundlagen', 'Verwende @media queries für verschiedene Bildschirmgrößen.', 'code_example', '@media'),
(1, 32, 'Mobile-First Design', 'Mobile-First Ansatz', 'Entwickle mobile-first mit min-width Media Queries.', 'code_example', 'mobile-first'),
(1, 33, 'CSS Animations', 'CSS Animationen erstellen', 'Verwende @keyframes, animation, transition für Animationen.', 'code_example', 'animation'),

-- Advanced Web Technologies (34-36)
(1, 34, 'Fetch API', 'Moderne HTTP Requests', 'Verwende fetch() API für HTTP Requests und Promise Handling.', 'code_example', 'fetch'),
(1, 35, 'JSON Handling', 'JSON Daten verarbeiten', 'Parse und stringify JSON Daten mit JavaScript.', 'code_example', 'JSON'),
(1, 36, 'Web Components', 'Custom HTML Elements', 'Erstelle Custom Elements mit customElements.define().', 'code_example', 'customElements'),

-- Modern JavaScript (37-39)
(1, 37, 'ES6 Arrow Functions', 'Moderne JavaScript Syntax', 'Verwende Arrow Functions, Template Literals, Destructuring.', 'code_example', '=>'),
(1, 38, 'Async/Await', 'Asynchrone Programmierung', 'Verwende async/await für asynchrone Operationen.', 'code_example', 'async'),
(1, 39, 'JavaScript Modules', 'Module System verwenden', 'Verwende import/export für modulare JavaScript Entwicklung.', 'code_example', 'import'),

-- BOSS LEVEL 40
(1, 40, '🏆 BOSS: Modern Web Portfolio', 'Vollständiges modernes Web-Portfolio', 'Erstelle ein responsives Portfolio mit HTML5 Semantics, CSS Grid/Flexbox, Animationen, Fetch API, ES6+ JavaScript und Web Components. Features: Responsive Design, Smooth Scrolling, Contact Form, Project Gallery.', 'project', 'portfolio');

-- JavaScript Advanced Course Extended Levels (21-40)
INSERT OR IGNORE INTO levels (course_id, level_number, title, description, content, exercise_type, expected_output) VALUES
-- Advanced Async Programming (21-23)
(2, 21, 'Promise Chaining', 'Promise Verkettung verstehen', 'Verwende Promise.then() Chaining für sequenzielle asynchrone Operationen.', 'code_example', 'then'),
(2, 22, 'Promise.all und Promise.race', 'Parallele Promise Verarbeitung', 'Verwende Promise.all() und Promise.race() für parallele Operationen.', 'code_example', 'Promise.all'),
(2, 23, 'Error Handling mit Promises', 'Promise Fehlerbehandlung', 'Implementiere .catch() und try/catch mit async/await.', 'code_example', 'catch'),

-- Advanced Functions (24-26)
(2, 24, 'Higher-Order Functions', 'Funktionen als Parameter', 'Verwende map(), filter(), reduce() und eigene Higher-Order Functions.', 'code_example', 'map'),
(2, 25, 'Function Composition', 'Funktions-Komposition', 'Kombiniere Funktionen zu komplexeren Operationen.', 'code_example', 'compose'),
(2, 26, 'Currying und Partial Application', 'Funktionale Programmierung', 'Implementiere Currying und Partial Application Patterns.', 'code_example', 'curry'),

-- Advanced OOP (27-29)
(2, 27, 'Class Inheritance', 'Erweiterte Klassen-Vererbung', 'Verwende extends, super() und method overriding.', 'code_example', 'extends'),
(2, 28, 'Static Methods und Properties', 'Statische Klassen-Member', 'Implementiere static methods und properties in Klassen.', 'code_example', 'static'),
(2, 29, 'Private Fields', 'Private Klassen-Felder', 'Verwende private fields (#) und methods in Klassen.', 'code_example', '#private'),

-- BOSS LEVEL 30
(2, 30, '🏆 BOSS: Advanced Task Manager', 'Objektorientierter Task Manager', 'Erstelle einen erweiterten Task Manager mit Klassen-Vererbung, Private Fields, Promise-basierter API, Higher-Order Functions für Filtering/Sorting. Features: Task Categories, Due Dates, Priority Levels, Data Persistence.', 'project', 'task-manager'),

-- Advanced Patterns (31-33)
(2, 31, 'Observer Pattern', 'Observer Design Pattern', 'Implementiere das Observer Pattern für Event-basierte Kommunikation.', 'code_example', 'observer'),
(2, 32, 'Module Pattern', 'Module Design Pattern', 'Verwende IIFE und Module Pattern für Code-Organisation.', 'code_example', 'module'),
(2, 33, 'Singleton Pattern', 'Singleton Design Pattern', 'Implementiere das Singleton Pattern für einmalige Instanzen.', 'code_example', 'singleton'),

-- Performance & Optimization (34-36)
(2, 34, 'Debouncing und Throttling', 'Performance Optimierung', 'Implementiere debounce() und throttle() für Event-Optimierung.', 'code_example', 'debounce'),
(2, 35, 'Memoization', 'Caching für Performance', 'Verwende Memoization für teure Berechnungen zu cachen.', 'code_example', 'memoize'),
(2, 36, 'Lazy Loading', 'Lazy Loading Patterns', 'Implementiere Lazy Loading für Module und Daten.', 'code_example', 'lazy'),

-- Advanced Browser APIs (37-39)
(2, 37, 'Intersection Observer', 'Moderne Browser API', 'Verwende Intersection Observer API für Scroll-basierte Features.', 'code_example', 'IntersectionObserver'),
(2, 38, 'Service Workers', 'Progressive Web Apps', 'Implementiere Service Workers für Offline-Funktionalität.', 'code_example', 'serviceWorker'),
(2, 39, 'WebSockets', 'Real-time Kommunikation', 'Verwende WebSockets für Real-time Datenübertragung.', 'code_example', 'WebSocket'),

-- BOSS LEVEL 40
(2, 40, '🏆 BOSS: Real-time Chat App', 'Vollständige Chat-Anwendung', 'Erstelle eine Real-time Chat App mit WebSockets, Service Workers, Design Patterns, Performance Optimierung. Features: Real-time Messaging, Offline Support, User Authentication, File Sharing, Typing Indicators.', 'project', 'chat-app');

-- PHP Course Extended Levels (21-40)
INSERT OR IGNORE INTO levels (course_id, level_number, title, description, content, exercise_type, expected_output) VALUES
-- Advanced PHP Basics (21-23)
(3, 21, 'PHP Include/Require', 'Code-Wiederverwendung', 'Verwende include, require, include_once, require_once für Code-Organisation.', 'code_example', 'include'),
(3, 22, 'PHP Constants', 'Konstanten definieren', 'Verwende define() und const für unveränderliche Werte.', 'code_example', 'define'),
(3, 23, 'PHP Magic Constants', 'Magic Constants verwenden', 'Nutze __FILE__, __DIR__, __LINE__, __FUNCTION__ für Debugging.', 'code_example', '__FILE__'),

-- Advanced Arrays & Strings (24-26)
(3, 24, 'PHP Array Functions', 'Erweiterte Array-Manipulation', 'Verwende array_map(), array_filter(), array_reduce() für Datenverarbeitung.', 'code_example', 'array_map'),
(3, 25, 'PHP String Functions', 'String-Manipulation', 'Nutze explode(), implode(), str_replace(), preg_match() für Strings.', 'code_example', 'explode'),
(3, 26, 'PHP Multidimensional Arrays', 'Mehrdimensionale Arrays', 'Arbeite mit verschachtelten Arrays und array_walk_recursive().', 'code_example', 'multidimensional'),

-- File & Directory Operations (27-29)
(3, 27, 'PHP Directory Operations', 'Verzeichnis-Operationen', 'Verwende opendir(), readdir(), scandir() für Verzeichnis-Navigation.', 'code_example', 'opendir'),
(3, 28, 'PHP File Upload', 'Datei-Upload verarbeiten', 'Implementiere sicheren File-Upload mit $_FILES und move_uploaded_file().', 'code_example', '$_FILES'),
(3, 29, 'PHP CSV/JSON Processing', 'Datenformat-Verarbeitung', 'Verarbeite CSV mit fgetcsv() und JSON mit json_decode()/json_encode().', 'code_example', 'fgetcsv'),

-- BOSS LEVEL 30
(3, 30, '🏆 BOSS: File Manager System', 'Vollständiges Datei-Management', 'Erstelle ein File Manager System mit Upload, Download, Directory Navigation, CSV/JSON Import/Export. Features: File Permissions, Image Thumbnails, Search Functionality, Bulk Operations.', 'project', 'file-manager'),

-- Advanced OOP (31-33)
(3, 31, 'PHP Abstract Classes', 'Abstrakte Klassen verwenden', 'Implementiere abstract classes und abstract methods für Template-Patterns.', 'code_example', 'abstract'),
(3, 32, 'PHP Interfaces - Erweitert', 'Erweiterte Interface-Nutzung', 'Verwende multiple Interfaces und Interface-Vererbung.', 'code_example', 'implements'),
(3, 33, 'PHP Traits', 'Code-Wiederverwendung mit Traits', 'Nutze Traits für horizontale Code-Wiederverwendung.', 'code_example', 'trait'),

-- Database Advanced (34-36)
(3, 34, 'PHP Transactions', 'Datenbank-Transaktionen', 'Verwende PDO Transaktionen mit beginTransaction(), commit(), rollback().', 'code_example', 'beginTransaction'),
(3, 35, 'PHP Database Migrations', 'Schema-Verwaltung', 'Implementiere Database Migrations für Schema-Änderungen.', 'code_example', 'migration'),
(3, 36, 'PHP Query Builder', 'Dynamische SQL-Queries', 'Erstelle einen Query Builder für dynamische SQL-Generierung.', 'code_example', 'queryBuilder'),

-- Modern PHP (37-39)
(3, 37, 'PHP 8 Features', 'Moderne PHP Syntax', 'Verwende PHP 8 Features: Union Types, Named Arguments, Match Expression.', 'code_example', 'php8'),
(3, 38, 'PHP Attributes', 'Metadata mit Attributes', 'Nutze PHP 8 Attributes für Metadata und Annotations.', 'code_example', 'attribute'),
(3, 39, 'PHP Enums', 'Enumerations verwenden', 'Verwende PHP 8.1 Enums für typsichere Konstanten.', 'code_example', 'enum'),

-- BOSS LEVEL 40
(3, 40, '🏆 BOSS: E-Commerce API', 'Vollständige E-Commerce REST API', 'Erstelle eine moderne E-Commerce REST API mit PHP 8, PDO Transactions, Abstract Classes, Traits, Query Builder. Features: Product Management, Order Processing, User Authentication, Payment Integration, API Documentation.', 'project', 'ecommerce-api');

-- Python Course Extended Levels (21-40)
INSERT OR IGNORE INTO levels (course_id, level_number, title, description, content, exercise_type, expected_output) VALUES
-- Advanced Python Basics (21-23)
(4, 21, 'Python List Comprehensions - Erweitert', 'Komplexe List Comprehensions', 'Verwende nested list comprehensions und conditional expressions.', 'code_example', 'nested_comprehension'),
(4, 22, 'Python Dictionary Comprehensions', 'Dictionary Comprehensions', 'Erstelle Dictionaries mit comprehension syntax und conditions.', 'code_example', 'dict_comprehension'),
(4, 23, 'Python Set Comprehensions', 'Set Comprehensions', 'Verwende set comprehensions für unique value collections.', 'code_example', 'set_comprehension'),

-- Advanced Functions (24-26)
(4, 24, 'Python *args und **kwargs', 'Variable Argumente', 'Verwende *args und **kwargs für flexible Funktions-Parameter.', 'code_example', '*args'),
(4, 25, 'Python Closures', 'Closures und Scope', 'Implementiere Closures und verstehe variable scope.', 'code_example', 'closure'),
(4, 26, 'Python Decorators - Erweitert', 'Erweiterte Decorator Patterns', 'Erstelle Decorators mit Parametern und Class-based Decorators.', 'code_example', 'decorator_params'),

-- Object-Oriented Programming (27-29)
(4, 27, 'Python Magic Methods', 'Dunder Methods verwenden', 'Implementiere __str__, __repr__, __len__, __getitem__ Methods.', 'code_example', '__str__'),
(4, 28, 'Python Property Decorators', 'Properties und Setters', 'Verwende @property, @setter, @deleter für controlled access.', 'code_example', '@property'),
(4, 29, 'Python Multiple Inheritance', 'Mehrfach-Vererbung', 'Implementiere multiple inheritance und Method Resolution Order (MRO).', 'code_example', 'multiple_inheritance'),

-- BOSS LEVEL 30
(4, 30, '🏆 BOSS: Data Analysis Tool', 'Vollständiges Datenanalyse-Tool', 'Erstelle ein Data Analysis Tool mit OOP, Decorators, Magic Methods, Comprehensions. Features: CSV/JSON Import, Statistical Analysis, Data Visualization, Export Functionality.', 'project', 'data-analysis'),

-- Advanced Libraries (31-33)
(4, 31, 'Python Requests - Erweitert', 'HTTP Client Programming', 'Verwende requests für APIs, Sessions, Authentication, Error Handling.', 'code_example', 'requests_advanced'),
(4, 32, 'Python BeautifulSoup', 'Web Scraping mit BeautifulSoup', 'Scrape Websites mit BeautifulSoup und CSS Selectors.', 'code_example', 'BeautifulSoup'),
(4, 33, 'Python Selenium', 'Browser Automation', 'Automatisiere Browser-Interaktionen mit Selenium WebDriver.', 'code_example', 'selenium'),

-- Data Science (34-36)
(4, 34, 'NumPy - Erweitert', 'Erweiterte NumPy Operationen', 'Verwende Broadcasting, Vectorization, Advanced Indexing.', 'code_example', 'numpy_advanced'),
(4, 35, 'Pandas - Erweitert', 'Erweiterte Pandas Operationen', 'Nutze GroupBy, Pivot Tables, Time Series Analysis.', 'code_example', 'pandas_advanced'),
(4, 36, 'Python Data Visualization', 'Erweiterte Visualisierung', 'Erstelle interaktive Plots mit Plotly und Seaborn.', 'code_example', 'plotly'),

-- Advanced Topics (37-39)
(4, 37, 'Python Asyncio', 'Asynchrone Programmierung', 'Verwende async/await, asyncio.gather(), Event Loops.', 'code_example', 'asyncio'),
(4, 38, 'Python Type Hints', 'Static Type Checking', 'Nutze Type Hints und mypy für bessere Code-Qualität.', 'code_example', 'typing'),
(4, 39, 'Python Context Managers', 'Context Managers erstellen', 'Implementiere eigene Context Managers mit __enter__ und __exit__.', 'code_example', 'context_manager'),

-- BOSS LEVEL 40
(4, 40, '🏆 BOSS: ML Web Scraper', 'Machine Learning Web Scraper', 'Erstelle einen intelligenten Web Scraper mit Selenium, BeautifulSoup, Pandas, Machine Learning. Features: Dynamic Content Scraping, Data Cleaning, Pattern Recognition, Automated Reporting.', 'project', 'ml-scraper');

-- Go Course Extended Levels (21-40)
INSERT OR IGNORE INTO levels (course_id, level_number, title, description, content, exercise_type, expected_output) VALUES
-- Advanced Go Basics (21-23)
(5, 21, 'Go Pointers', 'Pointer und Memory Management', 'Verwende Pointers (*) und Address-of Operator (&) für Memory-Effizienz.', 'code_example', 'pointer'),
(5, 22, 'Go Defer Statement', 'Defer für Cleanup', 'Nutze defer für Resource-Cleanup und Error-Handling.', 'code_example', 'defer'),
(5, 23, 'Go Panic und Recover', 'Error Recovery', 'Implementiere panic() und recover() für Exception-ähnliches Verhalten.', 'code_example', 'panic'),

-- Advanced Concurrency (24-26)
(5, 24, 'Go Worker Pools', 'Worker Pool Pattern', 'Implementiere Worker Pools für parallele Task-Verarbeitung.', 'code_example', 'worker_pool'),
(5, 25, 'Go Context Package', 'Context für Cancellation', 'Verwende context.Context für Request-Cancellation und Timeouts.', 'code_example', 'context'),
(5, 26, 'Go Sync Package', 'Synchronisation Primitives', 'Nutze sync.Mutex, sync.WaitGroup, sync.Once für Thread-Safety.', 'code_example', 'sync'),

-- Web Development (27-29)
(5, 27, 'Go HTTP Middleware', 'HTTP Middleware Pattern', 'Erstelle HTTP Middleware für Logging, Authentication, CORS.', 'code_example', 'middleware'),
(5, 28, 'Go Template Engine', 'HTML Templates', 'Verwende html/template für dynamische HTML-Generierung.', 'code_example', 'template'),
(5, 29, 'Go REST API', 'RESTful Web Services', 'Implementiere vollständige REST API mit JSON Handling.', 'code_example', 'rest_api'),

-- BOSS LEVEL 30
(5, 30, '🏆 BOSS: Concurrent Web Server', 'High-Performance Web Server', 'Erstelle einen concurrent Web Server mit Goroutines, Channels, Middleware, Templates. Features: Request Routing, Static File Serving, API Endpoints, Graceful Shutdown.', 'project', 'web-server'),

-- Advanced Go Features (31-33)
(5, 31, 'Go Reflection', 'Runtime Type Information', 'Verwende reflect Package für Runtime Type Inspection.', 'code_example', 'reflect'),
(5, 32, 'Go Build Tags', 'Conditional Compilation', 'Nutze Build Tags für plattform-spezifischen Code.', 'code_example', 'build_tags'),
(5, 33, 'Go Embedding', 'Struct Embedding', 'Implementiere Composition über Inheritance mit Embedding.', 'code_example', 'embedding'),

-- Database & Persistence (34-36)
(5, 34, 'Go Database/SQL', 'SQL Database Integration', 'Verwende database/sql Package für SQL Database Operations.', 'code_example', 'database_sql'),
(5, 35, 'Go ORM (GORM)', 'Object-Relational Mapping', 'Nutze GORM für typsichere Database Operations.', 'code_example', 'gorm'),
(5, 36, 'Go Redis Integration', 'Caching mit Redis', 'Implementiere Redis Caching für Performance-Optimierung.', 'code_example', 'redis'),

-- Microservices & Deployment (37-39)
(5, 37, 'Go gRPC', 'High-Performance RPC', 'Implementiere gRPC Services für Microservice-Kommunikation.', 'code_example', 'grpc'),
(5, 38, 'Go Docker Integration', 'Containerization', 'Erstelle optimierte Docker Images für Go Applications.', 'code_example', 'docker'),
(5, 39, 'Go Kubernetes Deployment', 'Container Orchestration', 'Deploy Go Apps auf Kubernetes mit Health Checks.', 'code_example', 'kubernetes'),

-- BOSS LEVEL 40
(5, 40, '🏆 BOSS: Microservices Platform', 'Vollständige Microservices Platform', 'Erstelle eine Microservices Platform mit gRPC, Redis, Database, Docker, Kubernetes. Features: Service Discovery, Load Balancing, Health Monitoring, Distributed Tracing.', 'project', 'microservices');

-- Java Course Extended Levels (21-40)
INSERT OR IGNORE INTO levels (course_id, level_number, title, description, content, exercise_type, expected_output) VALUES
-- Advanced Java Basics (21-23)
(6, 21, 'Java Enums', 'Enumerations verwenden', 'Erstelle Enums mit Methods, Constructors und Fields.', 'code_example', 'enum'),
(6, 22, 'Java Nested Classes', 'Inner und Static Nested Classes', 'Verwende Inner Classes, Static Nested Classes, Anonymous Classes.', 'code_example', 'nested'),
(6, 23, 'Java Packages', 'Package Organisation', 'Organisiere Code in Packages mit import Statements.', 'code_example', 'package'),

-- Advanced Collections (24-26)
(6, 24, 'Java Collections Framework', 'Erweiterte Collections', 'Nutze LinkedList, TreeSet, HashMap, TreeMap für verschiedene Use Cases.', 'code_example', 'collections'),
(6, 25, 'Java Comparator und Comparable', 'Custom Sorting', 'Implementiere Comparable Interface und Comparator für Custom Sorting.', 'code_example', 'Comparable'),
(6, 26, 'Java Optional', 'Null-Safety mit Optional', 'Verwende Optional<T> für Null-Safety und funktionale Programmierung.', 'code_example', 'Optional'),

-- Advanced OOP (27-29)
(6, 27, 'Java Abstract Classes - Erweitert', 'Erweiterte Abstraktion', 'Kombiniere Abstract Classes mit Interfaces für flexible Designs.', 'code_example', 'abstract_advanced'),
(6, 28, 'Java Design Patterns', 'Common Design Patterns', 'Implementiere Singleton, Factory, Observer Patterns.', 'code_example', 'design_patterns'),
(6, 29, 'Java Reflection', 'Runtime Class Inspection', 'Verwende Reflection API für Runtime Class Manipulation.', 'code_example', 'reflection'),

-- BOSS LEVEL 30
(6, 30, '🏆 BOSS: Library Management System', 'Vollständiges Bibliotheks-System', 'Erstelle ein Library Management System mit OOP, Collections, Design Patterns, Reflection. Features: Book Management, User Management, Lending System, Search Functionality.', 'project', 'library-system'),

-- Advanced Java Features (31-33)
(6, 31, 'Java Concurrency', 'Thread-sichere Programmierung', 'Verwende ExecutorService, Future, CompletableFuture für Concurrency.', 'code_example', 'ExecutorService'),
(6, 32, 'Java NIO', 'Non-blocking I/O', 'Nutze java.nio für High-Performance File und Network Operations.', 'code_example', 'nio'),
(6, 33, 'Java Serialization', 'Object Serialization', 'Implementiere Serializable Interface für Object Persistence.', 'code_example', 'Serializable'),

-- Spring Framework (34-36)
(6, 34, 'Spring Boot - Erweitert', 'Advanced Spring Boot', 'Erstelle REST Controllers mit Validation, Exception Handling.', 'code_example', 'SpringBoot'),
(6, 35, 'Spring Data JPA', 'Database Integration', 'Verwende Spring Data JPA für Repository Pattern und Query Methods.', 'code_example', 'SpringData'),
(6, 36, 'Spring Security', 'Authentication & Authorization', 'Implementiere Security mit JWT, Role-based Access Control.', 'code_example', 'SpringSecurity'),

-- Enterprise Java (37-39)
(6, 37, 'Java Build Tools', 'Maven und Gradle', 'Verwende Maven/Gradle für Dependency Management und Build Automation.', 'code_example', 'maven'),
(6, 38, 'Java Testing - Erweitert', 'Advanced Testing', 'Nutze Mockito, TestContainers für Integration Testing.', 'code_example', 'mockito'),
(6, 39, 'Java Performance Tuning', 'Performance Optimierung', 'Optimiere Java Applications mit Profiling und JVM Tuning.', 'code_example', 'performance'),

-- BOSS LEVEL 40
(6, 40, '🏆 BOSS: Enterprise E-Commerce', 'Enterprise E-Commerce Platform', 'Erstelle eine vollständige E-Commerce Platform mit Spring Boot, Security, JPA, Testing, Performance Optimization. Features: Product Catalog, Order Management, Payment Processing, Admin Dashboard.', 'project', 'enterprise-ecommerce');
