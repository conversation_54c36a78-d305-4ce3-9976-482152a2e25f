-- Fix exercise structure for all new levels

-- Go Level 1: Erstes Go-Programm
UPDATE levels SET 
    exercise_type = 'project',
    expected_output = 'Hallo Welt!
Willkommen bei Go!',
    content = '
            <h3>Go Grundlagen</h3>
            <p>Go ist eine moderne, kompilierte Programmiersprache von Google, die für Einfachheit und Effizienz entwickelt wurde.</p>
            
            <h4>Deine Aufgabe:</h4>
            <p>Erstelle dein erstes Go-Programm, das zwei Zeilen ausgibt.</p>
            
            <h4>Beispiel-Code:</h4>
            <pre><code>package main

import "fmt"

func main() {
    fmt.Println("Hallo Welt!")
    fmt.Println("Willkommen bei Go!")
}</code></pre>

            <h4>Was du lernen wirst:</h4>
            <ul>
                <li><code>package main</code> - Hauptpaket</li>
                <li><code>import "fmt"</code> - Format-Paket importieren</li>
                <li><code>func main()</code> - Hauptfunktion</li>
                <li><code>fmt.Println()</code> - Text ausgeben</li>
            </ul>
        '
WHERE course_id = 5 AND level_number = 1;

-- Go Level 2: Variablen
UPDATE levels SET 
    exercise_type = 'project',
    expected_output = 'Name: Max, Alter: 25',
    content = '
            <h3>Go Variablen</h3>
            <p>Lerne, wie du Variablen in Go deklarierst und verwendest.</p>
            
            <h4>Deine Aufgabe:</h4>
            <p>Erstelle Variablen für Name und Alter und gib sie aus.</p>
            
            <h4>Beispiel-Code:</h4>
            <pre><code>package main

import "fmt"

func main() {
    var name string = "Max"
    var age int = 25
    
    fmt.Printf("Name: %s, Alter: %d", name, age)
}</code></pre>

            <h4>Variablen-Deklaration:</h4>
            <ul>
                <li><code>var name string</code> - Explizite Deklaration</li>
                <li><code>name := "Max"</code> - Kurze Deklaration</li>
                <li><code>fmt.Printf()</code> - Formatierte Ausgabe</li>
            </ul>
        '
WHERE course_id = 5 AND level_number = 2;

-- Java Level 1: Erstes Java-Programm
UPDATE levels SET 
    exercise_type = 'project',
    expected_output = 'Hallo Welt!
Willkommen bei Java!',
    content = '
            <h3>Java Grundlagen</h3>
            <p>Java ist eine objektorientierte, plattformunabhängige Programmiersprache.</p>
            
            <h4>Deine Aufgabe:</h4>
            <p>Erstelle dein erstes Java-Programm mit zwei Ausgaben.</p>
            
            <h4>Beispiel-Code:</h4>
            <pre><code>public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hallo Welt!");
        System.out.println("Willkommen bei Java!");
    }
}</code></pre>

            <h4>Java-Grundlagen:</h4>
            <ul>
                <li><code>public class</code> - Öffentliche Klasse</li>
                <li><code>public static void main</code> - Hauptmethode</li>
                <li><code>System.out.println()</code> - Text ausgeben</li>
                <li>Jede Anweisung endet mit <code>;</code></li>
            </ul>
        '
WHERE course_id = 6 AND level_number = 1;

-- Python Level 1: Erstes Python-Programm
UPDATE levels SET 
    exercise_type = 'project',
    expected_output = 'Hallo Welt!
Willkommen bei Python!',
    content = '
            <h3>Python Grundlagen</h3>
            <p>Python ist eine vielseitige, leicht zu erlernende Programmiersprache mit klarer Syntax.</p>
            
            <h4>Deine Aufgabe:</h4>
            <p>Erstelle dein erstes Python-Programm mit zwei print-Anweisungen.</p>
            
            <h4>Beispiel-Code:</h4>
            <pre><code>print("Hallo Welt!")
print("Willkommen bei Python!")</code></pre>

            <h4>Python-Besonderheiten:</h4>
            <ul>
                <li>Keine geschweiften Klammern - Einrückung ist wichtig</li>
                <li><code>print()</code> - Einfache Ausgabe</li>
                <li>Keine Semikolons nötig</li>
                <li>Sehr lesbare Syntax</li>
            </ul>
        '
WHERE course_id = 4 AND level_number = 1;

-- PHP Level 1: Erstes PHP-Programm
UPDATE levels SET 
    exercise_type = 'project',
    expected_output = 'Hallo Welt!
Willkommen bei PHP!',
    content = '
            <h3>PHP Grundlagen</h3>
            <p>PHP ist eine serverseitige Programmiersprache, die speziell für die Webentwicklung entwickelt wurde.</p>
            
            <h4>Deine Aufgabe:</h4>
            <p>Erstelle dein erstes PHP-Programm mit zwei echo-Anweisungen.</p>
            
            <h4>Beispiel-Code:</h4>
            <pre><code>&lt;?php
echo "Hallo Welt!";
echo "\n";
echo "Willkommen bei PHP!";
?&gt;</code></pre>

            <h4>PHP-Grundlagen:</h4>
            <ul>
                <li><code>&lt;?php</code> - PHP-Code beginnt</li>
                <li><code>echo</code> - Text ausgeben</li>
                <li>Variablen beginnen mit <code>$</code></li>
                <li>Anweisungen enden mit <code>;</code></li>
            </ul>
        '
WHERE course_id = 3 AND level_number = 1;
