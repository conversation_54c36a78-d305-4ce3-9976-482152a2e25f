const Database = require('./db');

const db = new Database();

// HTML/CSS/JS Course Levels
const htmlCssJsLevels = [
    {
        level_number: 1,
        title: "Deine erste HTML-Seite",
        description: "Lerne die Grundstruktur einer HTML-Seite kennen",
        content: `
            <h3>Willkommen zur Webentwicklung!</h3>
            <p>HTML (HyperText Markup Language) ist die Grundlage jeder Webseite. Es definiert die Struktur und den Inhalt.</p>
            
            <h4>Grundstruktur einer HTML-Seite:</h4>
            <pre><code>&lt;!DOCTYPE html&gt;
&lt;html&gt;
&lt;head&gt;
    &lt;title&gt;Meine erste Seite&lt;/title&gt;
&lt;/head&gt;
&lt;body&gt;
    &lt;h1&gt;Hallo Welt!&lt;/h1&gt;
    &lt;p&gt;Das ist mein erster Paragraph.&lt;/p&gt;
&lt;/body&gt;
&lt;/html&gt;</code></pre>

            <h4>Wichtige HTML-Tags:</h4>
            <ul>
                <li><code>&lt;h1&gt;</code> bis <code>&lt;h6&gt;</code> - Überschriften</li>
                <li><code>&lt;p&gt;</code> - Paragraphen</li>
                <li><code>&lt;div&gt;</code> - Container-Element</li>
                <li><code>&lt;span&gt;</code> - Inline-Element</li>
            </ul>
        `,
        exercise_type: "project",
        exercise_data: "Erstelle eine HTML-Seite mit: 1) Einer <h1> Überschrift mit dem Text 'Hallo Welt!' 2) Einem <p> Paragraph mit dem Text 'Das ist mein erster Paragraph.'",
        expected_output: "Hallo Welt!\nDas ist mein erster Paragraph.",
        test_cases: JSON.stringify([
            { input: "", expected: "Hallo Welt!\nDas ist mein erster Paragraph." }
        ])
    },
    {
        level_number: 2,
        title: "HTML-Listen und Links",
        description: "Arbeite mit Listen und Verlinkungen",
        content: `
            <h3>Listen und Links in HTML</h3>
            <p>Listen helfen dabei, Inhalte zu strukturieren. Links verbinden Seiten miteinander.</p>
            
            <h4>Ungeordnete Liste:</h4>
            <pre><code>&lt;ul&gt;
    &lt;li&gt;Erstes Element&lt;/li&gt;
    &lt;li&gt;Zweites Element&lt;/li&gt;
    &lt;li&gt;Drittes Element&lt;/li&gt;
&lt;/ul&gt;</code></pre>

            <h4>Geordnete Liste:</h4>
            <pre><code>&lt;ol&gt;
    &lt;li&gt;Schritt 1&lt;/li&gt;
    &lt;li&gt;Schritt 2&lt;/li&gt;
    &lt;li&gt;Schritt 3&lt;/li&gt;
&lt;/ol&gt;</code></pre>

            <h4>Links:</h4>
            <pre><code>&lt;a href="https://example.com"&gt;Externer Link&lt;/a&gt;
&lt;a href="seite2.html"&gt;Interner Link&lt;/a&gt;</code></pre>
        `,
        exercise_type: "project",
        exercise_data: "Erstelle: 1) Eine <ul> Liste mit 3 <li> Elementen (Hobbys) 2) Einen <a> Link zu Google (href='https://google.com')",
        expected_output: "• Lesen\n• Programmieren\n• Sport\nGoogle",
        test_cases: JSON.stringify([])
    },
    {
        level_number: 3,
        title: "Bilder und Attribute",
        description: "Füge Bilder hinzu und lerne HTML-Attribute kennen",
        content: `
            <h3>Bilder und Attribute</h3>
            <p>Bilder machen Webseiten lebendig. Attribute geben HTML-Elementen zusätzliche Eigenschaften.</p>
            
            <h4>Bilder einfügen:</h4>
            <pre><code>&lt;img src="bild.jpg" alt="Beschreibung des Bildes"&gt;</code></pre>

            <h4>Wichtige Attribute:</h4>
            <ul>
                <li><code>src</code> - Quelle des Bildes</li>
                <li><code>alt</code> - Alternative Beschreibung</li>
                <li><code>width</code> und <code>height</code> - Größe</li>
                <li><code>id</code> - Eindeutige Identifikation</li>
                <li><code>class</code> - CSS-Klasse</li>
            </ul>

            <h4>Beispiel mit Attributen:</h4>
            <pre><code>&lt;img src="logo.png" alt="Firmenlogo" width="200" id="logo" class="header-image"&gt;</code></pre>
        `,
        exercise_type: "project",
        exercise_data: "Erstelle ein <img> Tag mit: 1) src='https://via.placeholder.com/200' 2) alt='Mein Lieblingsbild' 3) width='200'",
        expected_output: "Mein Lieblingsbild",
        test_cases: JSON.stringify([])
    },
    {
        level_number: 4,
        title: "Erste CSS-Styles",
        description: "Gestalte deine HTML-Seite mit CSS",
        content: `
            <h3>Einführung in CSS</h3>
            <p>CSS (Cascading Style Sheets) ist für das Aussehen deiner Webseite verantwortlich.</p>
            
            <h4>CSS einbinden:</h4>
            <pre><code>&lt;head&gt;
    &lt;style&gt;
        h1 {
            color: blue;
            font-size: 24px;
        }
        
        p {
            color: gray;
            font-family: Arial, sans-serif;
        }
    &lt;/style&gt;
&lt;/head&gt;</code></pre>

            <h4>Grundlegende CSS-Eigenschaften:</h4>
            <ul>
                <li><code>color</code> - Textfarbe</li>
                <li><code>font-size</code> - Schriftgröße</li>
                <li><code>font-family</code> - Schriftart</li>
                <li><code>background-color</code> - Hintergrundfarbe</li>
                <li><code>margin</code> - Außenabstand</li>
                <li><code>padding</code> - Innenabstand</li>
            </ul>
        `,
        exercise_type: "project",
        exercise_data: "Erstelle HTML mit <style> Tag: 1) h1 { color: blue; } 2) p { color: red; } 3) Eine <h1> mit 'Meine gestylte Seite' 4) Ein <p> mit 'Dieser Text ist rot.'",
        expected_output: "Meine gestylte Seite\nDieser Text ist rot.",
        test_cases: JSON.stringify([])
    },
    {
        level_number: 5,
        title: "CSS-Selektoren",
        description: "Lerne verschiedene Arten, HTML-Elemente auszuwählen",
        content: `
            <h3>CSS-Selektoren</h3>
            <p>Selektoren bestimmen, welche HTML-Elemente gestylt werden sollen.</p>
            
            <h4>Element-Selektor:</h4>
            <pre><code>h1 { color: red; }</code></pre>

            <h4>Klassen-Selektor:</h4>
            <pre><code>.meine-klasse { background-color: yellow; }</code></pre>

            <h4>ID-Selektor:</h4>
            <pre><code>#meine-id { font-weight: bold; }</code></pre>

            <h4>Beispiel HTML mit Klassen und IDs:</h4>
            <pre><code>&lt;h1 id="haupttitel"&gt;Titel&lt;/h1&gt;
&lt;p class="wichtig"&gt;Wichtiger Text&lt;/p&gt;
&lt;p class="normal"&gt;Normaler Text&lt;/p&gt;</code></pre>
        `,
        exercise_type: "project",
        exercise_data: "Erstelle HTML mit CSS, das verschiedene Selektoren verwendet (Element, Klasse, ID).",
        expected_output: "Haupttitel\nWichtiger Text\nNormaler Text",
        test_cases: JSON.stringify([])
    },
    {
        level_number: 6,
        title: "CSS Box Model",
        description: "Verstehe Margin, Padding und Border",
        content: `
            <h3>Das CSS Box Model</h3>
            <p>Jedes HTML-Element ist eine Box mit verschiedenen Bereichen.</p>

            <h4>Die vier Bereiche:</h4>
            <ul>
                <li><strong>Content</strong> - Der Inhalt</li>
                <li><strong>Padding</strong> - Innenabstand</li>
                <li><strong>Border</strong> - Rahmen</li>
                <li><strong>Margin</strong> - Außenabstand</li>
            </ul>

            <h4>CSS-Beispiel:</h4>
            <pre><code>.box {
    width: 200px;
    height: 100px;
    padding: 20px;
    border: 2px solid black;
    margin: 10px;
    background-color: lightblue;
}</code></pre>
        `,
        exercise_type: "project",
        exercise_data: "Erstelle eine Box mit Padding, Border und Margin.",
        expected_output: "Meine Box",
        test_cases: JSON.stringify([])
    },
    {
        level_number: 7,
        title: "Flexbox Grundlagen",
        description: "Lerne das moderne CSS Layout-System",
        content: `
            <h3>Flexbox Layout</h3>
            <p>Flexbox macht es einfach, Elemente zu positionieren und zu verteilen.</p>

            <h4>Flex Container:</h4>
            <pre><code>.container {
    display: flex;
    justify-content: center;
    align-items: center;
}</code></pre>

            <h4>Wichtige Flexbox-Eigenschaften:</h4>
            <ul>
                <li><code>display: flex</code> - Aktiviert Flexbox</li>
                <li><code>justify-content</code> - Horizontale Ausrichtung</li>
                <li><code>align-items</code> - Vertikale Ausrichtung</li>
                <li><code>flex-direction</code> - Richtung der Elemente</li>
            </ul>
        `,
        exercise_type: "project",
        exercise_data: "Erstelle ein Flexbox-Layout mit drei zentrierten Elementen.",
        expected_output: "Element 1\nElement 2\nElement 3",
        test_cases: JSON.stringify([])
    },
    {
        level_number: 8,
        title: "JavaScript Einführung",
        description: "Deine ersten JavaScript-Befehle",
        content: `
            <h3>Willkommen zu JavaScript!</h3>
            <p>JavaScript macht Webseiten interaktiv und dynamisch.</p>

            <h4>Variablen:</h4>
            <pre><code>let name = "Max";
const alter = 25;
var stadt = "Berlin";</code></pre>

            <h4>Ausgabe in der Konsole:</h4>
            <pre><code>console.log("Hallo Welt!");
console.log(name);
console.log("Ich bin " + alter + " Jahre alt.");</code></pre>

            <h4>Grundlegende Datentypen:</h4>
            <ul>
                <li><strong>String</strong> - Text in Anführungszeichen</li>
                <li><strong>Number</strong> - Zahlen</li>
                <li><strong>Boolean</strong> - true oder false</li>
            </ul>
        `,
        exercise_type: "project",
        exercise_data: "Schreibe JavaScript-Code, der 'Hallo JavaScript!' ausgibt.",
        expected_output: "Hallo JavaScript!",
        test_cases: JSON.stringify([
            { input: "", expected: "Hallo JavaScript!" }
        ])
    },
    {
        level_number: 9,
        title: "JavaScript Funktionen",
        description: "Erstelle wiederverwendbare Code-Blöcke",
        content: `
            <h3>Funktionen in JavaScript</h3>
            <p>Funktionen sind wiederverwendbare Code-Blöcke.</p>

            <h4>Funktion definieren:</h4>
            <pre><code>function grüßen(name) {
    return "Hallo " + name + "!";
}

// Funktion aufrufen
console.log(grüßen("Anna"));</code></pre>

            <h4>Arrow Functions (moderne Syntax):</h4>
            <pre><code>const addieren = (a, b) => {
    return a + b;
};

console.log(addieren(5, 3));</code></pre>
        `,
        exercise_type: "project",
        exercise_data: "Erstelle eine Funktion, die zwei Zahlen addiert und das Ergebnis ausgibt.",
        expected_output: "8",
        test_cases: JSON.stringify([
            { input: "addieren(3, 5)", expected: "8" }
        ])
    },
    {
        level_number: 10,
        title: "DOM Manipulation",
        description: "Verändere HTML-Elemente mit JavaScript",
        content: `
            <h3>Das Document Object Model (DOM)</h3>
            <p>Mit JavaScript kannst du HTML-Elemente dynamisch verändern.</p>

            <h4>Elemente auswählen:</h4>
            <pre><code>// Nach ID
const element = document.getElementById("meine-id");

// Nach Klasse
const elemente = document.getElementsByClassName("meine-klasse");

// Nach Tag
const paragraphen = document.getElementsByTagName("p");</code></pre>

            <h4>Inhalt ändern:</h4>
            <pre><code>element.innerHTML = "Neuer Inhalt";
element.textContent = "Nur Text";
element.style.color = "red";</code></pre>
        `,
        exercise_type: "project",
        exercise_data: "Erstelle HTML mit JavaScript, das den Inhalt eines Elements ändert.",
        expected_output: "Geänderter Inhalt",
        test_cases: JSON.stringify([])
    }
];

// Function to seed HTML/CSS/JS course levels
function seedHtmlCssJsLevels() {
    // Get course ID for HTML/CSS/JS
    db.getCourseBySlug('html-css-js', (err, course) => {
        if (err || !course) {
            console.error('HTML/CSS/JS course not found');
            return;
        }

        console.log('Seeding HTML/CSS/JS levels...');
        
        htmlCssJsLevels.forEach((levelData, index) => {
            const sql = `INSERT OR REPLACE INTO levels 
                (course_id, level_number, title, description, content, exercise_type, exercise_data, expected_output, test_cases) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`;
            
            db.db.run(sql, [
                course.id,
                levelData.level_number,
                levelData.title,
                levelData.description,
                levelData.content,
                levelData.exercise_type,
                levelData.exercise_data,
                levelData.expected_output,
                levelData.test_cases
            ], (err) => {
                if (err) {
                    console.error(`Error inserting level ${levelData.level_number}:`, err);
                } else {
                    console.log(`Level ${levelData.level_number} inserted successfully`);
                }
                
                // Close database connection after last level
                if (index === htmlCssJsLevels.length - 1) {
                    setTimeout(() => {
                        db.close();
                        console.log('HTML/CSS/JS levels seeded successfully!');
                    }, 100);
                }
            });
        });
    });
}

// Run if called directly
if (require.main === module) {
    seedHtmlCssJsLevels();
}

module.exports = { seedHtmlCssJsLevels };
