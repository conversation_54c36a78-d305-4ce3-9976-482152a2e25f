-- Python Level 1-20
INSERT OR IGNORE INTO levels (course_id, level_number, title, description, content, expected_output, difficulty) VALUES
(4, 1, 'Python Grundlagen', '<PERSON><PERSON>e Schritte mit Python', 'Python Basics:\n\n```python\nprint("Hallo Welt!")\nname = "Max"\nprint(f"Hallo {name}")\n```', 'print', 'easy'),
(4, 2, 'Variablen und Datentypen', 'Python Datentypen', 'Python Variablen:\n\n```python\nstring = "Text"\nnumber = 42\nboolean = True\nlist_data = [1, 2, 3]\n```', 'str', 'easy'),
(4, 3, 'Listen und Tupel', 'Datenstrukturen in Python', 'Listen und Tupel:\n\n```python\nfruits = ["Apfel", "Banane", "Orange"]\ncoordinates = (10, 20)\nprint(fruits[0])\n```', 'list', 'easy'),
(4, 4, 'Dictionaries', 'Key-Value Paare', 'Python Dictionaries:\n\n```python\nperson = {"name": "<PERSON>", "age": 25}\nprint(person["name"])\nperson["city"] = "Berlin"\n```', 'dict', 'easy'),
(4, 5, 'Kontrollstrukturen', 'If/Else und Schleifen', 'Kontrollstrukturen:\n\n```python\nif age >= 18:\n    print("Erwachsen")\n\nfor i in range(5):\n    print(i)\n```', 'if', 'easy'),
(4, 6, 'Funktionen', 'Eigene Funktionen definieren', 'Python Funktionen:\n\n```python\ndef greet(name):\n    return f"Hallo {name}"\n\nresult = greet("Max")\nprint(result)\n```', 'def', 'easy'),
(4, 7, 'List Comprehensions', 'Elegante Listen-Erstellung', 'List Comprehensions:\n\n```python\nsquares = [x**2 for x in range(10)]\neven_numbers = [x for x in range(20) if x % 2 == 0]\n```', 'comprehension', 'medium'),
(4, 8, 'Klassen und Objekte', 'OOP in Python', 'Python OOP:\n\n```python\nclass Person:\n    def __init__(self, name):\n        self.name = name\n    \n    def greet(self):\n        return f"Hallo, ich bin {self.name}"\n```', 'class', 'medium'),
(4, 9, 'Module und Packages', 'Code-Organisation', 'Python Module:\n\n```python\nimport math\nfrom datetime import datetime\n\nresult = math.sqrt(16)\nnow = datetime.now()\n```', 'import', 'medium'),
(4, 10, 'File I/O', 'Dateien lesen und schreiben', 'File Operations:\n\n```python\nwith open("data.txt", "r") as file:\n    content = file.read()\n\nwith open("output.txt", "w") as file:\n    file.write("Neuer Inhalt")\n```', 'open', 'medium'),
(4, 11, 'Exception Handling', 'Fehlerbehandlung', 'Exception Handling:\n\n```python\ntry:\n    result = 10 / 0\nexcept ZeroDivisionError:\n    print("Division durch Null!")\nfinally:\n    print("Cleanup")\n```', 'try', 'medium'),
(4, 12, 'Lambda Functions', 'Anonyme Funktionen', 'Lambda Functions:\n\n```python\nsquare = lambda x: x**2\nnumbers = [1, 2, 3, 4, 5]\nsquared = list(map(square, numbers))\n```', 'lambda', 'medium'),
(4, 13, 'Decorators', 'Funktions-Dekoratoren', 'Python Decorators:\n\n```python\ndef timer(func):\n    def wrapper(*args, **kwargs):\n        start = time.time()\n        result = func(*args, **kwargs)\n        print(f"Zeit: {time.time() - start}")\n        return result\n    return wrapper\n```', 'decorator', 'hard'),
(4, 14, 'Generators', 'Memory-effiziente Iteratoren', 'Python Generators:\n\n```python\ndef fibonacci():\n    a, b = 0, 1\n    while True:\n        yield a\n        a, b = b, a + b\n\nfib = fibonacci()\n```', 'yield', 'hard'),
(4, 15, 'NumPy Basics', 'Numerische Berechnungen', 'NumPy:\n\n```python\nimport numpy as np\n\narray = np.array([1, 2, 3, 4, 5])\nmatrix = np.array([[1, 2], [3, 4]])\nresult = np.dot(matrix, array[:2])\n```', 'numpy', 'hard'),
(4, 16, 'Pandas Grundlagen', 'Datenanalyse mit Pandas', 'Pandas:\n\n```python\nimport pandas as pd\n\ndf = pd.read_csv("data.csv")\nfiltered = df[df["age"] > 25]\ndf.groupby("category").mean()\n```', 'pandas', 'hard'),
(4, 17, 'Matplotlib', 'Datenvisualisierung', 'Matplotlib:\n\n```python\nimport matplotlib.pyplot as plt\n\nplt.plot([1, 2, 3, 4], [1, 4, 9, 16])\nplt.xlabel("X-Achse")\nplt.ylabel("Y-Achse")\nplt.show()\n```', 'matplotlib', 'hard'),
(4, 18, 'Web Scraping', 'Daten aus dem Web extrahieren', 'Web Scraping:\n\n```python\nimport requests\nfrom bs4 import BeautifulSoup\n\nresponse = requests.get("https://example.com")\nsoup = BeautifulSoup(response.text, "html.parser")\n```', 'requests', 'hard'),
(4, 19, 'Machine Learning', 'ML mit scikit-learn', 'Machine Learning:\n\n```python\nfrom sklearn.linear_model import LinearRegression\nfrom sklearn.model_selection import train_test_split\n\nmodel = LinearRegression()\nmodel.fit(X_train, y_train)\n```', 'sklearn', 'hard'),
(4, 20, 'Django Basics', 'Web-Framework Django', 'Django:\n\n```python\nfrom django.http import HttpResponse\nfrom django.shortcuts import render\n\ndef index(request):\n    return HttpResponse("Hallo Django!")\n```', 'django', 'hard');

-- Go Level 1-20
INSERT OR IGNORE INTO levels (course_id, level_number, title, description, content, expected_output, difficulty) VALUES
(5, 1, 'Go Grundlagen', 'Erste Schritte mit Go', 'Go Basics:\n\n```go\npackage main\n\nimport "fmt"\n\nfunc main() {\n    fmt.Println("Hallo Welt!")\n    name := "Max"\n    fmt.Printf("Hallo %s\\n", name)\n}\n```', 'fmt.Println', 'easy'),
(5, 2, 'Variablen und Typen', 'Go Datentypen', 'Go Variablen:\n\n```go\nvar message string = "Hallo"\nnumber := 42\nvar isActive bool = true\n\nfmt.Printf("Type: %T\\n", number)\n```', 'var', 'easy'),
(5, 3, 'Arrays und Slices', 'Datenstrukturen in Go', 'Arrays und Slices:\n\n```go\nvar arr [5]int = [5]int{1, 2, 3, 4, 5}\nslice := []string{"Go", "ist", "toll"}\nslice = append(slice, "!")\n```', 'slice', 'easy'),
(5, 4, 'Maps', 'Key-Value Strukturen', 'Go Maps:\n\n```go\nperson := map[string]int{\n    "Max":  25,\n    "Anna": 30,\n}\n\nage := person["Max"]\nperson["Tom"] = 28\n```', 'map', 'easy'),
(5, 5, 'Funktionen', 'Funktionen in Go', 'Go Funktionen:\n\n```go\nfunc add(a, b int) int {\n    return a + b\n}\n\nfunc divide(a, b float64) (float64, error) {\n    if b == 0 {\n        return 0, errors.New("division by zero")\n    }\n    return a / b, nil\n}\n```', 'func', 'easy'),
(5, 6, 'Structs', 'Benutzerdefinierte Typen', 'Go Structs:\n\n```go\ntype Person struct {\n    Name string\n    Age  int\n}\n\np := Person{Name: "Max", Age: 25}\nfmt.Println(p.Name)\n```', 'struct', 'medium'),
(5, 7, 'Methods', 'Methoden für Structs', 'Go Methods:\n\n```go\nfunc (p Person) Greet() string {\n    return fmt.Sprintf("Hallo, ich bin %s", p.Name)\n}\n\nfunc (p *Person) Birthday() {\n    p.Age++\n}\n```', 'method', 'medium'),
(5, 8, 'Interfaces', 'Polymorphismus mit Interfaces', 'Go Interfaces:\n\n```go\ntype Speaker interface {\n    Speak() string\n}\n\nfunc (p Person) Speak() string {\n    return "Hallo!"\n}\n```', 'interface', 'medium'),
(5, 9, 'Error Handling', 'Fehlerbehandlung in Go', 'Error Handling:\n\n```go\nif err != nil {\n    log.Fatal(err)\n}\n\nresult, err := someFunction()\nif err != nil {\n    return err\n}\n```', 'error', 'medium'),
(5, 10, 'Goroutines', 'Concurrency Grundlagen', 'Goroutines:\n\n```go\ngo func() {\n    fmt.Println("In Goroutine")\n}()\n\ntime.Sleep(time.Second)\n```', 'goroutine', 'medium'),
(5, 11, 'Channels', 'Kommunikation zwischen Goroutines', 'Go Channels:\n\n```go\nch := make(chan string)\n\ngo func() {\n    ch <- "Hallo"\n}()\n\nmessage := <-ch\n```', 'channel', 'hard'),
(5, 12, 'Select Statement', 'Channel-Multiplexing', 'Select Statement:\n\n```go\nselect {\ncase msg1 := <-ch1:\n    fmt.Println(msg1)\ncase msg2 := <-ch2:\n    fmt.Println(msg2)\ndefault:\n    fmt.Println("Kein Channel bereit")\n}\n```', 'select', 'hard'),
(5, 13, 'Packages', 'Code-Organisation', 'Go Packages:\n\n```go\npackage utils\n\nfunc Add(a, b int) int {\n    return a + b\n}\n\n// In main.go\nimport "myproject/utils"\n```', 'package', 'hard'),
(5, 14, 'HTTP Server', 'Web-Server erstellen', 'HTTP Server:\n\n```go\nhttp.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {\n    fmt.Fprintf(w, "Hallo Web!")\n})\n\nlog.Fatal(http.ListenAndServe(":8080", nil))\n```', 'http', 'hard'),
(5, 15, 'JSON Handling', 'JSON verarbeiten', 'JSON in Go:\n\n```go\ntype User struct {\n    Name string `json:"name"`\n    Age  int    `json:"age"`\n}\n\ndata, _ := json.Marshal(user)\njson.Unmarshal(data, &user)\n```', 'json', 'hard'),
(5, 16, 'Database', 'Datenbank-Zugriff', 'Database:\n\n```go\ndb, err := sql.Open("mysql", "user:password@/dbname")\nrows, err := db.Query("SELECT name FROM users")\ndefer rows.Close()\n```', 'database', 'hard'),
(5, 17, 'Testing', 'Unit Tests in Go', 'Go Testing:\n\n```go\nfunc TestAdd(t *testing.T) {\n    result := Add(2, 3)\n    if result != 5 {\n        t.Errorf("Expected 5, got %d", result)\n    }\n}\n```', 'testing', 'hard'),
(5, 18, 'Context', 'Context für Cancellation', 'Go Context:\n\n```go\nctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)\ndefer cancel()\n\nselect {\ncase <-ctx.Done():\n    return ctx.Err()\n}\n```', 'context', 'hard'),
(5, 19, 'Microservices', 'Service-Architektur', 'Microservices:\n\n```go\nfunc healthHandler(w http.ResponseWriter, r *http.Request) {\n    w.WriteHeader(http.StatusOK)\n    json.NewEncoder(w).Encode(map[string]string{"status": "ok"})\n}\n```', 'microservice', 'hard'),
(5, 20, 'Docker Deployment', 'Go Apps containerisieren', 'Docker:\n\n```dockerfile\nFROM golang:alpine\nWORKDIR /app\nCOPY . .\nRUN go build -o main .\nCMD ["./main"]\n```', 'docker', 'hard');

-- Java Level 1-20
INSERT OR IGNORE INTO levels (course_id, level_number, title, description, content, expected_output, difficulty) VALUES
(6, 1, 'Java Grundlagen', 'Erste Schritte mit Java', 'Java Basics:\n\n```java\npublic class HelloWorld {\n    public static void main(String[] args) {\n        System.out.println("Hallo Welt!");\n        String name = "Max";\n        System.out.println("Hallo " + name);\n    }\n}\n```', 'System.out.println', 'easy'),
(6, 2, 'Variablen und Datentypen', 'Java Datentypen', 'Java Variablen:\n\n```java\nint number = 42;\nString text = "Hallo";\nboolean isActive = true;\ndouble price = 19.99;\nchar grade = \'A\';\n```', 'int', 'easy'),
(6, 3, 'Arrays', 'Arrays in Java', 'Java Arrays:\n\n```java\nint[] numbers = {1, 2, 3, 4, 5};\nString[] names = new String[3];\nnames[0] = "Max";\nSystem.out.println(numbers.length);\n```', 'array', 'easy'),
(6, 4, 'Kontrollstrukturen', 'If/Else und Schleifen', 'Kontrollstrukturen:\n\n```java\nif (age >= 18) {\n    System.out.println("Erwachsen");\n}\n\nfor (int i = 0; i < 5; i++) {\n    System.out.println(i);\n}\n```', 'if', 'easy'),
(6, 5, 'Methoden', 'Eigene Methoden erstellen', 'Java Methoden:\n\n```java\npublic static String greet(String name) {\n    return "Hallo " + name;\n}\n\npublic static int add(int a, int b) {\n    return a + b;\n}\n```', 'public static', 'easy'),
(6, 6, 'Klassen und Objekte', 'OOP Grundlagen', 'Java OOP:\n\n```java\npublic class Person {\n    private String name;\n    private int age;\n    \n    public Person(String name, int age) {\n        this.name = name;\n        this.age = age;\n    }\n    \n    public String getName() {\n        return name;\n    }\n}\n```', 'class', 'medium'),
(6, 7, 'Vererbung', 'Klassen-Vererbung', 'Java Vererbung:\n\n```java\npublic class Student extends Person {\n    private String university;\n    \n    public Student(String name, int age, String university) {\n        super(name, age);\n        this.university = university;\n    }\n}\n```', 'extends', 'medium'),
(6, 8, 'Interfaces', 'Abstrakte Verträge', 'Java Interfaces:\n\n```java\npublic interface Drawable {\n    void draw();\n    default void print() {\n        System.out.println("Printing...");\n    }\n}\n\npublic class Circle implements Drawable {\n    public void draw() {\n        System.out.println("Drawing circle");\n    }\n}\n```', 'interface', 'medium'),
(6, 9, 'Collections', 'ArrayList und HashMap', 'Java Collections:\n\n```java\nList<String> names = new ArrayList<>();\nnames.add("Max");\nnames.add("Anna");\n\nMap<String, Integer> ages = new HashMap<>();\nages.put("Max", 25);\n```', 'ArrayList', 'medium'),
(6, 10, 'Exception Handling', 'Fehlerbehandlung', 'Exception Handling:\n\n```java\ntry {\n    int result = 10 / 0;\n} catch (ArithmeticException e) {\n    System.out.println("Division durch Null!");\n} finally {\n    System.out.println("Cleanup");\n}\n```', 'try', 'medium'),
(6, 11, 'File I/O', 'Dateien lesen und schreiben', 'File Operations:\n\n```java\ntry (BufferedReader reader = Files.newBufferedReader(Paths.get("data.txt"))) {\n    String line = reader.readLine();\n} catch (IOException e) {\n    e.printStackTrace();\n}\n```', 'Files', 'medium'),
(6, 12, 'Generics', 'Typsichere Collections', 'Java Generics:\n\n```java\npublic class Box<T> {\n    private T content;\n    \n    public void set(T content) {\n        this.content = content;\n    }\n    \n    public T get() {\n        return content;\n    }\n}\n```', 'generics', 'hard'),
(6, 13, 'Lambda Expressions', 'Funktionale Programmierung', 'Lambda Expressions:\n\n```java\nList<String> names = Arrays.asList("Max", "Anna", "Tom");\nnames.stream()\n     .filter(name -> name.length() > 3)\n     .forEach(System.out::println);\n```', 'lambda', 'hard'),
(6, 14, 'Streams API', 'Datenverarbeitung mit Streams', 'Streams API:\n\n```java\nList<Integer> numbers = Arrays.asList(1, 2, 3, 4, 5);\nint sum = numbers.stream()\n                .filter(n -> n % 2 == 0)\n                .mapToInt(Integer::intValue)\n                .sum();\n```', 'stream', 'hard'),
(6, 15, 'Multithreading', 'Parallele Programmierung', 'Multithreading:\n\n```java\nThread thread = new Thread(() -> {\n    System.out.println("In Thread");\n});\nthread.start();\n\nExecutorService executor = Executors.newFixedThreadPool(4);\n```', 'Thread', 'hard'),
(6, 16, 'Annotations', 'Metadaten für Code', 'Java Annotations:\n\n```java\n@Entity\n@Table(name = "users")\npublic class User {\n    @Id\n    @GeneratedValue\n    private Long id;\n    \n    @Column(name = "username")\n    private String username;\n}\n```', 'annotation', 'hard'),
(6, 17, 'Spring Framework', 'Dependency Injection', 'Spring Framework:\n\n```java\n@Component\npublic class UserService {\n    @Autowired\n    private UserRepository userRepository;\n    \n    public User findById(Long id) {\n        return userRepository.findById(id);\n    }\n}\n```', 'Spring', 'hard'),
(6, 18, 'JPA/Hibernate', 'Object-Relational Mapping', 'JPA/Hibernate:\n\n```java\n@Repository\npublic interface UserRepository extends JpaRepository<User, Long> {\n    List<User> findByUsername(String username);\n    \n    @Query("SELECT u FROM User u WHERE u.age > :age")\n    List<User> findUsersOlderThan(@Param("age") int age);\n}\n```', 'JPA', 'hard'),
(6, 19, 'REST APIs', 'Web Services mit Spring Boot', 'REST APIs:\n\n```java\n@RestController\n@RequestMapping("/api/users")\npublic class UserController {\n    @GetMapping("/{id}")\n    public ResponseEntity<User> getUser(@PathVariable Long id) {\n        return ResponseEntity.ok(userService.findById(id));\n    }\n}\n```', 'RestController', 'hard'),
(6, 20, 'Testing', 'Unit Tests mit JUnit', 'JUnit Testing:\n\n```java\n@Test\npublic void testUserCreation() {\n    User user = new User("Max", 25);\n    assertEquals("Max", user.getName());\n    assertEquals(25, user.getAge());\n}\n\n@MockBean\nprivate UserRepository userRepository;\n```', 'JUnit', 'hard');
