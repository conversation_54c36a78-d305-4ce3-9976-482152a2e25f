-- HTML-CSS-JS Level 11-20 Content Updates
UPDATE levels SET content = '
            <h3>CSS Grid Layout</h3>
            <p>CSS Grid ist ein mächtiges Layout-System, das dir ermöglicht, komplexe zweidimensionale Layouts zu erstellen.</p>
            
            <h4>Grid Container erstellen:</h4>
            <pre><code>.container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: auto;
    gap: 20px;
    padding: 20px;
}</code></pre>

            <h4>Grid Items positionieren:</h4>
            <pre><code>.item1 {
    grid-column: 1 / 3;  /* Spalte 1 bis 3 */
    grid-row: 1;
}

.item2 {
    grid-column: 3;
    grid-row: 1 / 3;     /* Zeile 1 bis 3 */
}</code></pre>

            <h4>Praktisches Beispiel:</h4>
            <pre><code>&lt;div class="grid-container"&gt;
    &lt;div class="header"&gt;Header&lt;/div&gt;
    &lt;div class="sidebar"&gt;Sidebar&lt;/div&gt;
    &lt;div class="content"&gt;Content&lt;/div&gt;
    &lt;div class="footer"&gt;Footer&lt;/div&gt;
&lt;/div&gt;</code></pre>
        ' WHERE course_id = 1 AND level_number = 11;

UPDATE levels SET content = '
            <h3>JavaScript Events</h3>
            <p>Events machen Webseiten interaktiv. Sie reagieren auf Benutzeraktionen wie Klicks, Tastatureingaben oder Mausbewegungen.</p>
            
            <h4>Event Listener hinzufügen:</h4>
            <pre><code>const button = document.getElementById("meinButton");

button.addEventListener("click", function() {
    alert("Button wurde geklickt!");
});

// Oder mit Arrow Function:
button.addEventListener("click", () => {
    console.log("Klick erkannt!");
});</code></pre>

            <h4>Verschiedene Event-Typen:</h4>
            <ul>
                <li><code>click</code> - Mausklick</li>
                <li><code>mouseover</code> - Maus über Element</li>
                <li><code>keydown</code> - Taste gedrückt</li>
                <li><code>submit</code> - Formular abgesendet</li>
                <li><code>load</code> - Seite geladen</li>
            </ul>

            <h4>Event-Objekt verwenden:</h4>
            <pre><code>button.addEventListener("click", function(event) {
    event.preventDefault(); // Standardverhalten verhindern
    console.log("Klick-Position:", event.clientX, event.clientY);
});</code></pre>
        ' WHERE course_id = 1 AND level_number = 12;

UPDATE levels SET content = '
            <h3>CSS Animations</h3>
            <p>CSS Animationen bringen Leben in deine Webseite. Sie können Elemente bewegen, drehen, skalieren und vieles mehr.</p>
            
            <h4>Keyframes definieren:</h4>
            <pre><code>@keyframes slideIn {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-30px);
    }
    60% {
        transform: translateY(-15px);
    }
}</code></pre>

            <h4>Animation anwenden:</h4>
            <pre><code>.animated-element {
    animation: slideIn 0.5s ease-in-out;
}

.bounce-element {
    animation: bounce 2s infinite;
}</code></pre>

            <h4>Animation-Eigenschaften:</h4>
            <ul>
                <li><code>animation-duration</code> - Dauer</li>
                <li><code>animation-timing-function</code> - Timing (ease, linear, etc.)</li>
                <li><code>animation-delay</code> - Verzögerung</li>
                <li><code>animation-iteration-count</code> - Wiederholungen</li>
            </ul>
        ' WHERE course_id = 1 AND level_number = 13;

UPDATE levels SET content = '
            <h3>JavaScript DOM Manipulation</h3>
            <p>Das DOM (Document Object Model) ermöglicht es dir, HTML-Elemente dynamisch zu erstellen, zu ändern und zu löschen.</p>
            
            <h4>Elemente erstellen und hinzufügen:</h4>
            <pre><code>// Neues Element erstellen
const newDiv = document.createElement("div");
newDiv.textContent = "Ich bin ein neues Element!";
newDiv.className = "dynamic-content";

// Element zum DOM hinzufügen
document.body.appendChild(newDiv);

// Element an bestimmter Stelle einfügen
const container = document.getElementById("container");
container.appendChild(newDiv);</code></pre>

            <h4>Elemente finden und ändern:</h4>
            <pre><code>// Element finden
const element = document.querySelector(".meine-klasse");
const elements = document.querySelectorAll("p");

// Inhalt ändern
element.textContent = "Neuer Text";
element.innerHTML = "&lt;strong&gt;Fetter Text&lt;/strong&gt;";

// Attribute ändern
element.setAttribute("data-id", "123");
element.style.color = "red";</code></pre>

            <h4>Elemente entfernen:</h4>
            <pre><code>const elementToRemove = document.getElementById("remove-me");
elementToRemove.remove(); // Moderne Methode

// Oder klassisch:
elementToRemove.parentNode.removeChild(elementToRemove);</code></pre>
        ' WHERE course_id = 1 AND level_number = 14;

UPDATE levels SET content = '
            <h3>Responsive Design</h3>
            <p>Responsive Design sorgt dafür, dass deine Webseite auf allen Geräten gut aussieht - vom Smartphone bis zum Desktop.</p>
            
            <h4>Viewport Meta-Tag:</h4>
            <pre><code>&lt;meta name="viewport" content="width=device-width, initial-scale=1.0"&gt;</code></pre>

            <h4>Media Queries:</h4>
            <pre><code>/* Mobile First Approach */
.container {
    width: 100%;
    padding: 10px;
}

/* Tablet */
@media (min-width: 768px) {
    .container {
        width: 750px;
        margin: 0 auto;
        padding: 20px;
    }
}

/* Desktop */
@media (min-width: 1024px) {
    .container {
        width: 1000px;
        padding: 30px;
    }
}

/* Large Desktop */
@media (min-width: 1200px) {
    .container {
        width: 1170px;
    }
}</code></pre>

            <h4>Flexible Bilder:</h4>
            <pre><code>img {
    max-width: 100%;
    height: auto;
}

/* Responsive Flexbox */
.flex-container {
    display: flex;
    flex-wrap: wrap;
}

.flex-item {
    flex: 1 1 300px; /* grow, shrink, basis */
}</code></pre>
        ' WHERE course_id = 1 AND level_number = 15;

UPDATE levels SET content = '
            <h3>Fetch API</h3>
            <p>Die Fetch API ermöglicht es dir, HTTP-Requests zu senden und Daten von Servern zu laden, ohne die Seite neu zu laden.</p>

            <h4>Einfacher GET Request:</h4>
            <pre><code>fetch("https://api.example.com/data")
    .then(response => response.json())
    .then(data => {
        console.log("Daten erhalten:", data);
        // Daten in der UI anzeigen
        document.getElementById("result").textContent = data.message;
    })
    .catch(error => {
        console.error("Fehler:", error);
    });</code></pre>

            <h4>POST Request mit Daten:</h4>
            <pre><code>const userData = {
    name: "Max Mustermann",
    email: "<EMAIL>"
};

fetch("https://api.example.com/users", {
    method: "POST",
    headers: {
        "Content-Type": "application/json"
    },
    body: JSON.stringify(userData)
})
.then(response => response.json())
.then(result => {
    console.log("Benutzer erstellt:", result);
});</code></pre>

            <h4>Async/Await Syntax:</h4>
            <pre><code>async function loadData() {
    try {
        const response = await fetch("https://api.example.com/data");
        const data = await response.json();
        return data;
    } catch (error) {
        console.error("Fehler beim Laden:", error);
    }
}</code></pre>
        ' WHERE course_id = 1 AND level_number = 16;

UPDATE levels SET content = '
            <h3>CSS Flexbox</h3>
            <p>Flexbox ist ein eindimensionales Layout-System, das perfekt für die Ausrichtung von Elementen in einer Reihe oder Spalte ist.</p>

            <h4>Flex Container erstellen:</h4>
            <pre><code>.flex-container {
    display: flex;
    justify-content: center;    /* Horizontale Ausrichtung */
    align-items: center;        /* Vertikale Ausrichtung */
    flex-direction: row;        /* Richtung: row, column */
    flex-wrap: wrap;            /* Umbruch erlauben */
    gap: 20px;                  /* Abstand zwischen Items */
}</code></pre>

            <h4>Flex Items steuern:</h4>
            <pre><code>.flex-item {
    flex: 1;                    /* Gleichmäßig verteilen */
    flex-grow: 1;               /* Wachsen erlauben */
    flex-shrink: 0;             /* Schrumpfen verhindern */
    flex-basis: 200px;          /* Basis-Größe */
}

.flex-item-special {
    flex: 2;                    /* Doppelt so groß */
    align-self: flex-start;     /* Individuelle Ausrichtung */
}</code></pre>

            <h4>Praktische Layouts:</h4>
            <pre><code>/* Zentriertes Layout */
.center-everything {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

/* Navigation */
.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
}</code></pre>
        ' WHERE course_id = 1 AND level_number = 17;

-- Weitere HTML-CSS-JS Level 18-20
UPDATE levels SET content = '
            <h3>Local Storage</h3>
            <p>Local Storage ermöglicht es dir, Daten im Browser des Benutzers zu speichern, die auch nach dem Schließen der Seite erhalten bleiben.</p>

            <h4>Daten speichern und laden:</h4>
            <pre><code>// Daten speichern
localStorage.setItem("username", "MaxMustermann");
localStorage.setItem("settings", JSON.stringify({
    theme: "dark",
    language: "de"
}));

// Daten laden
const username = localStorage.getItem("username");
const settings = JSON.parse(localStorage.getItem("settings"));

console.log("Benutzername:", username);
console.log("Theme:", settings.theme);</code></pre>

            <h4>Daten verwalten:</h4>
            <pre><code>// Prüfen ob Daten existieren
if (localStorage.getItem("username")) {
    console.log("Benutzer ist angemeldet");
}

// Daten löschen
localStorage.removeItem("username");

// Alle Daten löschen
localStorage.clear();

// Alle Keys durchgehen
for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    console.log(key, localStorage.getItem(key));
}</code></pre>

            <h4>Praktisches Beispiel - Theme Switcher:</h4>
            <pre><code>function toggleTheme() {
    const currentTheme = localStorage.getItem("theme") || "light";
    const newTheme = currentTheme === "light" ? "dark" : "light";

    localStorage.setItem("theme", newTheme);
    document.body.className = newTheme + "-theme";
}

// Theme beim Laden anwenden
window.addEventListener("load", () => {
    const savedTheme = localStorage.getItem("theme") || "light";
    document.body.className = savedTheme + "-theme";
});</code></pre>
        ' WHERE course_id = 1 AND level_number = 18;
