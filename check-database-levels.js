const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'database', 'learning_platform.db');

console.log('🔍 Checking database levels...\n');

const db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
        console.error('❌ Error opening database:', err.message);
        return;
    }
    console.log('✅ Connected to SQLite database');
});

// Check all courses
db.all("SELECT * FROM courses ORDER BY id", (err, courses) => {
    if (err) {
        console.error('❌ Error fetching courses:', err.message);
        return;
    }

    console.log('\n📚 COURSES:');
    courses.forEach(course => {
        console.log(`  ${course.id}. ${course.name} (${course.slug}) - ${course.total_levels} levels`);
    });

    // Check levels for each course
    courses.forEach(course => {
        console.log(`\n🎯 LEVELS FOR ${course.name.toUpperCase()}:`);
        
        db.all("SELECT * FROM levels WHERE course_id = ? ORDER BY level_number", [course.id], (err, levels) => {
            if (err) {
                console.error(`❌ Error fetching levels for ${course.name}:`, err.message);
                return;
            }

            if (levels.length === 0) {
                console.log(`  ❌ NO LEVELS FOUND for ${course.name}!`);
                return;
            }

            console.log(`  📊 Found ${levels.length} levels:`);
            
            // Show first 5 and last 5 levels
            const showLevels = levels.length <= 10 ? levels : [
                ...levels.slice(0, 5),
                { level_number: '...', title: '...', expected_output: '...' },
                ...levels.slice(-5)
            ];

            showLevels.forEach(level => {
                if (level.level_number === '...') {
                    console.log(`    ...`);
                } else {
                    const isBoss = level.level_number % 10 === 0;
                    const icon = isBoss ? '🏆' : '📝';
                    console.log(`    ${icon} Level ${level.level_number}: ${level.title} (${level.expected_output || 'no validation'})`);
                }
            });

            // Check for Boss Levels
            const bossLevels = levels.filter(l => l.level_number % 10 === 0);
            console.log(`  🏆 Boss Levels: ${bossLevels.length} (${bossLevels.map(l => l.level_number).join(', ')})`);

            // Check if we have all 40 levels
            if (levels.length === 40) {
                console.log(`  ✅ Complete: All 40 levels present`);
            } else {
                console.log(`  ⚠️  Incomplete: Only ${levels.length}/40 levels`);
            }
        });
    });

    // Check achievements
    setTimeout(() => {
        console.log('\n🏅 ACHIEVEMENTS:');
        db.all("SELECT * FROM achievements ORDER BY id", (err, achievements) => {
            if (err) {
                console.error('❌ Error fetching achievements:', err.message);
                return;
            }

            console.log(`  📊 Found ${achievements.length} achievements:`);
            achievements.forEach(achievement => {
                console.log(`    🏅 ${achievement.name} (${achievement.points} points)`);
            });

            // Close database
            db.close((err) => {
                if (err) {
                    console.error('❌ Error closing database:', err.message);
                } else {
                    console.log('\n✅ Database connection closed');
                    console.log('\n🎉 Database check complete!');
                }
            });
        });
    }, 2000); // Wait for all level queries to complete
});
