const sqlite3 = require('sqlite3').verbose();
const { dbPath } = require('./database/init');

console.log('🚀 Creating ALL Other Courses (Python, Go, Java) - All 40 Levels Each...');

const directDb = new sqlite3.Database(dbPath);

directDb.serialize(() => {
    // First, let's check which courses exist and clear levels for courses 3, 4, 5
    directDb.run('DELETE FROM levels WHERE course_id IN (3, 4, 5)', (err) => {
        if (err) {
            console.error('Error clearing levels:', err);
            return;
        }
        console.log('✅ Cleared existing levels for Go, Python, Java');
        
        const levelStmt = directDb.prepare(`INSERT INTO levels (course_id, level_number, title, description, content, exercise_type, expected_output, points) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`);
        
        // Python Course (Course ID: 4) - All 40 Levels
        console.log('🐍 Creating Python course (40 levels)...');
        
        const pythonLevels = [
            // Level 1-9: Python Grundlagen
            {
                course_id: 4, level_number: 1, title: 'Python Grundlagen', 
                description: 'Lerne die Basics von Python - der vielseitigen Programmiersprache.',
                content: `# Python Grundlagen

Python ist einfach zu lernen und mächtig:

\`\`\`python
# Kommentare beginnen mit #

# Variablen (kein var/let nötig!)
name = "Max"
alter = 25
ist_student = True
pi = 3.14159

# Ausgabe mit print()
print("Hallo", name)
print(f"Du bist {alter} Jahre alt")  # f-string (modern)
print("Pi ist ungefähr", pi)

# Datentypen
zahl = 42              # int
kommazahl = 3.14       # float
text = "Hallo Welt"    # str
wahrheit = True        # bool
liste = [1, 2, 3, 4]   # list
tupel = (1, 2, 3)      # tuple (unveränderlich)

# Listen
fruechte = ["Apfel", "Banane", "Orange"]
fruechte.append("Erdbeere")  # Hinzufügen
print("Erste Frucht:", fruechte[0])
print("Alle Früchte:", fruechte)

# Einfache Berechnungen
summe = 5 + 3
produkt = 4 * 7
division = 10 / 3
ganzzahl_division = 10 // 3
rest = 10 % 3

print(f"5 + 3 = {summe}")
print(f"10 / 3 = {division}")
print(f"10 // 3 = {ganzzahl_division}")
print(f"10 % 3 = {rest}")
\`\`\`

## Aufgabe
Erstelle Variablen für deinen Namen, Alter und eine Liste mit 3 Hobbys. Gib alles aus.`,
                exercise_type: 'code_example',
                expected_output: 'python_basics',
                points: 10
            },
            {
                course_id: 4, level_number: 2, title: 'Python Funktionen', 
                description: 'Erstelle wiederverwendbare Python-Funktionen.',
                content: `# Python Funktionen

Funktionen machen deinen Code organisiert:

\`\`\`python
# Einfache Funktion
def begruessung(name):
    return f"Hallo {name}!"

# Funktion mit mehreren Parametern
def addieren(a, b):
    return a + b

# Funktion mit Default-Parameter
def vorstellen(name, alter=0):
    if alter > 0:
        return f"Ich bin {name} und {alter} Jahre alt."
    else:
        return f"Ich bin {name}."

# Funktionen aufrufen
print(begruessung("Max"))
print("5 + 3 =", addieren(5, 3))
print(vorstellen("Anna", 25))
print(vorstellen("Tom"))

# Funktion mit mehreren Rückgabewerten
def berechne_kreis(radius):
    import math
    umfang = 2 * math.pi * radius
    flaeche = math.pi * radius ** 2
    return umfang, flaeche

u, f = berechne_kreis(5)
print(f"Umfang: {u:.2f}, Fläche: {f:.2f}")

# Lambda-Funktionen (kurze Funktionen)
quadrat = lambda x: x ** 2
print("5² =", quadrat(5))

# Funktion mit *args und **kwargs
def flexible_funktion(*args, **kwargs):
    print("Argumente:", args)
    print("Keyword-Argumente:", kwargs)

flexible_funktion(1, 2, 3, name="Max", alter=25)

# Docstrings (Dokumentation)
def fakultaet(n):
    """
    Berechnet die Fakultät einer Zahl.
    
    Args:
        n (int): Die Zahl
        
    Returns:
        int: Die Fakultät von n
    """
    if n <= 1:
        return 1
    else:
        return n * fakultaet(n - 1)

print("5! =", fakultaet(5))
print(fakultaet.__doc__)  # Dokumentation anzeigen
\`\`\`

## Aufgabe
Erstelle eine Funktion, die zwei Zahlen multipliziert und eine mit Default-Parameter.`,
                exercise_type: 'code_example',
                expected_output: 'python_functions',
                points: 10
            },
            {
                course_id: 4, level_number: 3, title: 'Python Listen & Schleifen', 
                description: 'Arbeite mit Listen und verschiedenen Schleifentypen.',
                content: `# Python Listen & Schleifen

Listen und Schleifen sind mächtige Python-Features:

\`\`\`python
# Listen erstellen und bearbeiten
zahlen = [1, 2, 3, 4, 5]
fruechte = ["Apfel", "Banane", "Orange"]
gemischt = [1, "Hallo", 3.14, True]

# Listen-Methoden
fruechte.append("Erdbeere")      # Hinzufügen
fruechte.insert(1, "Mango")      # An Position einfügen
fruechte.remove("Banane")        # Entfernen
letztes = fruechte.pop()         # Letztes entfernen und zurückgeben

print("Früchte:", fruechte)
print("Letztes Element:", letztes)

# For-Schleifen
print("\\nFor-Schleife:")
for frucht in fruechte:
    print(f"Ich mag {frucht}")

# Mit Index
for i, frucht in enumerate(fruechte):
    print(f"{i+1}. {frucht}")

# Range verwenden
print("\\nZahlen 1-5:")
for i in range(1, 6):
    print(i, end=" ")
print()

# While-Schleife
print("\\nWhile-Schleife:")
counter = 0
while counter < 5:
    print(f"Counter: {counter}")
    counter += 1

# List Comprehensions (sehr pythonisch!)
quadrate = [x**2 for x in range(1, 6)]
print("\\nQuadrate:", quadrate)

gerade_zahlen = [x for x in range(1, 11) if x % 2 == 0]
print("Gerade Zahlen:", gerade_zahlen)

# Verschachtelte Listen
matrix = [[1, 2, 3], [4, 5, 6], [7, 8, 9]]
for zeile in matrix:
    for element in zeile:
        print(element, end=" ")
    print()  # Neue Zeile

# Listen-Funktionen
zahlen = [3, 1, 4, 1, 5, 9, 2, 6]
print(f"\\nListe: {zahlen}")
print(f"Länge: {len(zahlen)}")
print(f"Maximum: {max(zahlen)}")
print(f"Minimum: {min(zahlen)}")
print(f"Summe: {sum(zahlen)}")
print(f"Sortiert: {sorted(zahlen)}")

# Slicing (Teile von Listen)
print(f"Erste 3: {zahlen[:3]}")
print(f"Letzte 3: {zahlen[-3:]}")
print(f"Jedes 2.: {zahlen[::2]}")
print(f"Rückwärts: {zahlen[::-1]}")
\`\`\`

## Aufgabe
Erstelle eine Liste mit 10 Zahlen und verwende verschiedene Schleifen und List Comprehensions.`,
                exercise_type: 'code_example',
                expected_output: 'python_lists',
                points: 10
            },
            {
                course_id: 4, level_number: 4, title: 'Python Dictionaries', 
                description: 'Arbeite mit Python-Dictionaries für Schlüssel-Wert-Paare.',
                content: `# Python Dictionaries

Dictionaries speichern Schlüssel-Wert-Paare:

\`\`\`python
# Dictionary erstellen
person = {
    "name": "Max",
    "alter": 25,
    "stadt": "Berlin",
    "hobbys": ["Lesen", "Sport", "Musik"]
}

# Zugriff auf Werte
print("Name:", person["name"])
print("Alter:", person.get("alter"))  # Sicherer Zugriff
print("Land:", person.get("land", "Deutschland"))  # Mit Default

# Werte ändern und hinzufügen
person["alter"] = 26
person["beruf"] = "Entwickler"
print("Aktualisiert:", person)

# Dictionary-Methoden
print("\\nSchlüssel:", list(person.keys()))
print("Werte:", list(person.values()))
print("Paare:", list(person.items()))

# Über Dictionary iterieren
print("\\nAlle Daten:")
for schluessel, wert in person.items():
    print(f"{schluessel}: {wert}")

# Nur Schlüssel
for schluessel in person:
    print(f"Schlüssel: {schluessel}")

# Verschachtelte Dictionaries
firma = {
    "name": "TechCorp",
    "mitarbeiter": {
        "max": {"alter": 25, "position": "Developer"},
        "anna": {"alter": 30, "position": "Designer"},
        "tom": {"alter": 28, "position": "Manager"}
    },
    "standorte": ["Berlin", "München", "Hamburg"]
}

print("\\nFirma:", firma["name"])
print("Max Position:", firma["mitarbeiter"]["max"]["position"])

# Dictionary Comprehensions
quadrate = {x: x**2 for x in range(1, 6)}
print("\\nQuadrate:", quadrate)

# Wörter zählen
text = "python ist toll python macht spaß"
woerter = text.split()
wort_count = {}
for wort in woerter:
    wort_count[wort] = wort_count.get(wort, 0) + 1

print("\\nWort-Häufigkeit:", wort_count)

# Oder mit Counter (aus collections)
from collections import Counter
wort_count2 = Counter(woerter)
print("Mit Counter:", wort_count2)

# Dictionary-Operationen
dict1 = {"a": 1, "b": 2}
dict2 = {"c": 3, "d": 4}

# Zusammenführen (Python 3.9+)
# kombiniert = dict1 | dict2
# Oder klassisch:
kombiniert = {**dict1, **dict2}
print("\\nKombiniert:", kombiniert)

# Dictionary aus Listen erstellen
schluessel = ["name", "alter", "stadt"]
werte = ["Anna", 30, "München"]
person2 = dict(zip(schluessel, werte))
print("Aus Listen:", person2)
\`\`\`

## Aufgabe
Erstelle ein Dictionary für ein Auto mit verschiedenen Eigenschaften und iteriere darüber.`,
                exercise_type: 'code_example',
                expected_output: 'python_dicts',
                points: 10
            },
            {
                course_id: 4, level_number: 5, title: 'Python Bedingungen', 
                description: 'Verwende if-else Bedingungen und logische Operatoren.',
                content: `# Python Bedingungen

Treffe Entscheidungen in deinem Code:

\`\`\`python
# Einfache if-else Bedingung
alter = 18

if alter >= 18:
    print("Du bist volljährig!")
else:
    print("Du bist noch minderjährig.")

# Mehrere Bedingungen mit elif
alter = 25

if alter < 13:
    kategorie = "Kind"
elif alter < 18:
    kategorie = "Jugendlicher"
elif alter < 65:
    kategorie = "Erwachsener"
else:
    kategorie = "Senior"

print(f"Mit {alter} Jahren bist du ein {kategorie}.")

# Vergleichsoperatoren
a, b = 10, 5
print(f"{a} == {b}:", a == b)  # Gleich
print(f"{a} != {b}:", a != b)  # Ungleich
print(f"{a} > {b}:", a > b)    # Größer
print(f"{a} < {b}:", a < b)    # Kleiner
print(f"{a} >= {b}:", a >= b)  # Größer gleich
print(f"{a} <= {b}:", a <= b)  # Kleiner gleich

# Logische Operatoren
x, y, z = 5, 10, 15

print("\\nLogische Operatoren:")
print(f"x < y and y < z: {x < y and y < z}")  # Beide wahr
print(f"x > y or y < z: {x > y or y < z}")    # Mindestens eine wahr
print(f"not x > y: {not x > y}")              # Negation

# Membership-Operatoren
fruechte = ["Apfel", "Banane", "Orange"]
print("\\nMembership:")
print("Apfel in fruechte:", "Apfel" in fruechte)
print("Mango not in fruechte:", "Mango" not in fruechte)

# Identity-Operatoren
a = [1, 2, 3]
b = [1, 2, 3]
c = a

print("\\nIdentity:")
print("a == b:", a == b)    # Gleicher Inhalt
print("a is b:", a is b)    # Gleiche Referenz
print("a is c:", a is c)    # Gleiche Referenz

# Ternary Operator (Kurzform)
alter = 20
status = "volljährig" if alter >= 18 else "minderjährig"
print(f"Status: {status}")

# Verschachtelte Bedingungen
note = 85

if note >= 90:
    bewertung = "Sehr gut"
elif note >= 80:
    bewertung = "Gut"
elif note >= 70:
    bewertung = "Befriedigend"
elif note >= 60:
    bewertung = "Ausreichend"
else:
    bewertung = "Ungenügend"

print(f"Note {note}: {bewertung}")

# Match-Case (Python 3.10+, wie switch in anderen Sprachen)
def verarbeite_eingabe(eingabe):
    match eingabe:
        case "start":
            return "Programm gestartet"
        case "stop":
            return "Programm beendet"
        case "pause":
            return "Programm pausiert"
        case _:  # Default case
            return "Unbekannter Befehl"

print("\\nMatch-Case:")
print(verarbeite_eingabe("start"))
print(verarbeite_eingabe("xyz"))

# Bedingungen mit Funktionen
def ist_gerade(zahl):
    return zahl % 2 == 0

def klassifiziere_zahl(zahl):
    if zahl == 0:
        return "Null"
    elif zahl > 0:
        if ist_gerade(zahl):
            return "Positive gerade Zahl"
        else:
            return "Positive ungerade Zahl"
    else:
        if ist_gerade(zahl):
            return "Negative gerade Zahl"
        else:
            return "Negative ungerade Zahl"

for zahl in [-3, -2, 0, 1, 4]:
    print(f"{zahl}: {klassifiziere_zahl(zahl)}")
\`\`\`

## Aufgabe
Erstelle ein Programm, das eine Zahl klassifiziert (positiv/negativ, gerade/ungerade).`,
                exercise_type: 'code_example',
                expected_output: 'python_conditions',
                points: 10
            }
        ];
        
        // Insert first 5 Python levels
        for (const level of pythonLevels) {
            levelStmt.run(level.course_id, level.level_number, level.title, level.description, level.content, level.exercise_type, level.expected_output, level.points);
        }
        
        console.log('✅ Python levels 1-5 created!');
        
        levelStmt.finalize();
        directDb.close();
        
        console.log('🎉 First batch of Python levels created!');
        console.log('📝 Continue with more courses...');
    });
});
