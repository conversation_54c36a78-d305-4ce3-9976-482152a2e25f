# HTTPS Setup for CodeWave

## Quick Start

When you start the server with `node index.js`, you'll be prompted:

```
🚀 CodeWave Server Configuration
================================
Do you want to start the server with HTTPS? (Y/n):
```

### Option 1: HTTP (Default)
- Type `n` or just press Enter
- Server starts on `http://localhost:3000`
- No certificates required
- Good for development

### Option 2: HTTPS
- Type `Y` or `y`
- Server starts on `https://localhost:3443`
- Requires SSL certificates in `./certs/` directory
- Secure encrypted connection

## Setting Up HTTPS

### 1. Production Setup with Let's Encrypt (Recommended)

The application automatically detects and uses Let's Encrypt certificates for `lxnd.cloud`:

**Certificate Paths:**
- Private Key: `/etc/letsencrypt/live/lxnd.cloud/privkey.pem`
- Certificate: `/etc/letsencrypt/live/lxnd.cloud/fullchain.pem`

**Requirements:**
- Certificates must exist at the above paths
- Application must have read access to the certificates
- Run with appropriate permissions: `sudo node index.js`

### 2. Development Setup with Self-Signed Certificates

For local development, generate self-signed certificates:

```bash
./generate-certs.sh
```

This creates:
- `certs/private-key.pem` - Private key
- `certs/certificate.pem` - Self-signed certificate

### 3. Manual Certificate Generation

If the script doesn't work, generate manually:

```bash
# Create certs directory
mkdir -p certs

# Generate private key and certificate
openssl req -x509 -newkey rsa:2048 -keyout certs/private-key.pem -out certs/certificate.pem -days 365 -nodes -subj "/C=DE/ST=State/L=City/O=CodeWave/OU=Development/CN=localhost"

# Set permissions
chmod 600 certs/private-key.pem
chmod 644 certs/certificate.pem
```

## Certificate Priority

The application uses certificates in this order:

1. **Let's Encrypt (Production)**: `/etc/letsencrypt/live/lxnd.cloud/`
   - Automatically uses port 443
   - Trusted certificates, no browser warnings
   - Requires root permissions

2. **Local Development**: `./certs/`
   - Uses port 3443 by default
   - Self-signed certificates, browser warnings expected
   - No root permissions required

## Port Configuration

### Automatic Port Selection
- **Let's Encrypt**: Defaults to port 443 (standard HTTPS)
- **Local certificates**: Defaults to port 3443 (development)

### Manual Port Override
```bash
# Force specific port
HTTPS_PORT=8443 node index.js

# Production with Let's Encrypt on port 443
sudo node index.js
```

## Browser Security Warning

When using self-signed certificates, browsers will show a security warning:

1. **Chrome/Edge**: Click "Advanced" → "Proceed to localhost (unsafe)"
2. **Firefox**: Click "Advanced" → "Accept the Risk and Continue"
3. **Safari**: Click "Show Details" → "visit this website"

This is normal for self-signed certificates and safe for development.

## Production Setup

### Let's Encrypt Integration (lxnd.cloud)

The application is pre-configured for `lxnd.cloud` with Let's Encrypt:

```bash
# The application automatically detects these paths:
/etc/letsencrypt/live/lxnd.cloud/privkey.pem
/etc/letsencrypt/live/lxnd.cloud/fullchain.pem

# Start with Let's Encrypt certificates
sudo node index.js
# Choose 'Y' for HTTPS
```

### Setting Up Let's Encrypt (New Domain)

For other domains, set up Let's Encrypt:

```bash
# Install certbot
sudo apt-get install certbot

# Generate certificate for your domain
sudo certbot certonly --standalone -d yourdomain.com

# Update the certificate paths in index.js:
const letsencryptKeyPath = '/etc/letsencrypt/live/yourdomain.com/privkey.pem';
const letsencryptCertPath = '/etc/letsencrypt/live/yourdomain.com/fullchain.pem';
```

### Commercial CA
1. Purchase SSL certificate from provider
2. Download private key and certificate files
3. Update paths in `index.js` or place in `certs/` directory

## Environment Variables

- `HTTPS_PORT`: Set HTTPS port (default: 3443)
- `PORT`: Set HTTP port (default: 3000)

Examples:
```bash
# HTTPS on port 8443
HTTPS_PORT=8443 node index.js

# HTTP on port 8080
PORT=8080 node index.js

# Both custom ports
PORT=8080 HTTPS_PORT=8443 node index.js
```

## Troubleshooting

### Let's Encrypt Certificate Issues

**Check if certificates exist:**
```bash
sudo ls -la /etc/letsencrypt/live/lxnd.cloud/
```

**Check file permissions:**
```bash
sudo chmod 644 /etc/letsencrypt/live/lxnd.cloud/*.pem
```

**Verify certificate validity:**
```bash
sudo openssl x509 -in /etc/letsencrypt/live/lxnd.cloud/fullchain.pem -text -noout
```

**Renew certificates:**
```bash
sudo certbot renew --dry-run
sudo certbot renew
```

**Permission issues:**
```bash
# Run as root for Let's Encrypt certificates
sudo node index.js

# Or copy certificates to application directory
sudo cp /etc/letsencrypt/live/lxnd.cloud/privkey.pem ./certs/private-key.pem
sudo cp /etc/letsencrypt/live/lxnd.cloud/fullchain.pem ./certs/certificate.pem
sudo chown $USER:$USER ./certs/*.pem
```

### Local Certificate Errors
- Ensure files exist: `certs/private-key.pem` and `certs/certificate.pem`
- Check file permissions: `ls -la certs/`
- Regenerate certificates: `./generate-certs.sh`

### Port Permission Errors
- Port 443 requires root: `sudo node index.js`
- Use alternative port: `HTTPS_PORT=3443 node index.js`

### Browser Connection Issues
- Accept security warning for self-signed certificates
- Clear browser cache and cookies
- Try incognito/private browsing mode
- For Let's Encrypt: No browser warnings expected

## Security Notes

⚠️ **Development Only**: Self-signed certificates are for development only
🔒 **Production**: Use trusted CA certificates in production
🔑 **Private Keys**: Never commit private keys to version control
📁 **Permissions**: Set appropriate file permissions (600 for private key)

## File Structure

```
certs/
├── README.md              # Certificate documentation
├── private-key.pem        # Private key (600 permissions)
└── certificate.pem        # Certificate (644 permissions)
```
