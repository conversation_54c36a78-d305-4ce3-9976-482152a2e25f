/**
 * Completely Rebuilt Achievement System
 * Fixes all tracking and awarding issues
 */

class AchievementSystem {
    constructor(database) {
        this.db = database;
        this.achievementTypes = {
            LEVELS_COMPLETED: 'levels_completed',
            TOTAL_SCORE: 'total_score', 
            COURSE_COMPLETED: 'course_completed',
            BOSS_LEVELS: 'boss_levels_completed',
            STREAK: 'daily_streak',
            PERFECT_SCORES: 'perfect_scores'
        };
    }

    /**
     * Initialize the achievement system with clean schema
     */
    async initializeSystem() {
        console.log('🏆 Initializing Achievement System...');
        
        return new Promise((resolve, reject) => {
            this.db.db.serialize(() => {
                // Clean up existing broken data
                this.db.db.run('DELETE FROM user_achievements WHERE achievement_id NOT IN (SELECT id FROM achievements)');
                
                // Standardize achievement table schema
                this.db.db.run(`
                    CREATE TABLE IF NOT EXISTS achievements_new (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name VARCHAR(100) NOT NULL,
                        description TEXT NOT NULL,
                        icon VARCHAR(50) NOT NULL,
                        points INTEGER DEFAULT 0,
                        requirement_type VARCHAR(50) NOT NULL,
                        requirement_value INTEGER NOT NULL,
                        language VARCHAR(20) DEFAULT 'general',
                        badge_color VARCHAR(7) DEFAULT '#28a745',
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                `);
                
                // Copy valid data to new table
                this.db.db.run(`
                    INSERT INTO achievements_new (name, description, icon, points, requirement_type, requirement_value, language, badge_color)
                    SELECT 
                        name, 
                        description, 
                        COALESCE(icon, '🏆') as icon,
                        COALESCE(points, points_required, 0) as points,
                        COALESCE(requirement_type, 'levels_completed') as requirement_type,
                        COALESCE(requirement_value, points_required, 1) as requirement_value,
                        COALESCE(language, 'general') as language,
                        COALESCE(badge_color, '#28a745') as badge_color
                    FROM achievements
                    WHERE name IS NOT NULL AND description IS NOT NULL
                `);
                
                // Replace old table
                this.db.db.run('DROP TABLE achievements');
                this.db.db.run('ALTER TABLE achievements_new RENAME TO achievements');
                
                // Create default achievements
                this.createDefaultAchievements(() => {
                    console.log('✅ Achievement System Initialized');
                    resolve();
                });
            });
        });
    }

    /**
     * Create comprehensive default achievements
     */
    createDefaultAchievements(callback) {
        const achievements = [
            // General Progress Achievements
            { name: 'Erste Schritte', description: 'Schließe dein erstes Level ab', icon: '🚀', points: 50, type: 'levels_completed', value: 1, language: 'general', color: '#28a745' },
            { name: 'Lernender', description: 'Schließe 5 Level ab', icon: '📚', points: 100, type: 'levels_completed', value: 5, language: 'general', color: '#17a2b8' },
            { name: 'Fortgeschrittener', description: 'Schließe 10 Level ab', icon: '🎯', points: 200, type: 'levels_completed', value: 10, language: 'general', color: '#ffc107' },
            { name: 'Experte', description: 'Schließe 20 Level ab', icon: '⭐', points: 400, type: 'levels_completed', value: 20, language: 'general', color: '#fd7e14' },
            { name: 'Meister', description: 'Schließe 40 Level ab', icon: '👑', points: 800, type: 'levels_completed', value: 40, language: 'general', color: '#6f42c1' },
            
            // Score-based Achievements
            { name: 'Punktesammler', description: 'Erreiche 1000 Punkte', icon: '💰', points: 100, type: 'total_score', value: 1000, language: 'general', color: '#28a745' },
            { name: 'Punktejäger', description: 'Erreiche 5000 Punkte', icon: '💎', points: 250, type: 'total_score', value: 5000, language: 'general', color: '#17a2b8' },
            { name: 'Punktekönig', description: 'Erreiche 10000 Punkte', icon: '👑', points: 500, type: 'total_score', value: 10000, language: 'general', color: '#ffd700' },
            
            // Course-specific Achievements
            { name: 'HTML Meister', description: 'Schließe den HTML/CSS/JS Kurs ab', icon: '🌐', points: 500, type: 'course_completed', value: 1, language: 'html-css-js', color: '#e34c26' },
            { name: 'JavaScript Ninja', description: 'Schließe den JavaScript Advanced Kurs ab', icon: '⚡', points: 500, type: 'course_completed', value: 1, language: 'javascript', color: '#f7df1e' },
            { name: 'PHP Entwickler', description: 'Schließe den PHP Kurs ab', icon: '🐘', points: 500, type: 'course_completed', value: 1, language: 'php', color: '#777bb4' },
            { name: 'Python Schlangen-Beschwörer', description: 'Schließe den Python Kurs ab', icon: '🐍', points: 500, type: 'course_completed', value: 1, language: 'python', color: '#3776ab' },
            { name: 'Go Gopher', description: 'Schließe den Go Kurs ab', icon: '🦫', points: 500, type: 'course_completed', value: 1, language: 'go', color: '#00add8' },
            { name: 'Java Barista', description: 'Schließe den Java Kurs ab', icon: '☕', points: 500, type: 'course_completed', value: 1, language: 'java', color: '#ed8b00' },
            
            // Boss Level Achievements
            { name: 'Boss Killer', description: 'Besiege dein erstes Boss Level', icon: '⚔️', points: 200, type: 'boss_levels_completed', value: 1, language: 'general', color: '#dc3545' },
            { name: 'Boss Slayer', description: 'Besiege 3 Boss Level', icon: '🗡️', points: 400, type: 'boss_levels_completed', value: 3, language: 'general', color: '#dc3545' },
            { name: 'Boss Master', description: 'Besiege alle Boss Level', icon: '🏆', points: 800, type: 'boss_levels_completed', value: 6, language: 'general', color: '#dc3545' },
            
            // Perfect Score Achievements
            { name: 'Perfektionist', description: 'Erreiche 5 perfekte Scores (100%)', icon: '💯', points: 300, type: 'perfect_scores', value: 5, language: 'general', color: '#28a745' },
            { name: 'Perfektion Meister', description: 'Erreiche 20 perfekte Scores (100%)', icon: '🎯', points: 600, type: 'perfect_scores', value: 20, language: 'general', color: '#28a745' }
        ];

        // Clear existing achievements
        this.db.db.run('DELETE FROM achievements', (err) => {
            if (err) {
                console.error('Error clearing achievements:', err);
                return callback(err);
            }

            const stmt = this.db.db.prepare(`
                INSERT INTO achievements (name, description, icon, points, requirement_type, requirement_value, language, badge_color) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `);

            let completed = 0;
            achievements.forEach(achievement => {
                stmt.run([
                    achievement.name,
                    achievement.description, 
                    achievement.icon,
                    achievement.points,
                    achievement.type,
                    achievement.value,
                    achievement.language,
                    achievement.color
                ], (err) => {
                    completed++;
                    if (completed === achievements.length) {
                        stmt.finalize();
                        console.log(`✅ Created ${achievements.length} achievements`);
                        callback();
                    }
                });
            });
        });
    }

    /**
     * FIXED: Comprehensive achievement checking and awarding
     */
    async checkAndAwardAchievements(userId) {
        return new Promise((resolve, reject) => {
            console.log(`🔍 Checking achievements for user ${userId}`);
            
            // Get user's current statistics
            this.getUserComprehensiveStats(userId, (err, stats) => {
                if (err) {
                    console.error('Error getting user stats:', err);
                    return reject(err);
                }

                console.log('📊 User stats:', stats);

                // Get all achievements
                this.db.getAllAchievements((err, allAchievements) => {
                    if (err) {
                        console.error('Error getting achievements:', err);
                        return reject(err);
                    }

                    // Get user's current achievements
                    this.db.getUserAchievements(userId, (err, userAchievements) => {
                        if (err) {
                            console.error('Error getting user achievements:', err);
                            return reject(err);
                        }

                        const earnedAchievementIds = new Set(userAchievements.map(ua => ua.id));
                        const newAchievements = [];

                        // Check each achievement
                        allAchievements.forEach(achievement => {
                            if (earnedAchievementIds.has(achievement.id)) {
                                return; // Already earned
                            }

                            const shouldAward = this.shouldAwardAchievement(achievement, stats);
                            
                            if (shouldAward) {
                                console.log(`🏆 New achievement earned: ${achievement.name}`);
                                newAchievements.push(achievement);
                            }
                        });

                        // Award new achievements
                        if (newAchievements.length === 0) {
                            return resolve([]);
                        }

                        this.awardAchievements(userId, newAchievements, (err) => {
                            if (err) {
                                console.error('Error awarding achievements:', err);
                                return reject(err);
                            }
                            
                            resolve(newAchievements);
                        });
                    });
                });
            });
        });
    }

    /**
     * FIXED: Determine if an achievement should be awarded
     */
    shouldAwardAchievement(achievement, stats) {
        const type = achievement.requirement_type;
        const value = achievement.requirement_value;
        const language = achievement.language;

        console.log(`   Checking ${achievement.name}: ${type} >= ${value} (${language})`);

        switch (type) {
            case this.achievementTypes.LEVELS_COMPLETED:
                if (language === 'general') {
                    return stats.total_completed_levels >= value;
                } else {
                    const langStats = stats.language_stats[language];
                    return langStats && langStats.completed_levels >= value;
                }

            case this.achievementTypes.TOTAL_SCORE:
                if (language === 'general') {
                    return stats.total_score >= value;
                } else {
                    const langStats = stats.language_stats[language];
                    return langStats && langStats.total_score >= value;
                }

            case this.achievementTypes.COURSE_COMPLETED:
                if (language === 'general') {
                    return stats.completed_courses >= value;
                } else {
                    const langStats = stats.language_stats[language];
                    return langStats && langStats.course_completed;
                }

            case this.achievementTypes.BOSS_LEVELS:
                return stats.boss_levels_completed >= value;

            case this.achievementTypes.PERFECT_SCORES:
                return stats.perfect_scores >= value;

            default:
                console.warn(`Unknown achievement type: ${type}`);
                return false;
        }
    }

    /**
     * FIXED: Get comprehensive user statistics for achievement checking
     */
    getUserComprehensiveStats(userId, callback) {
        const stats = {
            total_completed_levels: 0,
            total_score: 0,
            completed_courses: 0,
            boss_levels_completed: 0,
            perfect_scores: 0,
            language_stats: {}
        };

        // Get basic stats
        this.db.getUserTotalStats(userId, (err, basicStats) => {
            if (err) {
                return callback(err);
            }

            stats.total_completed_levels = basicStats?.total_completed_levels || 0;
            stats.total_score = basicStats?.total_score || 0;
            stats.completed_courses = basicStats?.completed_courses || 0;

            // Get language-specific stats
            this.db.getUserLanguageStats(userId, (err, languageStats) => {
                if (err) {
                    return callback(err);
                }

                stats.language_stats = languageStats || {};

                // Get boss levels completed
                this.getBossLevelsCompleted(userId, (err, bossCount) => {
                    if (err) {
                        return callback(err);
                    }

                    stats.boss_levels_completed = bossCount;

                    // Get perfect scores count
                    this.getPerfectScoresCount(userId, (err, perfectCount) => {
                        if (err) {
                            return callback(err);
                        }

                        stats.perfect_scores = perfectCount;

                        // Get course completion status for each language
                        this.getCourseCompletionStatus(userId, (err, courseStatus) => {
                            if (err) {
                                return callback(err);
                            }

                            // Add course completion status to language stats
                            Object.keys(courseStatus).forEach(language => {
                                if (!stats.language_stats[language]) {
                                    stats.language_stats[language] = { completed_levels: 0, total_score: 0 };
                                }
                                stats.language_stats[language].course_completed = courseStatus[language];
                            });

                            callback(null, stats);
                        });
                    });
                });
            });
        });
    }

    /**
     * Get count of boss levels completed (levels 10, 20, 30, 40)
     */
    getBossLevelsCompleted(userId, callback) {
        const sql = `
            SELECT COUNT(*) as boss_count
            FROM user_progress up
            JOIN levels l ON up.level_id = l.id
            WHERE up.user_id = ? AND up.completed = 1
            AND l.level_number IN (10, 20, 30, 40)
        `;

        this.db.db.get(sql, [userId], (err, result) => {
            callback(err, result ? result.boss_count : 0);
        });
    }

    /**
     * Get count of perfect scores (100%)
     */
    getPerfectScoresCount(userId, callback) {
        const sql = `
            SELECT COUNT(*) as perfect_count
            FROM user_progress
            WHERE user_id = ? AND score = 100 AND completed = 1
        `;

        this.db.db.get(sql, [userId], (err, result) => {
            callback(err, result ? result.perfect_count : 0);
        });
    }

    /**
     * Get course completion status for each language
     */
    getCourseCompletionStatus(userId, callback) {
        const sql = `
            SELECT
                c.slug as course_slug,
                COUNT(l.id) as total_levels,
                COUNT(CASE WHEN up.completed = 1 THEN 1 END) as completed_levels,
                CASE WHEN COUNT(l.id) = COUNT(CASE WHEN up.completed = 1 THEN 1 END) THEN 1 ELSE 0 END as is_completed
            FROM courses c
            JOIN levels l ON c.id = l.course_id
            LEFT JOIN user_progress up ON l.id = up.level_id AND up.user_id = ?
            GROUP BY c.id, c.slug
        `;

        this.db.db.all(sql, [userId], (err, results) => {
            if (err) {
                return callback(err);
            }

            const courseStatus = {};
            results.forEach(row => {
                // Map course slugs to achievement languages
                let language = row.course_slug;
                if (language === 'javascript-advanced') {
                    language = 'javascript';
                }

                courseStatus[language] = row.is_completed === 1;
            });

            callback(null, courseStatus);
        });
    }

    /**
     * Award multiple achievements to a user
     */
    awardAchievements(userId, achievements, callback) {
        if (achievements.length === 0) {
            return callback(null);
        }

        let completed = 0;
        let hasError = false;

        achievements.forEach(achievement => {
            this.db.awardAchievement(userId, achievement.id, (err) => {
                if (err && !hasError) {
                    hasError = true;
                    return callback(err);
                }

                completed++;
                if (completed === achievements.length && !hasError) {
                    callback(null);
                }
            });
        });
    }

    /**
     * Get user's achievement progress for display
     */
    getUserAchievementProgress(userId, callback) {
        this.getUserComprehensiveStats(userId, (err, stats) => {
            if (err) {
                return callback(err);
            }

            this.db.getAllAchievements((err, allAchievements) => {
                if (err) {
                    return callback(err);
                }

                this.db.getUserAchievements(userId, (err, userAchievements) => {
                    if (err) {
                        return callback(err);
                    }

                    const earnedMap = {};
                    userAchievements.forEach(ua => {
                        earnedMap[ua.id] = ua;
                    });

                    // Add progress information to each achievement
                    const achievementsWithProgress = allAchievements.map(achievement => {
                        const earned = !!earnedMap[achievement.id];
                        const progress = this.getAchievementProgress(achievement, stats);

                        return {
                            ...achievement,
                            earned,
                            earned_at: earnedMap[achievement.id]?.earned_at,
                            progress: progress.current,
                            progress_max: progress.max,
                            progress_percentage: Math.min(100, Math.round((progress.current / progress.max) * 100))
                        };
                    });

                    callback(null, achievementsWithProgress);
                });
            });
        });
    }

    /**
     * Get current progress towards an achievement
     */
    getAchievementProgress(achievement, stats) {
        const type = achievement.requirement_type;
        const value = achievement.requirement_value;
        const language = achievement.language;

        let current = 0;

        switch (type) {
            case this.achievementTypes.LEVELS_COMPLETED:
                if (language === 'general') {
                    current = stats.total_completed_levels;
                } else {
                    const langStats = stats.language_stats[language];
                    current = langStats ? langStats.completed_levels : 0;
                }
                break;

            case this.achievementTypes.TOTAL_SCORE:
                if (language === 'general') {
                    current = stats.total_score;
                } else {
                    const langStats = stats.language_stats[language];
                    current = langStats ? langStats.total_score : 0;
                }
                break;

            case this.achievementTypes.COURSE_COMPLETED:
                current = language === 'general' ? stats.completed_courses :
                         (stats.language_stats[language]?.course_completed ? 1 : 0);
                break;

            case this.achievementTypes.BOSS_LEVELS:
                current = stats.boss_levels_completed;
                break;

            case this.achievementTypes.PERFECT_SCORES:
                current = stats.perfect_scores;
                break;
        }

        return {
            current: Math.min(current, value),
            max: value
        };
    }
}

module.exports = AchievementSystem;
