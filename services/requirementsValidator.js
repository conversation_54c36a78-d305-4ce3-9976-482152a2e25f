/**
 * Requirements-Based Validation System
 * Implements flexible validation with mandatory, optional, and alternative requirements
 */

class RequirementsValidator {
    constructor(database) {
        this.db = database;
        this.requirementPatterns = new Map();
        this.loadRequirementPatterns();
    }

    /**
     * Load requirement patterns from database
     */
    async loadRequirementPatterns() {
        return new Promise((resolve, reject) => {
            this.db.db.all('SELECT * FROM validation_requirements', (err, requirements) => {
                if (err) {
                    console.error('Error loading requirement patterns:', err);
                    reject(err);
                    return;
                }

                requirements.forEach(req => {
                    this.requirementPatterns.set(req.requirement_key, {
                        name: req.requirement_name,
                        description: req.description,
                        pattern: new RegExp(req.detection_pattern, 'gi'),
                        type: req.requirement_type,
                        language: req.language,
                        difficulty: req.difficulty_level
                    });
                });

                console.log(`📋 Loaded ${requirements.length} requirement patterns`);
                resolve();
            });
        });
    }

    /**
     * Validate code against requirements specification
     */
    async validateRequirements(code, requirements, language = 'javascript') {
        if (!requirements) {
            return this.createDefaultValidation(code, language);
        }

        let requirementsObj;
        try {
            requirementsObj = typeof requirements === 'string' ? JSON.parse(requirements) : requirements;
        } catch (error) {
            console.error('Invalid requirements JSON:', error);
            return this.createDefaultValidation(code, language);
        }

        const {
            mandatory = [],
            optional = [],
            alternatives = [],
            scoring = {
                mandatory_weight: 70,
                optional_weight: 20,
                alternatives_weight: 10
            }
        } = requirementsObj;

        // Check mandatory requirements
        const mandatoryResults = this.checkRequirements(code, mandatory, language);
        const mandatoryScore = this.calculateMandatoryScore(mandatoryResults, scoring.mandatory_weight);

        // Check optional requirements
        const optionalResults = this.checkRequirements(code, optional, language);
        const optionalScore = this.calculateOptionalScore(optionalResults, scoring.optional_weight);

        // Check alternative requirements
        const alternativeResults = this.checkAlternatives(code, alternatives, language);
        const alternativeScore = this.calculateAlternativeScore(alternativeResults, scoring.alternatives_weight);

        // Calculate total score
        const totalScore = Math.min(100, mandatoryScore + optionalScore + alternativeScore);
        const passed = mandatoryScore >= (scoring.mandatory_weight * 0.5) && totalScore >= 60;

        // Generate feedback
        const feedback = this.generateFeedback(mandatoryResults, optionalResults, alternativeResults, passed);

        return {
            passed,
            score: Math.round(totalScore),
            message: feedback.message,
            details: {
                mandatory: mandatoryResults,
                optional: optionalResults,
                alternatives: alternativeResults,
                breakdown: {
                    mandatory_score: Math.round(mandatoryScore),
                    optional_score: Math.round(optionalScore),
                    alternative_score: Math.round(alternativeScore)
                }
            },
            suggestions: feedback.suggestions,
            errors: feedback.errors
        };
    }

    /**
     * Check individual requirements against code
     */
    checkRequirements(code, requirements, language) {
        return requirements.map(reqKey => {
            const requirement = this.requirementPatterns.get(reqKey);
            if (!requirement) {
                console.warn(`Unknown requirement: ${reqKey}`);
                return { key: reqKey, met: false, name: reqKey, description: 'Unknown requirement' };
            }

            // Skip if language doesn't match
            if (requirement.language !== language && requirement.language !== 'general') {
                return { key: reqKey, met: false, name: requirement.name, description: requirement.description, reason: 'Language mismatch' };
            }

            const matches = code.match(requirement.pattern);
            return {
                key: reqKey,
                met: !!matches,
                name: requirement.name,
                description: requirement.description,
                matches: matches ? matches.length : 0,
                examples: matches ? matches.slice(0, 3) : []
            };
        });
    }

    /**
     * Check alternative requirements (at least one from each group must be met)
     */
    checkAlternatives(code, alternatives, language) {
        return alternatives.map(alternativeGroup => {
            const results = this.checkRequirements(code, alternativeGroup, language);
            const metCount = results.filter(r => r.met).length;
            
            return {
                group: alternativeGroup,
                met: metCount > 0,
                results: results,
                metCount: metCount,
                totalCount: results.length
            };
        });
    }

    /**
     * Calculate mandatory score (must meet at least 50% to pass)
     */
    calculateMandatoryScore(mandatoryResults, weight) {
        if (mandatoryResults.length === 0) return weight;
        
        const metCount = mandatoryResults.filter(r => r.met).length;
        const percentage = metCount / mandatoryResults.length;
        
        return percentage * weight;
    }

    /**
     * Calculate optional score (bonus points)
     */
    calculateOptionalScore(optionalResults, weight) {
        if (optionalResults.length === 0) return 0;
        
        const metCount = optionalResults.filter(r => r.met).length;
        const percentage = metCount / optionalResults.length;
        
        return percentage * weight;
    }

    /**
     * Calculate alternative score (bonus for using alternatives)
     */
    calculateAlternativeScore(alternativeResults, weight) {
        if (alternativeResults.length === 0) return 0;
        
        const metCount = alternativeResults.filter(a => a.met).length;
        const percentage = metCount / alternativeResults.length;
        
        return percentage * weight;
    }

    /**
     * Generate comprehensive feedback
     */
    generateFeedback(mandatoryResults, optionalResults, alternativeResults, passed) {
        const errors = [];
        const suggestions = [];
        let message = '';

        // Check mandatory requirements
        const unmetMandatory = mandatoryResults.filter(r => !r.met);
        if (unmetMandatory.length > 0) {
            errors.push(`Missing mandatory requirements: ${unmetMandatory.map(r => r.name).join(', ')}`);
            unmetMandatory.forEach(req => {
                suggestions.push(`Add ${req.name}: ${req.description}`);
            });
        }

        // Check optional requirements
        const unmetOptional = optionalResults.filter(r => !r.met);
        if (unmetOptional.length > 0 && passed) {
            suggestions.push(`Consider adding: ${unmetOptional.map(r => r.name).join(', ')} for bonus points`);
        }

        // Check alternatives
        const unmetAlternatives = alternativeResults.filter(a => !a.met);
        if (unmetAlternatives.length > 0) {
            unmetAlternatives.forEach(alt => {
                const options = alt.results.map(r => r.name).join(' OR ');
                suggestions.push(`Use one of: ${options}`);
            });
        }

        // Generate main message
        if (passed) {
            const metMandatory = mandatoryResults.filter(r => r.met).length;
            const metOptional = optionalResults.filter(r => r.met).length;
            
            message = `Excellent! You met ${metMandatory}/${mandatoryResults.length} mandatory requirements`;
            if (metOptional > 0) {
                message += ` and ${metOptional}/${optionalResults.length} optional requirements`;
            }
            message += '.';
        } else {
            message = `Code needs improvement. Missing ${unmetMandatory.length} mandatory requirements.`;
        }

        return { message, errors, suggestions };
    }

    /**
     * Create default validation for levels without requirements
     */
    createDefaultValidation(code, language) {
        const hasContent = code && code.trim().length > 10;
        const score = hasContent ? 80 : 0;
        
        return {
            passed: hasContent,
            score: score,
            message: hasContent ? 'Code executed successfully!' : 'Code is too short or empty',
            details: {
                mandatory: [],
                optional: [],
                alternatives: [],
                breakdown: { mandatory_score: score, optional_score: 0, alternative_score: 0 }
            },
            suggestions: hasContent ? [] : ['Write more substantial code to complete this level'],
            errors: hasContent ? [] : ['Code appears to be empty or too short']
        };
    }

    /**
     * Get requirement pattern by key
     */
    getRequirement(key) {
        return this.requirementPatterns.get(key);
    }

    /**
     * List all available requirements for a language
     */
    getRequirementsForLanguage(language) {
        const requirements = [];
        for (const [key, req] of this.requirementPatterns) {
            if (req.language === language || req.language === 'general') {
                requirements.push({ key, ...req });
            }
        }
        return requirements;
    }
}

module.exports = RequirementsValidator;
