    /**
 * Multi-Language Code Validation Service
 * Provides comprehensive code validation with actual syntax checking, compilation,
 * and execution testing for all supported programming languages.
 *
 * Supported Languages: HTML, CSS, JavaScript, PHP, Python, Go, Java
 * Validation Rule: ANY syntax error, logical error, or non-functional code = FAIL
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

class CodeValidator {
    constructor() {
        this.htmlParser = require('node-html-parser');
        this.tempDir = path.join(os.tmpdir(), 'codewave-validation');
        this.ensureTempDir();
    }

    ensureTempDir() {
        if (!fs.existsSync(this.tempDir)) {
            fs.mkdirSync(this.tempDir, { recursive: true });
        }
    }

    /**
     * Main validation method that routes to specific validators based on course slug
     */
    async validateCode(code, level, courseSlug = 'html-css-js') {
        if (!code || !code.trim()) {
            return {
                passed: false,
                score: 0,
                message: 'Code cannot be empty!',
                errors: ['Empty code submission'],
                suggestions: ['Please write some code to solve the exercise']
            };
        }

        const levelNumber = level.level_number;
        const expectedOutput = level?.expected_output || '';

        console.log(`🔍 Validating code for course: ${courseSlug}, level: ${levelNumber}, expected: ${expectedOutput}`);

        try {
            // Improved language detection based on course slug and expected output
            switch (courseSlug.toLowerCase()) {
                case 'html-css-js':
                    return await this.validateWebCode(code, levelNumber, level);
                case 'javascript-advanced':
                case 'javascript':
                    return await this.validateJavaScript(code, levelNumber, level);
                case 'php':
                    return await this.validatePHP(code, levelNumber, level);
                case 'python':
                    return await this.validatePython(code, levelNumber, level);
                case 'go':
                    return await this.validateGo(code, levelNumber, level);
                case 'java':
                    return await this.validateJava(code, levelNumber, level);
                default:
                    return this.validateGeneric(code, levelNumber, level);
            }
        } catch (error) {
            console.error(`❌ Validation error for ${courseSlug}:`, error);
            return {
                passed: false,
                score: 0,
                message: 'Code validation error: ' + error.message,
                errors: [error.message],
                suggestions: ['Please check your code syntax and try again']
            };
        }
    }

    /**
     * Web Code Validation (HTML/CSS/JavaScript combined)
     * Improved detection logic to prevent false positives
     */
    async validateWebCode(code, levelNumber, level) {
        const errors = [];
        const suggestions = [];
        let score = 0;
        let passed = false;
        const expectedOutput = level?.expected_output || '';

        console.log(`🌐 Web code validation - Level ${levelNumber}, Expected: ${expectedOutput}`);

        // Improved code type detection based on expected output and content analysis
        const codeType = this.detectWebCodeType(code, expectedOutput, level);

        console.log(`🔍 Detected code type: ${codeType}`);

        switch (codeType) {
            case 'html':
                return await this.validateHTML(code, levelNumber, level);
            case 'css':
                return await this.validateCSS(code, levelNumber, level);
            case 'javascript':
                return await this.validateJavaScript(code, levelNumber, level);
            default:
                // Fallback to generic validation
                return this.validateGeneric(code, levelNumber, level);
        }
    }

    /**
     * Improved code type detection for web technologies
     */
    detectWebCodeType(code, expectedOutput, level) {
        // First check expected output for explicit hints
        if (expectedOutput) {
            if (expectedOutput.includes('html') || expectedOutput.includes('structure') ||
                expectedOutput.includes('h1_h2_p') || expectedOutput.includes('a_img') ||
                expectedOutput.includes('ul_li_table') || expectedOutput.includes('nav_footer')) {
                return 'html';
            }
            if (expectedOutput.includes('css') || expectedOutput.includes('flexbox') ||
                expectedOutput.includes('grid') || expectedOutput.includes('colors') ||
                expectedOutput.includes('box_model')) {
                return 'css';
            }
            if (expectedOutput.includes('js_') || expectedOutput.includes('javascript')) {
                return 'javascript';
            }
        }

        // Improved content-based detection
        const trimmedCode = code.trim();

        // Check for HTML - must have proper HTML tags, not just angle brackets
        const hasValidHTMLTags = /<(html|head|body|div|p|h[1-6]|a|img|ul|ol|li|table|tr|td|nav|footer|header|main|section|article|aside|form|input|button|span|strong|em|br|hr)\b[^>]*>/i.test(code);
        const hasHTMLStructure = /<\/[a-zA-Z]+>/.test(code) || /<!DOCTYPE/i.test(code);

        // Check for CSS - must have proper CSS syntax
        const hasCSSRules = /[a-zA-Z-]+\s*:\s*[^;]+;/.test(code) && /[^{}]*\{[^{}]*\}/.test(code);
        const hasCSSSyntax = /@media|@keyframes|\.[\w-]+\s*\{|#[\w-]+\s*\{|[a-zA-Z]+\s*\{/.test(code);

        // Check for JavaScript - must have JS syntax, not Java
        const hasJSSyntax = /\b(let|const|var|function|=>|console\.log|document\.|window\.|alert|prompt)\b/.test(code);
        const hasJavaKeywords = /\b(public|private|protected|class|static|void|int|String|boolean|double)\s/.test(code);

        // Decision logic with priority
        if (hasValidHTMLTags && hasHTMLStructure) {
            return 'html';
        }

        if (hasCSSRules || hasCSSSyntax) {
            return 'css';
        }

        if (hasJSSyntax && !hasJavaKeywords) {
            return 'javascript';
        }

        // Default fallback based on level patterns
        if (level?.level_number <= 10) {
            return 'html'; // Early levels are typically HTML
        }

        return 'javascript'; // Later levels default to JavaScript
    }

    /**
     * Enhanced HTML validation with actual DOM parsing and structure checking
     */
    async validateHTML(code, levelNumber, level) {
        const errors = [];
        const suggestions = [];
        let score = 0;
        let passed = false;

        // Parse HTML to check for syntax errors
        try {
            const root = this.htmlParser.parse(code, {
                lowerCaseTagName: false,
                comment: false,
                blockTextElements: {
                    script: true,
                    noscript: true,
                    style: true,
                    pre: true
                }
            });

            // Level-specific validation with requirements checking
            // First check if this is a complete HTML document
            const hasDoctype = code.toLowerCase().includes('<!doctype');
            const hasHtml = code.includes('<html>') || code.includes('<html ');
            const hasHead = code.includes('<head>');
            const hasBody = code.includes('<body>');
            const isCompleteDocument = hasDoctype && hasHtml && hasHead && hasBody;

            // Use level data to determine requirements
            const levelTitle = level?.title || '';
            const levelDescription = level?.description || '';
            const expectedOutput = level?.expected_output || '';

            console.log(`HTML Validation - Level ${levelNumber}: ${levelTitle}`);
            console.log(`Expected output: ${expectedOutput}`);
            console.log(`Description: ${levelDescription}`);

            // Improved validation based on expected_output and level content
            switch (expectedOutput) {
                case 'html_structure':
                    // Level 1: HTML Grundstruktur - Check for basic HTML elements
                    const h1Elements = root.querySelectorAll('h1');
                    const pElements = root.querySelectorAll('p');

                    // More lenient validation - focus on learning, not perfection
                    if (h1Elements.length > 0 && pElements.length > 0) {
                        passed = true;
                        score = 100;
                    } else if (h1Elements.length > 0 || pElements.length > 0) {
                        passed = true;
                        score = 75;
                        if (h1Elements.length === 0) {
                            suggestions.push('Great start! Try adding a <h1> heading element for the main title');
                        }
                        if (pElements.length === 0) {
                            suggestions.push('Good work! Consider adding a <p> paragraph element for text content');
                        }
                    } else {
                        // Check if there are any valid HTML elements at all
                        const anyElements = root.querySelectorAll('*');
                        if (anyElements.length > 0) {
                            passed = true;
                            score = 50;
                            suggestions.push('You have HTML elements! Try adding <h1> for headings and <p> for paragraphs');
                        } else {
                            passed = false;
                            errors.push('No valid HTML elements found');
                            suggestions.push('Add HTML elements like <h1>Your Title</h1> and <p>Your paragraph</p>');
                            score = 20;
                        }
                    }
                    break;

                case 'h1_h2_p':
                    // Level 2: Texte & Überschriften
                    const h1Els = root.querySelectorAll('h1');
                    const h2Els = root.querySelectorAll('h2');
                    const pEls = root.querySelectorAll('p');

                    if (h1Els.length > 0 && h2Els.length > 0 && pEls.length >= 2) {
                        passed = true;
                        score = 100;
                    } else if (h1Els.length > 0 && (h2Els.length > 0 || pEls.length > 0)) {
                        passed = true;
                        score = 80;
                    } else {
                        passed = false;
                        if (h1Els.length === 0) errors.push('Missing <h1> element');
                        if (h2Els.length === 0) errors.push('Missing <h2> element');
                        if (pEls.length < 2) errors.push('Need at least 2 <p> elements');
                        suggestions.push('Add h1, h2 headings and multiple paragraphs');
                        score = Math.max(20, (h1Els.length + h2Els.length + pEls.length) * 20);
                    }
                    break;

                case 'a_img':
                    // Level 3: Links & Bilder
                    const aElements = root.querySelectorAll('a');
                    const imgElements = root.querySelectorAll('img');

                    if (aElements.length > 0 && imgElements.length > 0) {
                        passed = true;
                        score = 100;
                    } else if (aElements.length > 0 || imgElements.length > 0) {
                        passed = true;
                        score = 85;
                        if (imgElements.length === 0) {
                            suggestions.push('Great! You have a link. Consider adding an image with <img src="..." alt="..."> for full points');
                        } else {
                            suggestions.push('Great! You have an image. Consider adding a table with <table><tr><td>...</td></tr></table> for full points');
                        }
                    } else {
                        passed = false;
                        errors.push('Missing <a> link or <img> image elements');
                        suggestions.push('Add a link using <a href="...">text</a> and an image using <img src="..." alt="...">');
                        score = 20;
                    }
                    break;

                case 'ul_li_table':
                    // Level 4: Listen & Tabellen
                    const ulElements = root.querySelectorAll('ul');
                    const liElements = root.querySelectorAll('li');
                    const tableElements = root.querySelectorAll('table');

                    if (ulElements.length > 0 && liElements.length >= 3 && tableElements.length > 0) {
                        passed = true;
                        score = 100;
                    } else if (ulElements.length > 0 && liElements.length >= 3) {
                        passed = true;
                        score = 85;
                        suggestions.push('Great list! Add a table with <table><tr><td>...</td></tr></table> for full points');
                    } else if (tableElements.length > 0) {
                        passed = true;
                        score = 85;
                        suggestions.push('Great table! Add a list with <ul><li>...</li></ul> for full points');
                    } else {
                        passed = false;
                        errors.push('Missing list or table elements');
                        suggestions.push('Create a list with <ul><li>...</li></ul> and a table with <table><tr><td>...</td></tr></table>');
                        score = Math.max(20, (ulElements.length + tableElements.length) * 30);
                    }
                    break;
                case 'css_internal':
                    // Level 5: CSS einbinden
                    const hasStyle = code.includes('<style>') || code.includes('style=');
                    const hasCSS = /[^{}]*\{[^{}]*\}/.test(code);

                    if (hasStyle && hasCSS) {
                        passed = true;
                        score = 100;
                    } else if (hasStyle || hasCSS) {
                        passed = true;
                        score = 80;
                        suggestions.push('Add CSS styling using <style> tags or inline styles');
                    } else {
                        passed = false;
                        errors.push('Missing CSS styling');
                        suggestions.push('Add CSS using <style> tags in the head or inline style attributes');
                        score = 30;
                    }
                    break;

                case 'css_colors':
                    // Level 6: Farben & Hintergründe
                    const hasColor = code.includes('color:') || code.includes('background-color:') || code.includes('background:');

                    if (hasColor) {
                        passed = true;
                        score = 100;
                    } else {
                        passed = false;
                        errors.push('Missing color properties');
                        suggestions.push('Add color or background-color properties to your CSS');
                        score = 30;
                    }
                    break;

                case 'css_box_model':
                    // Level 7: Box-Modell
                    const hasBoxModel = code.includes('padding:') || code.includes('margin:') || code.includes('border:');

                    if (hasBoxModel) {
                        passed = true;
                        score = 100;
                    } else {
                        passed = false;
                        errors.push('Missing box model properties');
                        suggestions.push('Add padding, margin, or border properties');
                        score = 30;
                    }
                    break;

                case 'css_flexbox':
                    // Level 8: Flexbox
                    const hasFlexbox = code.includes('display: flex') || code.includes('display:flex');

                    if (hasFlexbox) {
                        passed = true;
                        score = 100;
                    } else {
                        passed = false;
                        errors.push('Missing flexbox');
                        suggestions.push('Use display: flex to create a flexbox layout');
                        score = 30;
                    }
                    break;

                case 'nav_footer':
                    // Level 9: Navigation & Footer
                    const navElements = root.querySelectorAll('nav');
                    const footerElements = root.querySelectorAll('footer');

                    if (navElements.length > 0 && footerElements.length > 0) {
                        passed = true;
                        score = 100;
                    } else if (navElements.length > 0 || footerElements.length > 0) {
                        passed = true;
                        score = 80;
                        suggestions.push('Add both <nav> and <footer> elements');
                    } else {
                        passed = false;
                        errors.push('Missing navigation or footer');
                        suggestions.push('Add <nav> for navigation and <footer> for footer content');
                        score = 30;
                    }
                    break;

                case 'boss_project':
                    // Boss Levels - Project validation
                    const hasHTML = root.querySelectorAll('*').length > 5;
                    const hasInteractivity = code.includes('addEventListener') || code.includes('onclick') || code.includes('function');
                    const hasStorage = code.includes('localStorage') || code.includes('sessionStorage');
                    const hasJavaScript = code.includes('<script>') || code.includes('function') || code.includes('document.');
                    const hasNavigation = root.querySelectorAll('nav').length > 0;
                    const hasFooter = root.querySelectorAll('footer').length > 0;

                    let bossScore = 0;
                    if (hasHTML) bossScore += 20;
                    if (hasNavigation) bossScore += 20;
                    if (hasFooter) bossScore += 20;
                    if (hasJavaScript) bossScore += 20;
                    if (hasInteractivity) bossScore += 10;
                    if (hasStorage) bossScore += 10;

                    if (bossScore >= 80) {
                        passed = true;
                        score = Math.min(100, bossScore);
                    } else if (bossScore >= 60) {
                        passed = true;
                        score = bossScore;
                        suggestions.push('Great progress! Add more features for full points');
                    } else {
                        passed = false;
                        errors.push('Boss level requires comprehensive HTML structure and functionality');
                        suggestions.push('Create a complete project with navigation, content, footer, and interactivity');
                        score = Math.max(40, bossScore);
                    }
                    break;

                case 'final_boss':
                    // Final Boss Level - Ultimate validation
                    const hasCompleteStructure = hasDoctype && hasHtml && hasHead && hasBody;
                    const hasAdvancedElements = root.querySelectorAll('nav, main, section, footer').length >= 3;
                    const hasAdvancedJS = code.includes('fetch') || code.includes('async') || code.includes('class ');
                    const hasAdvancedCSS = code.includes('@media') || code.includes('grid') || code.includes('animation');

                    let finalScore = 0;
                    if (hasCompleteStructure) finalScore += 25;
                    if (hasAdvancedElements) finalScore += 25;
                    if (hasAdvancedJS) finalScore += 25;
                    if (hasAdvancedCSS) finalScore += 25;

                    if (finalScore >= 75) {
                        passed = true;
                        score = Math.min(100, finalScore);
                    } else {
                        passed = false;
                        errors.push('Final Boss requires advanced HTML, CSS, and JavaScript features');
                        suggestions.push('Create a comprehensive project with modern web technologies');
                        score = Math.max(50, finalScore);
                    }
                    break;

                    // Extended HTML Levels (21-40)
                    case 21: // HTML5 Semantic Elements
                    case 22: // HTML5 Semantic Elements - Praxis
                        const semanticElements = root.querySelectorAll('header, nav, main, section, article, aside, footer');
                        if (semanticElements.length >= 2) {
                            passed = true;
                            score = 100;
                        } else if (semanticElements.length >= 1) {
                            passed = true;
                            score = 70;
                            suggestions.push('Add more semantic HTML5 elements like <header>, <nav>, <main>, <section>, <article>, <aside>, <footer>');
                        } else {
                            passed = false;
                            errors.push('Missing HTML5 semantic elements');
                            suggestions.push('Use semantic HTML5 elements like <header>, <nav>, <main>, <section>, <article>, <aside>, <footer>');
                            score = 30;
                        }
                        break;

                    case 23: // HTML5 Input Types
                        const modernInputs = root.querySelectorAll('input[type="email"], input[type="date"], input[type="number"], input[type="range"], input[type="color"]');
                        if (modernInputs.length >= 2) {
                            passed = true;
                            score = 100;
                        } else if (modernInputs.length >= 1) {
                            passed = true;
                            score = 70;
                            suggestions.push('Add more HTML5 input types like email, date, number, range, color');
                        } else {
                            passed = false;
                            errors.push('Missing HTML5 input types');
                            suggestions.push('Use HTML5 input types like <input type="email">, <input type="date">, etc.');
                            score = 30;
                        }
                        break;

                    // Boss Levels - Project validation
                    case 30: // Interactive Todo App
                    case 40: // Modern Web Portfolio
                        // Boss levels require comprehensive validation
                        const hasBossHTML = root.querySelectorAll('*').length > 3;
                        const hasBossInteractivity = code.includes('addEventListener') || code.includes('onclick') || code.includes('function');
                        const hasBossStorage = code.includes('localStorage') || code.includes('sessionStorage');
                        const hasBossJavaScript = code.includes('<script>') || code.includes('function') || code.includes('document.');

                        if (hasBossHTML && hasBossInteractivity && hasBossStorage) {
                            passed = true;
                            score = 100;
                        } else if (hasBossHTML && hasBossJavaScript) {
                            passed = true;
                            score = 85;
                            if (!hasBossStorage) {
                                suggestions.push('Great work! Consider adding localStorage for data persistence to get full points');
                            }
                        } else if (hasBossHTML) {
                            passed = true;
                            score = 70;
                            suggestions.push('Good HTML structure! Add JavaScript interactivity and localStorage functionality');
                        } else {
                            passed = false;
                            errors.push('Boss level requires HTML structure and JavaScript functionality');
                            suggestions.push('Create a complete project with HTML elements and JavaScript functions');
                            score = 40;
                        }
                        break;

                    default:
                        // Generic HTML validation - any valid HTML passes
                        const genericElements = root.querySelectorAll('*');
                        if (genericElements.length > 0) {
                            passed = true;
                            score = 80;
                        } else {
                            errors.push('No valid HTML elements found');
                            suggestions.push('Add proper HTML elements to your code');
                            score = 20;
                        }
                        break;
                }

        } catch (parseError) {
            errors.push('HTML syntax error: ' + parseError.message);
            suggestions.push('Check your HTML tags for proper opening and closing');
            score = 10;
        }

        // Generate appropriate message
        let message;
        if (passed) {
            message = 'Excellent! Your HTML code is correct and well-structured.';
        } else if (score >= 70) {
            message = 'Good progress! Your code is mostly correct but needs some improvements.';
        } else if (score >= 40) {
            message = 'You\'re on the right track, but your code needs more work.';
        } else {
            message = 'Your code needs significant improvements. Please review the requirements.';
        }

        return {
            passed,
            score,
            message,
            errors,
            suggestions
        };
    }

    /**
     * Enhanced CSS validation with actual CSS parsing
     */
    async validateCSS(code, levelNumber, level) {
        const errors = [];
        const suggestions = [];
        let score = 0;
        let passed = false;

        // Basic CSS syntax validation
        const cssRules = this.parseCSS(code);

        if (cssRules.length === 0) {
            errors.push('No valid CSS rules found');
            suggestions.push('Add CSS rules with selectors and properties');
            return { passed: false, score: 10, message: 'No valid CSS found', errors, suggestions };
        }

        // Check for CSS syntax errors
        const syntaxErrors = this.validateCSSyntax(code);
        if (syntaxErrors.length > 0) {
            errors.push(...syntaxErrors);
            suggestions.push('Fix CSS syntax errors - check for missing semicolons, brackets, or colons');
            return { passed: false, score: 10, message: 'CSS syntax errors detected', errors, suggestions };
        }

        // Level-specific CSS validation based on actual course content
        switch (levelNumber) {
            case 4: // CSS Grundlagen - Level 4 in HTML-CSS-JS course
                let hasColorProperty = false;
                let hasFontProperty = false;

                cssRules.forEach(rule => {
                    if (rule.properties) {
                        rule.properties.forEach(prop => {
                            if (prop.property === 'color' || prop.property === 'background-color') {
                                hasColorProperty = true;
                            }
                            if (prop.property === 'font-size' || prop.property === 'font-family') {
                                hasFontProperty = true;
                            }
                        });
                    }
                });

                if (hasColorProperty && hasFontProperty) {
                    passed = true;
                    score = 100;
                } else if (hasColorProperty || hasFontProperty) {
                    passed = true;
                    score = 80;
                } else {
                    passed = false;
                    errors.push('Missing basic CSS properties');
                    suggestions.push('Add color, background-color, font-size, or font-family properties');
                    score = 30;
                }
                break;

            case 5: // CSS Selektoren - Level 5 in HTML-CSS-JS course
                let hasClassSelector = false;
                let hasIdSelector = false;
                let hasElementSelector = false;

                cssRules.forEach(rule => {
                    if (rule.selector.includes('.')) hasClassSelector = true;
                    if (rule.selector.includes('#')) hasIdSelector = true;
                    if (/^[a-zA-Z][a-zA-Z0-9]*/.test(rule.selector.trim())) hasElementSelector = true;
                });

                if (hasClassSelector && hasIdSelector && hasElementSelector) {
                    passed = true;
                    score = 100;
                } else if (hasClassSelector || hasIdSelector || hasElementSelector) {
                    passed = true;
                    score = 80;
                } else {
                    passed = false;
                    errors.push('Missing CSS selectors');
                    suggestions.push('Use element selectors (h1), class selectors (.class), or ID selectors (#id)');
                    score = 30;
                }
                break;

            // Extended CSS Levels (24-26)
            case 24: // CSS Flexbox Grundlagen
            case 25: // CSS Flexbox - Erweitert
                const hasFlexbox = code.includes('display: flex') || code.includes('display:flex');
                const hasFlexProperties = code.includes('justify-content') || code.includes('align-items') || code.includes('flex-direction');

                if (hasFlexbox && hasFlexProperties) {
                    passed = true;
                    score = 100;
                } else if (hasFlexbox) {
                    passed = true;
                    score = 70;
                    suggestions.push('Add flexbox properties like justify-content, align-items, flex-direction');
                } else {
                    passed = false;
                    errors.push('Missing CSS Flexbox');
                    suggestions.push('Use display: flex and flexbox properties like justify-content, align-items');
                    score = 30;
                }
                break;

            case 26: // CSS Grid Grundlagen
                const hasGrid = code.includes('display: grid') || code.includes('display:grid');
                const hasGridProperties = code.includes('grid-template') || code.includes('grid-column') || code.includes('grid-row');

                if (hasGrid && hasGridProperties) {
                    passed = true;
                    score = 100;
                } else if (hasGrid) {
                    passed = true;
                    score = 70;
                    suggestions.push('Add grid properties like grid-template-columns, grid-template-rows');
                } else {
                    passed = false;
                    errors.push('Missing CSS Grid');
                    suggestions.push('Use display: grid and grid properties like grid-template-columns');
                    score = 30;
                }
                break;

            case 31: // CSS Media Queries
                const hasMediaQuery = code.includes('@media');
                if (hasMediaQuery) {
                    passed = true;
                    score = 100;
                } else {
                    passed = false;
                    errors.push('Missing CSS Media Queries');
                    suggestions.push('Use @media queries for responsive design');
                    score = 30;
                }
                break;

            case 33: // CSS Animations
                const hasAnimation = code.includes('@keyframes') || code.includes('animation') || code.includes('transition');
                if (hasAnimation) {
                    passed = true;
                    score = 100;
                } else {
                    passed = false;
                    errors.push('Missing CSS Animations');
                    suggestions.push('Use @keyframes, animation, or transition properties');
                    score = 30;
                }
                break;

            default:
                // Generic CSS validation - any valid CSS passes
                if (cssRules.length > 0) {
                    passed = true;
                    score = 80;
                } else {
                    passed = false;
                    errors.push('No valid CSS rules found');
                    suggestions.push('Add CSS rules with selectors and properties');
                    score = 20;
                }
        }

        let message;
        if (passed) {
            message = 'Great! Your CSS code is working correctly.';
        } else if (score >= 60) {
            message = 'Your CSS is partially correct but needs some fixes.';
        } else {
            message = 'Your CSS needs improvement. Check the requirements.';
        }

        return { passed, score, message, errors, suggestions };
    }

    /**
     * Enhanced JavaScript validation with actual code execution in safe environment
     */
    validateJavaScript(code, levelNumber, level) {
        const errors = [];
        const suggestions = [];
        let score = 0;
        let passed = false;

        // Basic syntax check
        try {
            new Function(code);
        } catch (syntaxError) {
            errors.push('JavaScript syntax error: ' + syntaxError.message);
            suggestions.push('Check your JavaScript syntax for missing semicolons, brackets, or quotes');
            return { passed: false, score: 10, message: 'Syntax error in JavaScript', errors, suggestions };
        }

        // Dynamic JavaScript validation based on expected_output and level content
        const expectedOutput = level?.expected_output || '';

        switch (expectedOutput) {
            case 'js_variables':
                // Level 11: Erste Variablen & Datentypen
                let hasVariable = false;
                let hasConsoleLog = false;

                if (code.includes('let ') || code.includes('const ') || code.includes('var ')) {
                    hasVariable = true;
                }

                if (code.includes('console.log')) {
                    hasConsoleLog = true;
                }

                if (hasVariable && hasConsoleLog) {
                    try {
                        let output = '';
                        const originalLog = console.log;
                        console.log = (...args) => {
                            output += args.join(' ') + '\n';
                        };

                        eval(code);
                        console.log = originalLog;

                        if (output.trim().length > 0) {
                            passed = true;
                            score = 100;
                        } else {
                            passed = false;
                            errors.push('console.log() is not producing any output');
                            suggestions.push('Make sure your console.log() statement displays the variable');
                            score = 60;
                        }
                    } catch (runtimeError) {
                        passed = false;
                        errors.push('Runtime error: ' + runtimeError.message);
                        suggestions.push('Check your code for logical errors');
                        score = 40;
                    }
                } else {
                    passed = false;
                    if (!hasVariable) {
                        errors.push('Missing variable declaration');
                        suggestions.push('Declare a variable using let, const, or var');
                    }
                    if (!hasConsoleLog) {
                        errors.push('Missing console.log() statement');
                        suggestions.push('Use console.log() to output the variable');
                    }
                    score = Math.max(20, (hasVariable ? 40 : 0) + (hasConsoleLog ? 40 : 0));
                }
                break;

            case 'js_conditions':
                // Level 12: Operatoren & Bedingungen
                const hasIf = code.includes('if');
                const hasComparison = /[<>=!]=?/.test(code);

                if (hasIf && hasComparison) {
                    try {
                        eval(code);
                        passed = true;
                        score = 100;
                    } catch (runtimeError) {
                        passed = false;
                        errors.push('Runtime error: ' + runtimeError.message);
                        suggestions.push('Check your if statement syntax');
                        score = 60;
                    }
                } else {
                    passed = false;
                    if (!hasIf) errors.push('Missing if statement');
                    if (!hasComparison) errors.push('Missing comparison operators');
                    suggestions.push('Use if statements with comparison operators (>, <, ==, etc.)');
                    score = Math.max(20, (hasIf ? 50 : 0) + (hasComparison ? 30 : 0));
                }
                break;

            case 'js_loops':
                // Level 13: Schleifen
                const hasLoop = code.includes('for') || code.includes('while') || code.includes('forEach');

                if (hasLoop) {
                    try {
                        eval(code);
                        passed = true;
                        score = 100;
                    } catch (runtimeError) {
                        passed = false;
                        errors.push('Runtime error: ' + runtimeError.message);
                        suggestions.push('Check your loop syntax');
                        score = 60;
                    }
                } else {
                    passed = false;
                    errors.push('Missing loop structure');
                    suggestions.push('Use for, while, or forEach loops');
                    score = 20;
                }
                break;

            case 'js_functions':
                // Level 14: Funktionen
                const hasFunction = code.includes('function ') || code.includes('=>') || code.includes('function(');
                const hasFunctionCall = /\w+\s*\(/.test(code);

                if (hasFunction && hasFunctionCall) {
                    try {
                        eval(code);
                        passed = true;
                        score = 100;
                    } catch (runtimeError) {
                        passed = false;
                        errors.push('Runtime error: ' + runtimeError.message);
                        suggestions.push('Check your function definition and call');
                        score = 60;
                    }
                } else {
                    passed = false;
                    if (!hasFunction) {
                        errors.push('Missing function declaration');
                        suggestions.push('Create a function using function keyword or arrow syntax');
                    }
                    if (!hasFunctionCall) {
                        errors.push('Missing function call');
                        suggestions.push('Call your function by adding parentheses after the function name');
                    }
                    score = Math.max(20, (hasFunction ? 50 : 0) + (hasFunctionCall ? 30 : 0));
                }
                break;

            case 'js_dom':
                // Level 15: DOM-Auswahl
                const hasDOM = code.includes('document.') || code.includes('getElementById') || code.includes('querySelector');

                if (hasDOM) {
                    passed = true;
                    score = 100;
                } else {
                    passed = false;
                    errors.push('Missing DOM manipulation');
                    suggestions.push('Use document.getElementById, querySelector, or other DOM methods');
                    score = 30;
                }
                break;

            case 'js_events':
                // Level 16: Events
                const hasEventListener = code.includes('addEventListener') || code.includes('onclick');

                if (hasEventListener) {
                    passed = true;
                    score = 100;
                } else {
                    passed = false;
                    errors.push('Missing event handling');
                    suggestions.push('Use addEventListener or onclick for event handling');
                    score = 30;
                }
                break;

            case 'js_forms':
                // Level 17: Formulare auslesen
                const hasFormAccess = code.includes('.value') || code.includes('getElementById') || code.includes('querySelector');

                if (hasFormAccess) {
                    passed = true;
                    score = 100;
                } else {
                    passed = false;
                    errors.push('Missing form data access');
                    suggestions.push('Use .value to get form input values');
                    score = 30;
                }
                break;

            case 'js_arrays_objects':
                // Level 18: Arrays & Objekte
                const hasArray = code.includes('[') && code.includes(']');
                const hasObject = code.includes('{') && code.includes(':');

                if (hasArray && hasObject) {
                    try {
                        eval(code);
                        passed = true;
                        score = 100;
                    } catch (runtimeError) {
                        passed = false;
                        errors.push('Runtime error: ' + runtimeError.message);
                        suggestions.push('Check your array and object syntax');
                        score = 60;
                    }
                } else {
                    passed = false;
                    if (!hasArray) errors.push('Missing array declaration');
                    if (!hasObject) errors.push('Missing object declaration');
                    suggestions.push('Create arrays with [] and objects with {}');
                    score = Math.max(20, (hasArray ? 50 : 0) + (hasObject ? 50 : 0));
                }
                break;

            case 'js_localstorage':
                // Level 19: Lokale Speicherung
                const hasLocalStorage = code.includes('localStorage') || code.includes('sessionStorage');

                if (hasLocalStorage) {
                    passed = true;
                    score = 100;
                } else {
                    passed = false;
                    errors.push('Missing local storage');
                    suggestions.push('Use localStorage.setItem() and localStorage.getItem()');
                    score = 30;
                }
                break;

            case 'css_grid':
                // Level 21: CSS Grid
                const hasGrid = code.includes('display: grid') || code.includes('display:grid');

                if (hasGrid) {
                    passed = true;
                    score = 100;
                } else {
                    passed = false;
                    errors.push('Missing CSS Grid');
                    suggestions.push('Use display: grid to create grid layouts');
                    score = 30;
                }
                break;

            case 'css_responsive':
                // Level 22: Responsive Design
                const hasMediaQuery = code.includes('@media');

                if (hasMediaQuery) {
                    passed = true;
                    score = 100;
                } else {
                    passed = false;
                    errors.push('Missing media queries');
                    suggestions.push('Use @media queries for responsive design');
                    score = 30;
                }
                break;

            case 'css_animations':
                // Level 23: Animierte Buttons
                const hasAnimation = code.includes('transition') || code.includes('animation') || code.includes('@keyframes');

                if (hasAnimation) {
                    passed = true;
                    score = 100;
                } else {
                    passed = false;
                    errors.push('Missing CSS animations');
                    suggestions.push('Use transition, animation, or @keyframes for animations');
                    score = 30;
                }
                break;

            case 'js_modals':
                // Level 24: Modals & Popups
                const hasModal = code.includes('modal') || (code.includes('display') && code.includes('block'));

                if (hasModal) {
                    passed = true;
                    score = 100;
                } else {
                    passed = false;
                    errors.push('Missing modal functionality');
                    suggestions.push('Create modal functionality with display properties');
                    score = 30;
                }
                break;

            // Legacy level number support for backward compatibility
            case 9: // JavaScript Funktionen - Level 9 in HTML-CSS-JS course
                let legacyHasFunction = false;
                let legacyHasFunctionCall = false;

                // Check for function declaration or expression
                if (code.includes('function ') || code.includes('=>') || code.includes('function(')) {
                    legacyHasFunction = true;
                }

                // Check for function call (parentheses after identifier)
                if (/\w+\s*\(/.test(code)) {
                    legacyHasFunctionCall = true;
                }

                if (legacyHasFunction && legacyHasFunctionCall) {
                    try {
                        eval(code);
                        passed = true;
                        score = 100;
                    } catch (runtimeError) {
                        passed = false;
                        errors.push('Runtime error: ' + runtimeError.message);
                        suggestions.push('Check your function definition and call');
                        score = 60;
                    }
                } else {
                    passed = false;
                    if (!legacyHasFunction) {
                        errors.push('Missing function declaration');
                        suggestions.push('Create a function using function keyword or arrow syntax');
                    }
                    if (!legacyHasFunctionCall) {
                        errors.push('Missing function call');
                        suggestions.push('Call your function by adding parentheses after the function name');
                    }
                    score = Math.max(20, (legacyHasFunction ? 50 : 0) + (legacyHasFunctionCall ? 30 : 0));
                }
                break;

            // JavaScript Advanced Course (course_id = 2)
            case 1: // Arrays und Schleifen - Level 1 in JavaScript Advanced course
                let advancedHasArray = false;
                let advancedHasLoop = false;

                // Check for array declaration
                if (code.includes('[') && code.includes(']')) {
                    advancedHasArray = true;
                }

                // Check for loops
                if (code.includes('for') || code.includes('while') || code.includes('forEach')) {
                    advancedHasLoop = true;
                }

                if (advancedHasArray && advancedHasLoop) {
                    try {
                        eval(code);
                        passed = true;
                        score = 100;
                    } catch (runtimeError) {
                        passed = false;
                        errors.push('Runtime error: ' + runtimeError.message);
                        suggestions.push('Check your array and loop syntax');
                        score = 60;
                    }
                } else {
                    passed = false;
                    if (!advancedHasArray) {
                        errors.push('Missing array declaration');
                        suggestions.push('Create an array using [element1, element2, ...]');
                    }
                    if (!advancedHasLoop) {
                        errors.push('Missing loop structure');
                        suggestions.push('Use for, while, or forEach to iterate through the array');
                    }
                    score = Math.max(20, (advancedHasArray ? 50 : 0) + (advancedHasLoop ? 50 : 0));
                }
                break;

            // Extended JavaScript Levels (27-29, 34-39)
            case 27: // JavaScript DOM Manipulation
                const hasDOM = code.includes('document.') || code.includes('getElementById') || code.includes('querySelector');
                if (hasDOM) {
                    passed = true;
                    score = 100;
                } else {
                    passed = false;
                    errors.push('Missing DOM manipulation');
                    suggestions.push('Use document.getElementById, querySelector, or other DOM methods');
                    score = 30;
                }
                break;

            case 28: // JavaScript Events
                const hasEventListener = code.includes('addEventListener') || code.includes('onclick');
                if (hasEventListener) {
                    passed = true;
                    score = 100;
                } else {
                    passed = false;
                    errors.push('Missing event handling');
                    suggestions.push('Use addEventListener or onclick for event handling');
                    score = 30;
                }
                break;

            case 29: // JavaScript Local Storage
                const hasLocalStorage = code.includes('localStorage') || code.includes('sessionStorage');
                if (hasLocalStorage) {
                    passed = true;
                    score = 100;
                } else {
                    passed = false;
                    errors.push('Missing local storage');
                    suggestions.push('Use localStorage.setItem() and localStorage.getItem()');
                    score = 30;
                }
                break;

            case 34: // Fetch API
                const hasFetch = code.includes('fetch(');
                if (hasFetch) {
                    passed = true;
                    score = 100;
                } else {
                    passed = false;
                    errors.push('Missing fetch API');
                    suggestions.push('Use fetch() for HTTP requests');
                    score = 30;
                }
                break;

            case 37: // ES6 Arrow Functions
                const hasArrowFunction = code.includes('=>');
                if (hasArrowFunction) {
                    passed = true;
                    score = 100;
                } else {
                    passed = false;
                    errors.push('Missing arrow functions');
                    suggestions.push('Use arrow function syntax: () => {}');
                    score = 30;
                }
                break;

            case 38: // Async/Await
                const hasAsync = code.includes('async') && code.includes('await');
                if (hasAsync) {
                    passed = true;
                    score = 100;
                } else {
                    passed = false;
                    errors.push('Missing async/await');
                    suggestions.push('Use async function and await keyword');
                    score = 30;
                }
                break;

            // JavaScript Advanced Extended Levels (21-39)
            case 21: // Promise Chaining
                const hasPromiseChain = code.includes('.then(') && code.includes('Promise');
                if (hasPromiseChain) {
                    passed = true;
                    score = 100;
                } else {
                    passed = false;
                    errors.push('Missing Promise chaining');
                    suggestions.push('Use Promise.then() for chaining operations');
                    score = 30;
                }
                break;

            case 24: // Higher-Order Functions
                const hasHigherOrder = code.includes('.map(') || code.includes('.filter(') || code.includes('.reduce(');
                if (hasHigherOrder) {
                    passed = true;
                    score = 100;
                } else {
                    passed = false;
                    errors.push('Missing higher-order functions');
                    suggestions.push('Use array methods like map(), filter(), reduce()');
                    score = 30;
                }
                break;

            default:
                // Generic JavaScript validation - any working JavaScript passes
                try {
                    eval(code);
                    passed = true;
                    score = 80;
                } catch (runtimeError) {
                    passed = false;
                    errors.push('Runtime error: ' + runtimeError.message);
                    suggestions.push('Check your JavaScript code for logical errors');
                    score = 20;
                }
        }

        let message;
        if (passed) {
            message = 'Perfect! Your JavaScript code works correctly.';
        } else if (score >= 60) {
            message = 'Your JavaScript is mostly correct but has some issues.';
        } else {
            message = 'Your JavaScript needs more work. Review the requirements.';
        }

        return { passed, score, message, errors, suggestions };
    }

    /**
     * PHP Code Validation with actual PHP syntax checking
     */
    async validatePHP(code, levelNumber, level) {
        const errors = [];
        const suggestions = [];
        let score = 0;
        let passed = false;

        // Ensure PHP code has proper opening tags
        if (!code.includes('<?php') && !code.startsWith('<?')) {
            code = '<?php\n' + code;
        }

        // Create temporary PHP file
        const tempFile = path.join(this.tempDir, `temp_${Date.now()}.php`);

        try {
            fs.writeFileSync(tempFile, code);

            // Check PHP syntax
            const syntaxResult = await this.runCommand('php', ['-l', tempFile]);

            if (syntaxResult.exitCode !== 0) {
                errors.push('PHP Syntax Error: ' + syntaxResult.stderr);
                suggestions.push('Check your PHP syntax for missing semicolons, brackets, or quotes');
                return { passed: false, score: 0, message: 'PHP syntax error detected', errors, suggestions };
            }

            // Dynamic PHP validation based on expected_output
            const expectedOutput = level?.expected_output || '';

            switch (expectedOutput) {
                case 'php_hello':
                    // Level 1: PHP Hello World
                    if (!code.includes('echo') && !code.includes('print')) {
                        errors.push('Missing echo or print statement');
                        suggestions.push('Use echo or print to output text');
                        score = 30;
                    } else {
                        const execResult = await this.runCommand('php', [tempFile]);
                        if (execResult.exitCode === 0 && execResult.stdout.trim().length > 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Code does not produce any output');
                            suggestions.push('Make sure your echo/print statement outputs something');
                            score = 60;
                        }
                    }
                    break;

                case 'php_variables':
                    // Level 2: Variablen & Datentypen
                    if (!code.includes('$')) {
                        errors.push('Missing PHP variables (variables start with $)');
                        suggestions.push('Create variables using $ syntax, e.g., $name = "value"');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('php', [tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your variable assignments and usage');
                            score = 40;
                        }
                    }
                    break;

                case 'php_arrays':
                    // Level 3: Arrays & Strings
                    if (!code.includes('array') && !code.includes('[') && !code.includes(']')) {
                        errors.push('Missing array declaration');
                        suggestions.push('Create arrays using array() or [] syntax');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('php', [tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your array syntax and usage');
                            score = 40;
                        }
                    }
                    break;

                case 'php_conditions':
                    // Level 4: Bedingungen
                    if (!code.includes('if')) {
                        errors.push('Missing if statement');
                        suggestions.push('Use if statements for conditional logic');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('php', [tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your if statement syntax');
                            score = 40;
                        }
                    }
                    break;

                case 'php_loops':
                    // Level 5: Schleifen
                    if (!code.includes('for') && !code.includes('while') && !code.includes('foreach')) {
                        errors.push('Missing loop structure');
                        suggestions.push('Use for, while, or foreach loops');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('php', [tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your loop syntax');
                            score = 40;
                        }
                    }
                    break;

                case 'php_functions':
                    // Level 6: Funktionen
                    if (!code.includes('function')) {
                        errors.push('Missing function declaration');
                        suggestions.push('Create a function using the function keyword');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('php', [tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your function syntax and calls');
                            score = 40;
                        }
                    }
                    break;

                case 'php_forms':
                    // Level 7: Formulare
                    if (!code.includes('$_POST') && !code.includes('$_GET')) {
                        errors.push('Missing form data handling');
                        suggestions.push('Use $_POST or $_GET to handle form data');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('php', [tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your form handling code');
                            score = 40;
                        }
                    }
                    break;

                case 'php_sessions':
                    // Level 8: Sessions & Cookies
                    if (!code.includes('session_start') && !code.includes('$_SESSION')) {
                        errors.push('Missing session handling');
                        suggestions.push('Use session_start() and $_SESSION for session management');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('php', [tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your session code');
                            score = 40;
                        }
                    }
                    break;

                case 'php_files':
                    // Level 9: Dateien lesen/schreiben
                    if (!code.includes('file_get_contents') && !code.includes('file_put_contents') && !code.includes('fopen')) {
                        errors.push('Missing file operations');
                        suggestions.push('Use file_get_contents, file_put_contents, or fopen for file operations');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('php', [tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your file operation code');
                            score = 40;
                        }
                    }
                    break;

                // Legacy level number support
                case 2: // Variablen und Datentypen

                case 2: // Variablen und Datentypen
                    if (!code.includes('$')) {
                        errors.push('Missing PHP variables (variables start with $)');
                        suggestions.push('Create variables using $ syntax, e.g., $name = "value"');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('php', [tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your variable assignments and usage');
                            score = 40;
                        }
                    }
                    break;

                case 3: // Arrays
                    if (!code.includes('array') && !code.includes('[') && !code.includes(']')) {
                        errors.push('Missing array declaration');
                        suggestions.push('Create arrays using array() or [] syntax');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('php', [tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your array syntax and usage');
                            score = 40;
                        }
                    }
                    break;

                case 4: // Kontrollstrukturen
                    if (!code.includes('if') && !code.includes('for') && !code.includes('while')) {
                        errors.push('Missing control structures (if, for, while)');
                        suggestions.push('Use control structures like if statements or loops');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('php', [tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your control structure syntax');
                            score = 40;
                        }
                    }
                    break;

                case 5: // Funktionen
                    if (!code.includes('function')) {
                        errors.push('Missing function declaration');
                        suggestions.push('Create a function using the function keyword');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('php', [tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your function syntax and calls');
                            score = 40;
                        }
                    }
                    break;

                default:
                    // Generic PHP validation - just check if it runs without errors
                    const execResult = await this.runCommand('php', [tempFile]);
                    if (execResult.exitCode === 0) {
                        passed = true;
                        score = 100;
                    } else {
                        errors.push('Runtime error: ' + execResult.stderr);
                        suggestions.push('Fix the runtime errors in your PHP code');
                        score = 20;
                    }
            }

        } catch (error) {
            errors.push('Validation error: ' + error.message);
            suggestions.push('Check your PHP installation and code syntax');
            score = 0;
        } finally {
            // Clean up temp file
            if (fs.existsSync(tempFile)) {
                fs.unlinkSync(tempFile);
            }
        }

        let message;
        if (passed) {
            message = 'Excellent! Your PHP code works correctly.';
        } else if (score >= 60) {
            message = 'Your PHP code has some issues but is partially correct.';
        } else {
            message = 'Your PHP code needs significant improvements.';
        }

        return { passed, score, message, errors, suggestions };
    }

    /**
     * Python Code Validation with actual Python syntax checking and execution
     */
    async validatePython(code, levelNumber, level) {
        const errors = [];
        const suggestions = [];
        let score = 0;
        let passed = false;

        // Create temporary Python file
        const tempFile = path.join(this.tempDir, `temp_${Date.now()}.py`);

        try {
            fs.writeFileSync(tempFile, code);

            // Check Python syntax by compiling
            const syntaxResult = await this.runCommand('python3', ['-m', 'py_compile', tempFile]);

            if (syntaxResult.exitCode !== 0) {
                errors.push('Python Syntax Error: ' + syntaxResult.stderr);
                suggestions.push('Check your Python syntax for proper indentation, colons, and brackets');
                return { passed: false, score: 0, message: 'Python syntax error detected', errors, suggestions };
            }

            // Dynamic Python validation based on expected_output
            const expectedOutput = level?.expected_output || '';

            switch (expectedOutput) {
                case 'python_hello':
                    // Level 1: Python Hello World
                    if (!code.includes('print')) {
                        errors.push('Missing print statement');
                        suggestions.push('Use print() to output text');
                        score = 30;
                    } else {
                        const execResult = await this.runCommand('python3', [tempFile]);
                        if (execResult.exitCode === 0 && execResult.stdout.trim().length > 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Code does not produce any output or has runtime errors');
                            suggestions.push('Make sure your print statement works correctly');
                            score = 60;
                        }
                    }
                    break;

                case 'python_variables':
                    // Level 2: Variablen & Datentypen
                    if (!/\w+\s*=/.test(code)) {
                        errors.push('Missing variable assignment');
                        suggestions.push('Create variables using assignment operator =');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('python3', [tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your variable assignments and usage');
                            score = 40;
                        }
                    }
                    break;

                case 'python_lists':
                    // Level 3: Listen & Tupel
                    if (!code.includes('[') && !code.includes('(')) {
                        errors.push('Missing list or tuple declaration');
                        suggestions.push('Create lists with [] or tuples with ()');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('python3', [tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your list/tuple syntax');
                            score = 40;
                        }
                    }
                    break;

                case 'python_conditions':
                    // Level 4: Bedingungen
                    if (!code.includes('if')) {
                        errors.push('Missing if statement');
                        suggestions.push('Use if statements for conditional logic');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('python3', [tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your if statement syntax and indentation');
                            score = 40;
                        }
                    }
                    break;

                case 'python_loops':
                    // Level 5: Schleifen
                    if (!code.includes('for') && !code.includes('while')) {
                        errors.push('Missing loop structure');
                        suggestions.push('Use for or while loops');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('python3', [tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your loop syntax and indentation');
                            score = 40;
                        }
                    }
                    break;

                case 'python_functions':
                    // Level 6: Funktionen
                    if (!code.includes('def ')) {
                        errors.push('Missing function definition');
                        suggestions.push('Create a function using def keyword');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('python3', [tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your function definition and calls');
                            score = 40;
                        }
                    }
                    break;

                case 'python_dictionaries':
                    // Level 7: Dictionaries
                    if (!code.includes('{') || !code.includes(':')) {
                        errors.push('Missing dictionary declaration');
                        suggestions.push('Create dictionaries using {} with key:value pairs');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('python3', [tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your dictionary syntax');
                            score = 40;
                        }
                    }
                    break;

                case 'python_files':
                    // Level 8: Dateien lesen/schreiben
                    if (!code.includes('open') && !code.includes('with open')) {
                        errors.push('Missing file operations');
                        suggestions.push('Use open() or with open() for file operations');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('python3', [tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your file operation code');
                            score = 40;
                        }
                    }
                    break;

                case 'python_modules':
                    // Level 9: Module & Packages
                    if (!code.includes('import') && !code.includes('from ')) {
                        errors.push('Missing import statements');
                        suggestions.push('Use import or from...import to use modules');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('python3', [tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your import statements and module usage');
                            score = 40;
                        }
                    }
                    break;

                // Legacy level number support
                case 2: // Variablen und Datentypen
                    if (!/\w+\s*=\s*.+/.test(code)) {
                        errors.push('Missing variable assignments');
                        suggestions.push('Create variables using assignment operator =');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('python3', [tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your variable assignments and types');
                            score = 40;
                        }
                    }
                    break;

                case 3: // Listen und Tupel
                    if (!code.includes('[') && !code.includes('(')) {
                        errors.push('Missing list or tuple declaration');
                        suggestions.push('Create lists using [] or tuples using ()');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('python3', [tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your list/tuple syntax');
                            score = 40;
                        }
                    }
                    break;

                case 4: // Dictionaries
                    if (!code.includes('{') || !code.includes(':')) {
                        errors.push('Missing dictionary declaration');
                        suggestions.push('Create dictionaries using {key: value} syntax');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('python3', [tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your dictionary syntax');
                            score = 40;
                        }
                    }
                    break;

                case 5: // Kontrollstrukturen
                    if (!code.includes('if') && !code.includes('for') && !code.includes('while')) {
                        errors.push('Missing control structures (if, for, while)');
                        suggestions.push('Use control structures like if statements or loops');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('python3', [tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your control structure syntax and indentation');
                            score = 40;
                        }
                    }
                    break;

                default:
                    // Generic Python validation - just check if it runs without errors
                    const execResult = await this.runCommand('python3', [tempFile]);
                    if (execResult.exitCode === 0) {
                        passed = true;
                        score = 100;
                    } else {
                        errors.push('Runtime error: ' + execResult.stderr);
                        suggestions.push('Fix the runtime errors in your Python code');
                        score = 20;
                    }
            }

        } catch (error) {
            errors.push('Validation error: ' + error.message);
            suggestions.push('Check your Python installation and code syntax');
            score = 0;
        } finally {
            // Clean up temp file
            if (fs.existsSync(tempFile)) {
                fs.unlinkSync(tempFile);
            }
        }

        let message;
        if (passed) {
            message = 'Perfect! Your Python code works correctly.';
        } else if (score >= 60) {
            message = 'Your Python code has some issues but is partially correct.';
        } else {
            message = 'Your Python code needs significant improvements.';
        }

        return { passed, score, message, errors, suggestions };
    }

    /**
     * Go Code Validation with actual Go compilation and execution
     */
    async validateGo(code, levelNumber, level) {
        const errors = [];
        const suggestions = [];
        let score = 0;
        let passed = false;

        // Ensure Go code has proper package and main function structure
        if (!code.includes('package main')) {
            code = 'package main\n\nimport "fmt"\n\n' + code;
        }
        if (!code.includes('func main()')) {
            code = code + '\n\nfunc main() {\n    // Add your code here\n}';
        }

        // Create temporary Go file
        const tempFile = path.join(this.tempDir, `temp_${Date.now()}.go`);

        try {
            fs.writeFileSync(tempFile, code);

            // Check Go syntax by building
            const buildResult = await this.runCommand('go', ['build', '-o', '/dev/null', tempFile]);

            if (buildResult.exitCode !== 0) {
                errors.push('Go Build Error: ' + buildResult.stderr);
                suggestions.push('Check your Go syntax for proper package declaration, imports, and function structure');
                return { passed: false, score: 0, message: 'Go compilation error detected', errors, suggestions };
            }

            // Dynamic Go validation based on expected_output
            const expectedOutput = level?.expected_output || '';

            switch (expectedOutput) {
                case 'go_hello':
                    // Level 1: Go Hello World
                    if (!code.includes('fmt.Println') && !code.includes('fmt.Printf')) {
                        errors.push('Missing fmt.Println or fmt.Printf statement');
                        suggestions.push('Use fmt.Println() or fmt.Printf() to output text');
                        score = 30;
                    } else {
                        const execResult = await this.runCommand('go', ['run', tempFile]);
                        if (execResult.exitCode === 0 && execResult.stdout.trim().length > 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Code does not produce any output or has runtime errors');
                            suggestions.push('Make sure your fmt.Println statement works correctly');
                            score = 60;
                        }
                    }
                    break;

                case 'go_variables':
                    // Level 2: Variablen & Typen
                    if (!code.includes('var ') && !code.includes(':=')) {
                        errors.push('Missing variable declaration');
                        suggestions.push('Declare variables using var keyword or := shorthand');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('go', ['run', tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your variable declarations and types');
                            score = 40;
                        }
                    }
                    break;

                case 'go_arrays_slices':
                    // Level 3: Arrays & Slices
                    if (!code.includes('[') && !code.includes('make([]')) {
                        errors.push('Missing array or slice declaration');
                        suggestions.push('Create arrays with [size]type or slices with []type or make()');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('go', ['run', tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your array/slice syntax');
                            score = 40;
                        }
                    }
                    break;

                case 'go_conditions':
                    // Level 4: Bedingungen
                    if (!code.includes('if ')) {
                        errors.push('Missing if statement');
                        suggestions.push('Use if statements for conditional logic');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('go', ['run', tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your if statement syntax');
                            score = 40;
                        }
                    }
                    break;

                case 'go_loops':
                    // Level 5: Schleifen
                    if (!code.includes('for ')) {
                        errors.push('Missing for loop');
                        suggestions.push('Use for loops for iteration');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('go', ['run', tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your for loop syntax');
                            score = 40;
                        }
                    }
                    break;

                case 'go_functions':
                    // Level 6: Funktionen
                    if (!code.includes('func ') || code.split('func ').length < 3) {
                        errors.push('Missing custom function declaration');
                        suggestions.push('Create a function using func keyword');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('go', ['run', tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your function definition and calls');
                            score = 40;
                        }
                    }
                    break;

                case 'go_structs':
                    // Level 7: Structs
                    if (!code.includes('type ') || !code.includes('struct')) {
                        errors.push('Missing struct declaration');
                        suggestions.push('Create structs using type Name struct{}');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('go', ['run', tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your struct syntax');
                            score = 40;
                        }
                    }
                    break;

                case 'go_pointers':
                    // Level 8: Pointer
                    if (!code.includes('*') || !code.includes('&')) {
                        errors.push('Missing pointer operations');
                        suggestions.push('Use * for dereferencing and & for getting addresses');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('go', ['run', tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your pointer syntax');
                            score = 40;
                        }
                    }
                    break;

                case 'go_interfaces':
                    // Level 9: Interfaces
                    if (!code.includes('interface')) {
                        errors.push('Missing interface declaration');
                        suggestions.push('Create interfaces using type Name interface{}');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('go', ['run', tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your interface implementation');
                            score = 40;
                        }
                    }
                    break;

                // Legacy level number support
                case 2: // Variablen und Typen

                case 2: // Variablen und Typen
                    if (!code.includes('var') && !code.includes(':=')) {
                        errors.push('Missing variable declarations');
                        suggestions.push('Declare variables using var keyword or := shorthand');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('go', ['run', tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your variable declarations and types');
                            score = 40;
                        }
                    }
                    break;

                case 3: // Arrays und Slices
                    if (!code.includes('[') && !code.includes('make([]')) {
                        errors.push('Missing array or slice declaration');
                        suggestions.push('Create arrays using [size]type{} or slices using []type{} or make([]type, size)');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('go', ['run', tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your array/slice syntax');
                            score = 40;
                        }
                    }
                    break;

                case 4: // Maps
                    if (!code.includes('map[') && !code.includes('make(map')) {
                        errors.push('Missing map declaration');
                        suggestions.push('Create maps using map[keyType]valueType{} or make(map[keyType]valueType)');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('go', ['run', tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your map syntax');
                            score = 40;
                        }
                    }
                    break;

                case 5: // Funktionen
                    if (!code.includes('func ') || code.split('func ').length < 3) { // main + custom function
                        errors.push('Missing custom function declaration');
                        suggestions.push('Define a custom function using func functionName() { ... }');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('go', ['run', tempFile]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your function syntax and calls');
                            score = 40;
                        }
                    }
                    break;

                default:
                    // Generic Go validation - just check if it compiles and runs
                    const execResult = await this.runCommand('go', ['run', tempFile]);
                    if (execResult.exitCode === 0) {
                        passed = true;
                        score = 100;
                    } else {
                        errors.push('Runtime error: ' + execResult.stderr);
                        suggestions.push('Fix the runtime errors in your Go code');
                        score = 20;
                    }
            }

        } catch (error) {
            errors.push('Validation error: ' + error.message);
            suggestions.push('Check your Go installation and code syntax');
            score = 0;
        } finally {
            // Clean up temp file
            if (fs.existsSync(tempFile)) {
                fs.unlinkSync(tempFile);
            }
        }

        let message;
        if (passed) {
            message = 'Excellent! Your Go code compiles and runs correctly.';
        } else if (score >= 60) {
            message = 'Your Go code has some issues but is partially correct.';
        } else {
            message = 'Your Go code needs significant improvements.';
        }

        return { passed, score, message, errors, suggestions };
    }

    /**
     * Java Code Validation with actual Java compilation and execution
     */
    async validateJava(code, levelNumber, level) {
        const errors = [];
        const suggestions = [];
        let score = 0;
        let passed = false;

        // Extract class name or use default
        const classMatch = code.match(/public\s+class\s+(\w+)/);
        const className = classMatch ? classMatch[1] : 'HelloWorld';

        // Ensure Java code has proper class structure
        if (!code.includes('public class')) {
            code = `public class ${className} {\n    public static void main(String[] args) {\n        ${code}\n    }\n}`;
        }

        // Create temporary Java file
        const tempFile = path.join(this.tempDir, `${className}.java`);

        try {
            fs.writeFileSync(tempFile, code);

            // Compile Java code
            const compileResult = await this.runCommand('javac', [tempFile]);

            if (compileResult.exitCode !== 0) {
                // Improved error handling - don't always fail completely
                const compileError = compileResult.stderr || compileResult.stdout || 'Unknown compilation error';
                console.log(`⚠️ Java compilation issue: ${compileError}`);

                // Check if Java compiler is available
                if (compileError.includes('javac: command not found') || compileError.includes('not recognized')) {
                    console.log('☕ Java compiler not available, using syntax-only validation');
                    return this.validateJavaSyntaxOnly(code, levelNumber, level);
                }

                // For simple syntax errors, provide partial credit
                if (this.isSimpleJavaSyntaxError(compileError)) {
                    errors.push('Minor Java syntax error: ' + compileError);
                    suggestions.push('Check your Java syntax for proper semicolons, brackets, and method structure');
                    return { passed: false, score: 30, message: 'Java code has minor syntax errors but shows understanding', errors, suggestions };
                }

                errors.push('Java compilation error: ' + compileError);
                suggestions.push('Check your Java syntax for proper class declaration, method structure, and semicolons');
                return { passed: false, score: 10, message: 'Java code has compilation errors', errors, suggestions };
            }

            // Dynamic Java validation based on expected_output
            const expectedOutput = level?.expected_output || '';

            switch (expectedOutput) {
                case 'java_hello':
                    // Level 1: Java Hello World
                    if (!code.includes('System.out.println') && !code.includes('System.out.print')) {
                        errors.push('Missing System.out.println or System.out.print statement');
                        suggestions.push('Use System.out.println() to output text');
                        score = 30;
                    } else {
                        const execResult = await this.runCommand('java', ['-cp', this.tempDir, className]);
                        if (execResult.exitCode === 0 && execResult.stdout.trim().length > 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Code does not produce any output or has runtime errors');
                            suggestions.push('Make sure your System.out.println statement works correctly');
                            score = 60;
                        }
                    }
                    break;

                case 'java_variables':
                    // Level 2: Variablen & Datentypen
                    if (!code.includes('int ') && !code.includes('String ') && !code.includes('boolean ') && !code.includes('double ')) {
                        errors.push('Missing variable declarations with types');
                        suggestions.push('Declare variables with proper types like int, String, boolean, double');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('java', ['-cp', this.tempDir, className]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your variable declarations and types');
                            score = 40;
                        }
                    }
                    break;

                case 'java_arrays':
                    // Level 3: Arrays
                    if (!code.includes('[]') && !code.includes('new ')) {
                        errors.push('Missing array declaration');
                        suggestions.push('Create arrays using type[] arrayName = new type[size] or type[] arrayName = {values}');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('java', ['-cp', this.tempDir, className]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your array syntax and initialization');
                            score = 40;
                        }
                    }
                    break;

                case 'java_conditions':
                    // Level 4: Bedingungen
                    if (!code.includes('if ')) {
                        errors.push('Missing if statement');
                        suggestions.push('Use if statements for conditional logic');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('java', ['-cp', this.tempDir, className]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your if statement syntax');
                            score = 40;
                        }
                    }
                    break;

                case 'java_loops':
                    // Level 5: Schleifen
                    if (!code.includes('for ') && !code.includes('while ')) {
                        errors.push('Missing loop structure');
                        suggestions.push('Use for or while loops for iteration');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('java', ['-cp', this.tempDir, className]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your loop syntax');
                            score = 40;
                        }
                    }
                    break;

                case 'java_methods':
                    // Level 6: Methoden
                    if (!code.includes('public static') || code.split('public static').length < 3) {
                        errors.push('Missing custom method declaration');
                        suggestions.push('Create methods using public static returnType methodName()');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('java', ['-cp', this.tempDir, className]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your method definition and calls');
                            score = 40;
                        }
                    }
                    break;

                case 'java_classes':
                    // Level 7: Klassen & Objekte
                    if (!code.includes('new ') || code.split('class ').length < 2) {
                        errors.push('Missing class instantiation or additional class');
                        suggestions.push('Create objects using new keyword and define custom classes');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('java', ['-cp', this.tempDir, className]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your class and object syntax');
                            score = 40;
                        }
                    }
                    break;

                case 'java_inheritance':
                    // Level 8: Vererbung
                    if (!code.includes('extends')) {
                        errors.push('Missing inheritance');
                        suggestions.push('Use extends keyword for class inheritance');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('java', ['-cp', this.tempDir, className]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your inheritance syntax');
                            score = 40;
                        }
                    }
                    break;

                case 'java_interfaces':
                    // Level 9: Interfaces
                    if (!code.includes('interface') && !code.includes('implements')) {
                        errors.push('Missing interface declaration or implementation');
                        suggestions.push('Create interfaces and implement them using implements keyword');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('java', ['-cp', this.tempDir, className]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your interface implementation');
                            score = 40;
                        }
                    }
                    break;

                // Legacy level number support
                case 2: // Variablen und Datentypen

                case 2: // Variablen und Datentypen
                    if (!code.includes('int ') && !code.includes('String ') && !code.includes('boolean ') && !code.includes('double ')) {
                        errors.push('Missing variable declarations with types');
                        suggestions.push('Declare variables with proper types like int, String, boolean, double');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('java', ['-cp', this.tempDir, className]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your variable declarations and types');
                            score = 40;
                        }
                    }
                    break;

                case 3: // Arrays
                    if (!code.includes('[]') && !code.includes('new ')) {
                        errors.push('Missing array declaration');
                        suggestions.push('Create arrays using type[] arrayName = new type[size] or type[] arrayName = {values}');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('java', ['-cp', this.tempDir, className]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your array syntax and initialization');
                            score = 40;
                        }
                    }
                    break;

                case 4: // Kontrollstrukturen
                    if (!code.includes('if') && !code.includes('for') && !code.includes('while')) {
                        errors.push('Missing control structures (if, for, while)');
                        suggestions.push('Use control structures like if statements or loops');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('java', ['-cp', this.tempDir, className]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your control structure syntax');
                            score = 40;
                        }
                    }
                    break;

                case 5: // Methoden
                    if (!code.includes('public static') || code.split('public static').length < 3) { // main + custom method
                        errors.push('Missing custom method declaration');
                        suggestions.push('Define a custom method using public static returnType methodName() { ... }');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('java', ['-cp', this.tempDir, className]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your method syntax and calls');
                            score = 40;
                        }
                    }
                    break;

                case 6: // Klassen und Objekte
                    if (!code.includes('new ') || !code.includes('class ')) {
                        errors.push('Missing class instantiation or class definition');
                        suggestions.push('Create objects using new ClassName() and define classes');
                        score = 20;
                    } else {
                        const execResult = await this.runCommand('java', ['-cp', this.tempDir, className]);
                        if (execResult.exitCode === 0) {
                            passed = true;
                            score = 100;
                        } else {
                            errors.push('Runtime error: ' + execResult.stderr);
                            suggestions.push('Check your class and object syntax');
                            score = 40;
                        }
                    }
                    break;

                default:
                    // Generic Java validation - just check if it compiles and runs
                    const execResult = await this.runCommand('java', ['-cp', this.tempDir, className]);
                    if (execResult.exitCode === 0) {
                        passed = true;
                        score = 100;
                    } else {
                        errors.push('Runtime error: ' + execResult.stderr);
                        suggestions.push('Fix the runtime errors in your Java code');
                        score = 20;
                    }
            }

        } catch (error) {
            errors.push('Validation error: ' + error.message);
            suggestions.push('Check your Java installation and code syntax');
            score = 0;
        } finally {
            // Clean up temp files
            if (fs.existsSync(tempFile)) {
                fs.unlinkSync(tempFile);
            }
            const classFile = path.join(this.tempDir, `${className}.class`);
            if (fs.existsSync(classFile)) {
                fs.unlinkSync(classFile);
            }
        }

        let message;
        if (passed) {
            message = 'Perfect! Your Java code compiles and runs correctly.';
        } else if (score >= 60) {
            message = 'Your Java code has some issues but is partially correct.';
        } else {
            message = 'Your Java code needs significant improvements.';
        }

        return { passed, score, message, errors, suggestions };
    }

    /**
     * Check if Java compiler is available
     */
    async checkJavaAvailability() {
        try {
            const result = await this.runCommand('javac', ['-version']);
            return result.exitCode === 0 || result.stderr.includes('javac');
        } catch (error) {
            return false;
        }
    }

    /**
     * Validate Java code using syntax-only approach when compiler is not available
     */
    validateJavaSyntaxOnly(code, levelNumber, level) {
        const errors = [];
        const suggestions = [];
        let score = 0;
        let passed = false;
        const expectedOutput = level?.expected_output || '';

        console.log(`☕ Java syntax-only validation - Level ${levelNumber}`);

        // Basic Java syntax checks
        const hasClass = /\b(public\s+)?class\s+\w+/.test(code);
        const hasMain = /public\s+static\s+void\s+main\s*\(\s*String\s*\[\s*\]\s*\w+\s*\)/.test(code);
        const hasPrintStatement = /System\.out\.(println|print)\s*\(/.test(code);
        const hasProperSemicolons = !code.includes('System.out.println') || code.includes('System.out.println(') && code.includes(');');
        const hasJavaKeywords = /\b(public|private|protected|static|void|int|String|boolean|double|class)\b/.test(code);

        // Validation based on expected output
        switch (expectedOutput) {
            case 'java_hello':
                if (hasPrintStatement && hasJavaKeywords) {
                    passed = true;
                    score = 90; // High score for syntax-only validation
                } else if (hasJavaKeywords) {
                    passed = true;
                    score = 70;
                    suggestions.push('Add System.out.println() to output text');
                } else {
                    passed = false;
                    errors.push('Missing Java syntax elements');
                    suggestions.push('Use Java syntax like System.out.println("Hello World");');
                    score = 30;
                }
                break;

            case 'java_variables':
                const hasVariables = /\b(int|String|boolean|double)\s+\w+\s*=/.test(code);
                if (hasVariables && hasJavaKeywords) {
                    passed = true;
                    score = 90;
                } else if (hasJavaKeywords) {
                    passed = true;
                    score = 60;
                    suggestions.push('Declare variables with types like: int number = 5;');
                } else {
                    passed = false;
                    errors.push('Missing variable declarations');
                    suggestions.push('Declare variables with proper Java types');
                    score = 20;
                }
                break;

            default:
                // Generic Java validation
                if (hasJavaKeywords && hasProperSemicolons) {
                    passed = true;
                    score = 85;
                } else if (hasJavaKeywords) {
                    passed = true;
                    score = 70;
                    suggestions.push('Check your semicolons and syntax');
                } else {
                    passed = false;
                    errors.push('Code does not appear to be valid Java');
                    suggestions.push('Use proper Java syntax with keywords like public, class, static, void');
                    score = 20;
                }
        }

        const message = passed ?
            'Good Java syntax! (Note: Code was not compiled due to system limitations)' :
            'Java syntax needs improvement';

        return { passed, score, message, errors, suggestions };
    }

    /**
     * Check if a Java compilation error is a simple syntax error
     */
    isSimpleJavaSyntaxError(errorMessage) {
        const simpleErrors = [
            'expected',
            'missing semicolon',
            'cannot find symbol',
            'illegal start of expression',
            'reached end of file while parsing'
        ];

        return simpleErrors.some(error => errorMessage.toLowerCase().includes(error.toLowerCase()));
    }

    /**
     * Helper method to run shell commands
     */
    runCommand(command, args, options = {}) {
        return new Promise((resolve) => {
            const process = spawn(command, args, {
                cwd: options.cwd || this.tempDir,
                timeout: 10000, // 10 second timeout
                ...options
            });

            let stdout = '';
            let stderr = '';

            process.stdout?.on('data', (data) => {
                stdout += data.toString();
            });

            process.stderr?.on('data', (data) => {
                stderr += data.toString();
            });

            process.on('close', (code) => {
                resolve({
                    exitCode: code,
                    stdout: stdout.trim(),
                    stderr: stderr.trim()
                });
            });

            process.on('error', (error) => {
                resolve({
                    exitCode: 1,
                    stdout: '',
                    stderr: error.message
                });
            });
        });
    }

    /**
     * Generic validation for unknown code types
     */
    validateGeneric(code, levelNumber, level) {
        const minLength = 20;
        const score = code.length >= minLength ? 80 : Math.max(20, (code.length / minLength) * 80);
        const passed = score >= 80;

        return {
            passed,
            score: Math.round(score),
            message: passed ? 'Code submitted successfully!' : 'Your code is too short. Please provide a more complete solution.',
            errors: passed ? [] : ['Code is too short'],
            suggestions: passed ? [] : ['Write a more detailed solution']
        };
    }

    /**
     * Validate CSS syntax for common errors
     */
    validateCSSyntax(css) {
        const errors = [];

        // Check for missing semicolons in property declarations
        const ruleRegex = /([^{]+)\{([^}]+)\}/g;
        let match;

        while ((match = ruleRegex.exec(css)) !== null) {
            const declarations = match[2].trim();

            // Check for multiple properties on same line without proper separation
            // This catches cases like "color: red font-size: 24px" (missing semicolon between properties)
            const colonCount = (declarations.match(/:/g) || []).length;
            const semicolonCount = (declarations.match(/;/g) || []).length;

            // If we have multiple colons but not enough semicolons to separate them properly
            if (colonCount > 1) {
                // Split by semicolons and check each part
                const parts = declarations.split(';');
                for (const part of parts) {
                    const trimmedPart = part.trim();
                    if (trimmedPart.length === 0) continue;

                    // Count colons in this part
                    const partColonCount = (trimmedPart.match(/:/g) || []).length;
                    if (partColonCount > 1) {
                        errors.push(`Multiple properties on one line need semicolons: "${trimmedPart}"`);
                    }
                }
            }
        }

        return errors;
    }

    /**
     * Simple CSS parser to extract rules and properties
     */
    parseCSS(css) {
        const rules = [];
        const ruleRegex = /([^{]+)\{([^}]+)\}/g;
        let match;

        while ((match = ruleRegex.exec(css)) !== null) {
            const selector = match[1].trim();
            const declarations = match[2].trim();
            
            const properties = [];
            const propRegex = /([^:]+):([^;]+);?/g;
            let propMatch;
            
            while ((propMatch = propRegex.exec(declarations)) !== null) {
                properties.push({
                    property: propMatch[1].trim(),
                    value: propMatch[2].trim()
                });
            }
            
            rules.push({
                selector,
                properties
            });
        }

        return rules;
    }
}

module.exports = CodeValidator;
