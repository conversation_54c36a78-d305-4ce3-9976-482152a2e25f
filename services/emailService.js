const { Resend } = require('resend');

class EmailService {
    constructor() {
        // Initialize Resend with API key from environment variable
        this.resend = new Resend("re_YrC7AiYy_AU4qY6RAomG4bwAQPoghXkg6");
        this.fromEmail = process.env.FROM_EMAIL || '<EMAIL>';
        this.baseUrl = process.env.BASE_URL || 'https://codewave.online';
    }

    async sendEmailVerification(email, username, token) {
        const verificationUrl = `${this.baseUrl}/verify-email?token=${token}`;
        
        try {
            const { data, error } = await this.resend.emails.send({
                from: this.fromEmail,
                to: [email],
                subject: 'CodeWave - Verify Email',
                html: this.getEmailVerificationTemplate(username, verificationUrl)
            });

            if (error) {
                console.error('Error sending verification email:', error);
                throw new Error('Failed to send verification email');
            }

            console.log('Verification email sent successfully:', data);
            return data;
        } catch (error) {
            console.error('Error in sendEmailVerification:', error);
            throw error;
        }
    }

    async sendPasswordReset(email, username, token) {
        const resetUrl = `${this.baseUrl}/reset-password?token=${token}`;
        
        try {
            const { data, error } = await this.resend.emails.send({
                from: this.fromEmail,
                to: [email],
                subject: 'CodeWave - Reset Password',
                html: this.getPasswordResetTemplate(username, resetUrl)
            });

            if (error) {
                console.error('Error sending password reset email:', error);
                throw new Error('Failed to send password reset email');
            }

            console.log('Password reset email sent successfully:', data);
            return data;
        } catch (error) {
            console.error('Error in sendPasswordReset:', error);
            throw error;
        }
    }

    async sendAdminEmail(fromEmail, toEmail, subject, message) {
        try {
            const { data, error } = await this.resend.emails.send({
                from: fromEmail,
                to: [toEmail],
                subject: subject,
                html: this.getAdminEmailTemplate(message, fromEmail)
            });

            if (error) {
                console.error('Error sending admin email:', error);
                throw new Error('Failed to send admin email');
            }

            console.log('Admin email sent successfully:', data);
            return data;
        } catch (error) {
            console.error('Error in sendAdminEmail:', error);
            throw error;
        }
    }

    getEmailVerificationTemplate(username, verificationUrl) {
        return `
        <!DOCTYPE html>
        <html lang="de">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Email-Adresse bestätigen - CodeWave</title>
            <style>
                * { margin: 0; padding: 0; box-sizing: border-box; }
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
                    line-height: 1.6;
                    color: #2d3748;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    padding: 40px 20px;
                }
                .email-container {
                    max-width: 600px;
                    margin: 0 auto;
                    background: white;
                    border-radius: 16px;
                    overflow: hidden;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                }
                .header {
                    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
                    color: white;
                    padding: 40px 30px;
                    text-align: center;
                    position: relative;
                }
                .header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="20" cy="80" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
                }
                .logo {
                    font-size: 32px;
                    font-weight: 800;
                    margin-bottom: 8px;
                    position: relative;
                    z-index: 1;
                }
                .tagline {
                    font-size: 16px;
                    opacity: 0.9;
                    position: relative;
                    z-index: 1;
                }
                .content {
                    padding: 40px 30px;
                    background: white;
                }
                .greeting {
                    font-size: 24px;
                    font-weight: 600;
                    color: #1a202c;
                    margin-bottom: 20px;
                }
                .message {
                    font-size: 16px;
                    color: #4a5568;
                    margin-bottom: 30px;
                    line-height: 1.7;
                }
                .cta-container {
                    text-align: center;
                    margin: 40px 0;
                }
                .cta-button {
                    display: inline-block;
                    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
                    color: white;
                    padding: 16px 32px;
                    text-decoration: none;
                    border-radius: 12px;
                    font-weight: 600;
                    font-size: 16px;
                    box-shadow: 0 4px 15px rgba(79, 70, 229, 0.4);
                    transition: all 0.3s ease;
                }
                .fallback-link {
                    background: #f7fafc;
                    border: 2px dashed #e2e8f0;
                    border-radius: 8px;
                    padding: 20px;
                    margin: 30px 0;
                    word-break: break-all;
                    font-family: 'Monaco', 'Menlo', monospace;
                    font-size: 14px;
                    color: #4a5568;
                }
                .warning {
                    background: #fef5e7;
                    border-left: 4px solid #f6ad55;
                    padding: 16px 20px;
                    border-radius: 0 8px 8px 0;
                    margin: 30px 0;
                }
                .warning-title {
                    font-weight: 600;
                    color: #c05621;
                    margin-bottom: 4px;
                }
                .warning-text {
                    color: #9c4221;
                    font-size: 14px;
                }
                .footer {
                    background: #f8fafc;
                    padding: 30px;
                    text-align: center;
                    border-top: 1px solid #e2e8f0;
                }
                .footer-text {
                    color: #718096;
                    font-size: 14px;
                    margin-bottom: 10px;
                }
                @media (max-width: 600px) {
                    body { padding: 20px 10px; }
                    .email-container { border-radius: 12px; }
                    .header, .content { padding: 30px 20px; }
                    .logo { font-size: 28px; }
                    .greeting { font-size: 22px; }
                    .cta-button { padding: 14px 28px; }
                }
            </style>
        </head>
        <body>
            <div class="email-container">
                <div class="header">
                    <div class="logo">🚀 CodeWave</div>
                    <div class="tagline">Verify your email address</div>
                </div>

                <div class="content">
                    <div class="greeting">Hi ${username}! 👋</div>

                    <div class="message">
                        Welcome to CodeWave! To get started, please verify your email address.
                    </div>

                    <div class="cta-container">
                        <a href="${verificationUrl}" class="cta-button">
                            ✨ Verify Email
                        </a>
                    </div>

                    <div style="color: #718096; font-size: 14px; text-align: center; margin-bottom: 30px;">
                        If the button above doesn't work, you can also copy and paste the following link into your browser:
                    </div>

                    <div class="fallback-link">${verificationUrl}</div>

                    <div class="warning">
                        <div class="warning-title">⏰ Important Notice</div>
                        <div class="warning-text">
                            This verification link is only valid for 24 hours. If you did not create an account, please ignore this email.
                        </div>
                    </div>
                </div>

                <div class="footer">
                    <div class="footer-text">© 2025 CodeWave - Learn Programming - Step by Step</div>
                    <div class="footer-text">🌐 codewave.online</div>
                </div>
            </div>
        </body>
        </html>
        `;
    }

    getPasswordResetTemplate(username, resetUrl) {
        return `
        <!DOCTYPE html>
        <html lang="de">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Reset Passwort - CodeWave</title>
            <style>
                * { margin: 0; padding: 0; box-sizing: border-box; }
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
                    line-height: 1.6;
                    color: #2d3748;
                    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
                    padding: 40px 20px;
                }
                .email-container {
                    max-width: 600px;
                    margin: 0 auto;
                    background: white;
                    border-radius: 16px;
                    overflow: hidden;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                }
                .header {
                    background: linear-gradient(135deg, #e53e3e 0%, #dd6b20 100%);
                    color: white;
                    padding: 40px 30px;
                    text-align: center;
                    position: relative;
                }
                .header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="20" cy="80" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
                }
                .logo {
                    font-size: 32px;
                    font-weight: 800;
                    margin-bottom: 8px;
                    position: relative;
                    z-index: 1;
                }
                .tagline {
                    font-size: 16px;
                    opacity: 0.9;
                    position: relative;
                    z-index: 1;
                }
                .content {
                    padding: 40px 30px;
                    background: white;
                }
                .greeting {
                    font-size: 24px;
                    font-weight: 600;
                    color: #1a202c;
                    margin-bottom: 20px;
                }
                .message {
                    font-size: 16px;
                    color: #4a5568;
                    margin-bottom: 30px;
                    line-height: 1.7;
                }
                .cta-container {
                    text-align: center;
                    margin: 40px 0;
                }
                .cta-button {
                    display: inline-block;
                    background: linear-gradient(135deg, #e53e3e 0%, #dd6b20 100%);
                    color: white;
                    padding: 16px 32px;
                    text-decoration: none;
                    border-radius: 12px;
                    font-weight: 600;
                    font-size: 16px;
                    box-shadow: 0 4px 15px rgba(229, 62, 62, 0.4);
                    transition: all 0.3s ease;
                }
                .fallback-link {
                    background: #f7fafc;
                    border: 2px dashed #e2e8f0;
                    border-radius: 8px;
                    padding: 20px;
                    margin: 30px 0;
                    word-break: break-all;
                    font-family: 'Monaco', 'Menlo', monospace;
                    font-size: 14px;
                    color: #4a5568;
                }
                .warning {
                    background: #fed7d7;
                    border-left: 4px solid #fc8181;
                    padding: 16px 20px;
                    border-radius: 0 8px 8px 0;
                    margin: 30px 0;
                }
                .warning-title {
                    font-weight: 600;
                    color: #c53030;
                    margin-bottom: 4px;
                }
                .warning-text {
                    color: #9b2c2c;
                    font-size: 14px;
                }
                .footer {
                    background: #f8fafc;
                    padding: 30px;
                    text-align: center;
                    border-top: 1px solid #e2e8f0;
                }
                .footer-text {
                    color: #718096;
                    font-size: 14px;
                    margin-bottom: 10px;
                }
                @media (max-width: 600px) {
                    body { padding: 20px 10px; }
                    .email-container { border-radius: 12px; }
                    .header, .content { padding: 30px 20px; }
                    .logo { font-size: 28px; }
                    .greeting { font-size: 22px; }
                    .cta-button { padding: 14px 28px; }
                }
            </style>
        </head>
        <body>
            <div class="email-container">
                <div class="header">
                    <div class="logo">🔐 CodeWave</div>
                    <div class="tagline">Reset your password</div>
                </div>

                <div class="content">
                    <div class="greeting">Hi ${username}! 🔑</div>

                    <div class="message">
                        You are receiving this email because you requested a password reset for your CodeWave account.
                    </div>

                    <div class="cta-container">
                        <a href="${resetUrl}" class="cta-button">
                            🔄 Create new password
                        </a>
                    </div>

                    <div style="color: #718096; font-size: 14px; text-align: center; margin-bottom: 30px;">
                        If the button above doesn't work, you can also copy and paste the following link into your browser:
                    </div>

                    <div class="fallback-link">${resetUrl}</div>

                    <div class="warning">
                        <div class="warning-title">⚠️ Sicherheitshinweis</div>
                        <div class="warning-text">
                            This reset link is only valid for 1 hour. If you did not request a password reset, please ignore this email.
                        </div>
                    </div>
                </div>

                <div class="footer">
                    <div class="footer-text">© 2025 CodeWave - Learn Programming - Step by Step</div>
                    <div class="footer-text">🌐 codewave.online</div>
                </div>
            </div>
        </body>
        </html>
        `;
    }

    getAdminEmailTemplate(message, fromEmail) {
        return `
        <!DOCTYPE html>
        <html lang="de">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Nachricht von CodeWave</title>
            <style>
                * { margin: 0; padding: 0; box-sizing: border-box; }
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
                    line-height: 1.6;
                    color: #2d3748;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    padding: 40px 20px;
                }
                .email-container {
                    max-width: 600px;
                    margin: 0 auto;
                    background: white;
                    border-radius: 16px;
                    overflow: hidden;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                }
                .header {
                    background: linear-gradient(135deg, #38b2ac 0%, #319795 100%);
                    color: white;
                    padding: 40px 30px;
                    text-align: center;
                    position: relative;
                }
                .header::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="20" cy="80" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
                }
                .logo {
                    font-size: 32px;
                    font-weight: 800;
                    margin-bottom: 8px;
                    position: relative;
                    z-index: 1;
                }
                .tagline {
                    font-size: 16px;
                    opacity: 0.9;
                    position: relative;
                    z-index: 1;
                }
                .content {
                    padding: 40px 30px;
                    background: white;
                }
                .message-container {
                    background: #f7fafc;
                    border-left: 4px solid #38b2ac;
                    border-radius: 0 12px 12px 0;
                    padding: 30px;
                    margin: 30px 0;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
                }
                .message-content {
                    font-size: 16px;
                    color: #2d3748;
                    line-height: 1.7;
                    white-space: pre-wrap;
                }
                .sender-info {
                    background: #edf2f7;
                    border-radius: 8px;
                    padding: 16px 20px;
                    margin-top: 30px;
                    border: 1px solid #e2e8f0;
                }
                .sender-label {
                    font-size: 12px;
                    font-weight: 600;
                    color: #718096;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    margin-bottom: 4px;
                }
                .sender-email {
                    font-size: 14px;
                    color: #4a5568;
                    font-weight: 500;
                }
                .footer {
                    background: #f8fafc;
                    padding: 30px;
                    text-align: center;
                    border-top: 1px solid #e2e8f0;
                }
                .footer-text {
                    color: #718096;
                    font-size: 14px;
                    margin-bottom: 10px;
                }
                @media (max-width: 600px) {
                    body { padding: 20px 10px; }
                    .email-container { border-radius: 12px; }
                    .header, .content { padding: 30px 20px; }
                    .logo { font-size: 28px; }
                    .message-container { padding: 20px; }
                }
            </style>
        </head>
        <body>
            <div class="email-container">
                <div class="header">
                    <div class="logo">📧 CodeWave</div>
                    <div class="tagline">Message by CodeWave Adminstration</div>
                </div>

                <div class="content">
                    <div class="message-container">
                        <div class="message-content">${message.replace(/\n/g, '<br>')}</div>
                    </div>

                    <div class="sender-info">
                        <div class="sender-label">Sent by</div>
                        <div class="sender-email">${fromEmail}</div>
                    </div>
                </div>

                <div class="footer">
                    <div class="footer-text">© 2025 CodeWave - Learn Programming - Step by Step</div>
                    <div class="footer-text">🌐 codewave.online</div>
                </div>
            </div>
        </body>
        </html>
        `;
    }
}

module.exports = EmailService;
