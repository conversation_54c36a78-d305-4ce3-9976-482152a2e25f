const CodeValidator = require('./services/codeValidatorFixed');

console.log('🧪 Testing Code Validation Fixes...\n');

const validator = new CodeValidator();

async function runTests() {
    const testCases = [
        // HTML Detection Problem Tests
        {
            name: 'HTML False Positive Fix - Simple Text',
            code: 'Hello World',
            level: { level_number: 1, expected_output: 'html_structure' },
            courseSlug: 'html-css-js',
            expectedPass: false,
            description: 'Should not detect HTML in plain text'
        },
        {
            name: 'HTML False Positive Fix - Math Expression',
            code: 'if (x < 5 && y > 3) { console.log("test"); }',
            level: { level_number: 15, expected_output: 'js_conditions' },
            courseSlug: 'html-css-js',
            expectedPass: true,
            description: 'Should detect JavaScript, not HTML due to < > operators'
        },
        {
            name: 'HTML True Positive - Valid HTML',
            code: '<h1>Hello World</h1><p>This is a paragraph</p>',
            level: { level_number: 1, expected_output: 'html_structure' },
            courseSlug: 'html-css-js',
            expectedPass: true,
            description: 'Should correctly detect valid HTML'
        },
        
        // JavaScript/Java Confusion Tests
        {
            name: 'JavaScript Detection - Arrow Function',
            code: 'const greet = (name) => { console.log("Hello " + name); }; greet("World");',
            level: { level_number: 14, expected_output: 'js_functions' },
            courseSlug: 'html-css-js',
            expectedPass: true,
            description: 'Should detect JavaScript, not Java'
        },
        {
            name: 'Java Detection - Class Structure',
            code: 'public class HelloWorld { public static void main(String[] args) { System.out.println("Hello World"); } }',
            level: { level_number: 1, expected_output: 'java_hello' },
            courseSlug: 'java',
            expectedPass: true,
            description: 'Should detect Java correctly'
        },
        
        // Java Enterprise Error Fix Tests
        {
            name: 'Java Syntax Only - Hello World',
            code: 'System.out.println("Hello World");',
            level: { level_number: 1, expected_output: 'java_hello' },
            courseSlug: 'java',
            expectedPass: true,
            description: 'Should pass with syntax-only validation when compiler unavailable'
        },
        {
            name: 'Java Syntax Only - Variables',
            code: 'int number = 42; String name = "Java"; System.out.println(name + " " + number);',
            level: { level_number: 2, expected_output: 'java_variables' },
            courseSlug: 'java',
            expectedPass: true,
            description: 'Should validate Java variable syntax without compilation'
        },
        
        // Expected Output Validation Tests
        {
            name: 'Expected Output - HTML Structure',
            code: '<h1>My Title</h1><p>My paragraph</p>',
            level: { level_number: 1, expected_output: 'html_structure' },
            courseSlug: 'html-css-js',
            expectedPass: true,
            description: 'Should validate based on expected_output field'
        },
        {
            name: 'Expected Output - JavaScript Variables',
            code: 'let name = "John"; const age = 25; console.log(name, age);',
            level: { level_number: 11, expected_output: 'js_variables' },
            courseSlug: 'html-css-js',
            expectedPass: true,
            description: 'Should validate JavaScript variables correctly'
        }
    ];

    let passedTests = 0;
    let totalTests = testCases.length;

    for (const testCase of testCases) {
        console.log(`\n🔍 Testing: ${testCase.name}`);
        console.log(`   Description: ${testCase.description}`);
        console.log(`   Course: ${testCase.courseSlug}, Level: ${testCase.level.level_number}`);
        console.log(`   Expected Output: ${testCase.level.expected_output}`);
        console.log(`   Code: ${testCase.code.substring(0, 60)}${testCase.code.length > 60 ? '...' : ''}`);

        try {
            const result = await validator.validateCode(testCase.code, testCase.level, testCase.courseSlug);
            
            const actualPass = result.passed;
            const testPassed = actualPass === testCase.expectedPass;
            
            if (testPassed) {
                passedTests++;
                console.log(`   ✅ PASS - Expected: ${testCase.expectedPass}, Got: ${actualPass}`);
            } else {
                console.log(`   ❌ FAIL - Expected: ${testCase.expectedPass}, Got: ${actualPass}`);
            }
            
            console.log(`   Score: ${result.score}`);
            console.log(`   Message: ${result.message}`);
            
            if (result.errors && result.errors.length > 0) {
                console.log(`   Errors: ${result.errors.join(', ')}`);
            }
            
            if (result.suggestions && result.suggestions.length > 0) {
                console.log(`   Suggestions: ${result.suggestions.join(', ')}`);
            }

        } catch (error) {
            console.log(`   ❌ ERROR - ${error.message}`);
        }
    }

    console.log(`\n📊 Test Results:`);
    console.log(`   Passed: ${passedTests}/${totalTests}`);
    console.log(`   Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
    
    if (passedTests === totalTests) {
        console.log(`\n🎉 All validation fixes are working correctly!`);
    } else {
        console.log(`\n⚠️ Some tests failed. Review the validation logic.`);
    }
}

// Run the tests
runTests().catch(console.error);
