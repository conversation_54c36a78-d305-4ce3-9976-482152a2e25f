const sqlite3 = require('sqlite3').verbose();
const { dbPath } = require('./database/init');

console.log('🚀 Creating Final Levels (30-40) and All Other Courses...');

const directDb = new sqlite3.Database(dbPath);

directDb.serialize(() => {
    const levelStmt = directDb.prepare(`INSERT INTO levels (course_id, level_number, title, description, content, exercise_type, expected_output, points) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`);
    
    // Level 30: Boss Level für HTML/CSS/JS
    const bossLevel30 = {
        course_id: 1, level_number: 30, title: '🏆 BOSS: Portfolio Website', 
        description: 'Erstelle eine vollständige Portfolio-Website mit allen gelernten Techniken.',
        content: `# 🏆 BOSS LEVEL: Portfolio Website

Erstelle deine professionelle Portfolio-Website!

## Anforderungen:
- ✅ Responsive Design (Mobile + Desktop)
- ✅ CSS Grid oder Flexbox Layout
- ✅ Animationen und Transitions
- ✅ Kontaktformular mit Validierung
- ✅ Semantic HTML5
- ✅ JavaScript Interaktivität
- ✅ LocalStorage für Einstellungen

## Bereiche:
- Header mit Navigation
- Hero-Bereich mit Vorstellung
- Portfolio-Galerie
- Über mich Sektion
- Kontaktformular
- Footer

Zeige alle deine Fähigkeiten! 🚀`,
        exercise_type: 'project',
        expected_output: 'boss_portfolio',
        points: 150
    };
    
    levelStmt.run(bossLevel30.course_id, bossLevel30.level_number, bossLevel30.title, bossLevel30.description, bossLevel30.content, bossLevel30.exercise_type, bossLevel30.expected_output, bossLevel30.points);
    
    // Level 31-39: Advanced JavaScript & Modern Web Development
    const modernWebLevels = [
        {
            course_id: 1, level_number: 31, title: 'Fetch API & AJAX', 
            description: 'Lade Daten von APIs mit der modernen Fetch API.',
            content: `# Fetch API & AJAX

Moderne Datenübertragung mit JavaScript:

\`\`\`javascript
// Einfacher GET Request
fetch('https://jsonplaceholder.typicode.com/posts/1')
    .then(response => response.json())
    .then(data => {
        console.log(data);
        document.getElementById('content').innerHTML = data.title;
    })
    .catch(error => console.error('Error:', error));

// POST Request
const postData = {
    title: 'Mein Post',
    body: 'Das ist der Inhalt',
    userId: 1
};

fetch('https://jsonplaceholder.typicode.com/posts', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify(postData)
})
.then(response => response.json())
.then(data => console.log('Success:', data));

// Async/Await Syntax
async function loadData() {
    try {
        const response = await fetch('https://api.example.com/data');
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('Error:', error);
    }
}
\`\`\`

## Aufgabe
Lade Daten von einer API und zeige sie auf der Webseite an.`,
            exercise_type: 'code_example',
            expected_output: 'js_fetch',
            points: 20
        },
        {
            course_id: 1, level_number: 32, title: 'ES6+ Features', 
            description: 'Verwende moderne JavaScript-Features.',
            content: `# ES6+ Features

Moderne JavaScript-Syntax und Features:

\`\`\`javascript
// Arrow Functions
const add = (a, b) => a + b;
const greet = name => \`Hello \${name}!\`;

// Template Literals
const name = "Max";
const age = 25;
const message = \`Hallo, ich bin \${name} und \${age} Jahre alt.\`;

// Destructuring
const person = { name: "Anna", age: 30, city: "Berlin" };
const { name, age } = person;

const numbers = [1, 2, 3, 4, 5];
const [first, second, ...rest] = numbers;

// Spread Operator
const arr1 = [1, 2, 3];
const arr2 = [4, 5, 6];
const combined = [...arr1, ...arr2];

const obj1 = { a: 1, b: 2 };
const obj2 = { c: 3, d: 4 };
const merged = { ...obj1, ...obj2 };

// Default Parameters
function greetUser(name = "Gast", greeting = "Hallo") {
    return \`\${greeting}, \${name}!\`;
}

// Classes
class Person {
    constructor(name, age) {
        this.name = name;
        this.age = age;
    }
    
    introduce() {
        return \`Ich bin \${this.name} und \${this.age} Jahre alt.\`;
    }
}

const person1 = new Person("Max", 25);
\`\`\`

## Aufgabe
Erstelle eine Klasse "Car" mit Eigenschaften und Methoden unter Verwendung von ES6+ Features.`,
            exercise_type: 'code_example',
            expected_output: 'js_es6',
            points: 20
        },
        {
            course_id: 1, level_number: 33, title: 'Module & Import/Export', 
            description: 'Organisiere deinen Code mit JavaScript-Modulen.',
            content: `# Module & Import/Export

Organisiere deinen Code in wiederverwendbare Module:

\`\`\`javascript
// math.js - Export einzelner Funktionen
export function add(a, b) {
    return a + b;
}

export function multiply(a, b) {
    return a * b;
}

export const PI = 3.14159;

// utils.js - Default Export
export default class Utils {
    static formatDate(date) {
        return date.toLocaleDateString('de-DE');
    }
    
    static capitalize(str) {
        return str.charAt(0).toUpperCase() + str.slice(1);
    }
}

// main.js - Import verwenden
import { add, multiply, PI } from './math.js';
import Utils from './utils.js';

console.log(add(5, 3)); // 8
console.log(multiply(4, 7)); // 28
console.log(PI); // 3.14159

const today = new Date();
console.log(Utils.formatDate(today));
console.log(Utils.capitalize('hello world'));

// Alle Exports importieren
import * as MathFunctions from './math.js';
console.log(MathFunctions.add(2, 3));

// Dynamic Imports
async function loadModule() {
    const module = await import('./math.js');
    console.log(module.add(1, 2));
}
\`\`\`

## Aufgabe
Erstelle ein Modul mit Hilfsfunktionen und importiere es in eine andere Datei.`,
            exercise_type: 'code_example',
            expected_output: 'js_modules',
            points: 20
        },
        {
            course_id: 1, level_number: 34, title: 'Web Components', 
            description: 'Erstelle wiederverwendbare Web Components.',
            content: `# Web Components

Erstelle eigene HTML-Elemente:

\`\`\`javascript
// Custom Element definieren
class MyButton extends HTMLElement {
    constructor() {
        super();
        
        // Shadow DOM erstellen
        this.attachShadow({ mode: 'open' });
        
        // Template erstellen
        this.shadowRoot.innerHTML = \`
            <style>
                button {
                    background: var(--button-color, #007bff);
                    color: white;
                    border: none;
                    padding: 10px 20px;
                    border-radius: 5px;
                    cursor: pointer;
                    font-size: 16px;
                }
                
                button:hover {
                    opacity: 0.8;
                }
            </style>
            <button>
                <slot></slot>
            </button>
        \`;
        
        // Event Listener
        this.shadowRoot.querySelector('button').addEventListener('click', () => {
            this.dispatchEvent(new CustomEvent('my-click', {
                detail: { message: 'Button clicked!' }
            }));
        });
    }
    
    // Attribute beobachten
    static get observedAttributes() {
        return ['color'];
    }
    
    attributeChangedCallback(name, oldValue, newValue) {
        if (name === 'color') {
            this.style.setProperty('--button-color', newValue);
        }
    }
}

// Element registrieren
customElements.define('my-button', MyButton);

// Verwendung in HTML:
// <my-button color="#28a745">Klick mich!</my-button>
\`\`\`

## Aufgabe
Erstelle ein Custom Element für eine Bewertung mit Sternen.`,
            exercise_type: 'code_example',
            expected_output: 'js_webcomponents',
            points: 20
        },
        {
            course_id: 1, level_number: 35, title: 'Progressive Web App (PWA)', 
            description: 'Mache deine Webseite zu einer installierbaren App.',
            content: `# Progressive Web App (PWA)

Verwandle deine Webseite in eine App:

\`\`\`json
// manifest.json
{
    "name": "Meine PWA",
    "short_name": "PWA",
    "description": "Eine tolle Progressive Web App",
    "start_url": "/",
    "display": "standalone",
    "background_color": "#ffffff",
    "theme_color": "#000000",
    "icons": [
        {
            "src": "icon-192.png",
            "sizes": "192x192",
            "type": "image/png"
        },
        {
            "src": "icon-512.png",
            "sizes": "512x512",
            "type": "image/png"
        }
    ]
}
\`\`\`

\`\`\`javascript
// Service Worker (sw.js)
const CACHE_NAME = 'pwa-cache-v1';
const urlsToCache = [
    '/',
    '/styles.css',
    '/script.js',
    '/offline.html'
];

// Installation
self.addEventListener('install', event => {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => cache.addAll(urlsToCache))
    );
});

// Fetch Events
self.addEventListener('fetch', event => {
    event.respondWith(
        caches.match(event.request)
            .then(response => {
                return response || fetch(event.request);
            })
    );
});

// main.js - Service Worker registrieren
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.register('/sw.js')
        .then(registration => console.log('SW registered'))
        .catch(error => console.log('SW registration failed'));
}
\`\`\`

## Aufgabe
Erstelle eine einfache PWA mit Manifest und Service Worker.`,
            exercise_type: 'code_example',
            expected_output: 'js_pwa',
            points: 20
        },
        {
            course_id: 1, level_number: 36, title: 'Canvas & SVG Graphics', 
            description: 'Erstelle Grafiken und Animationen mit Canvas und SVG.',
            content: `# Canvas & SVG Graphics

Erstelle interaktive Grafiken:

\`\`\`html
<!-- Canvas -->
<canvas id="myCanvas" width="400" height="300"></canvas>

<!-- SVG -->
<svg width="400" height="300">
    <circle cx="50" cy="50" r="40" fill="red" />
    <rect x="100" y="100" width="80" height="60" fill="blue" />
    <line x1="0" y1="0" x2="200" y2="200" stroke="green" stroke-width="2" />
</svg>
\`\`\`

\`\`\`javascript
// Canvas JavaScript
const canvas = document.getElementById('myCanvas');
const ctx = canvas.getContext('2d');

// Rechteck zeichnen
ctx.fillStyle = 'blue';
ctx.fillRect(10, 10, 100, 50);

// Kreis zeichnen
ctx.beginPath();
ctx.arc(200, 100, 30, 0, 2 * Math.PI);
ctx.fillStyle = 'red';
ctx.fill();

// Text zeichnen
ctx.font = '20px Arial';
ctx.fillStyle = 'black';
ctx.fillText('Hello Canvas!', 50, 200);

// Animation
let x = 0;
function animate() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    ctx.fillStyle = 'green';
    ctx.fillRect(x, 250, 50, 30);
    
    x += 2;
    if (x > canvas.width) x = -50;
    
    requestAnimationFrame(animate);
}
animate();

// SVG mit JavaScript manipulieren
const svg = document.querySelector('svg');
const circle = svg.querySelector('circle');

circle.addEventListener('click', () => {
    const currentRadius = circle.getAttribute('r');
    circle.setAttribute('r', currentRadius == 40 ? 60 : 40);
});
\`\`\`

## Aufgabe
Erstelle eine interaktive Zeichnung mit Canvas oder SVG.`,
            exercise_type: 'code_example',
            expected_output: 'js_graphics',
            points: 20
        },
        {
            course_id: 1, level_number: 37, title: 'WebSockets & Real-time', 
            description: 'Implementiere Echtzeit-Kommunikation mit WebSockets.',
            content: `# WebSockets & Real-time

Echtzeit-Kommunikation zwischen Client und Server:

\`\`\`javascript
// Client-Side WebSocket
const socket = new WebSocket('ws://localhost:8080');

// Verbindung geöffnet
socket.addEventListener('open', event => {
    console.log('WebSocket verbunden');
    socket.send('Hallo Server!');
});

// Nachricht empfangen
socket.addEventListener('message', event => {
    console.log('Nachricht vom Server:', event.data);
    
    const messageDiv = document.createElement('div');
    messageDiv.textContent = event.data;
    document.getElementById('messages').appendChild(messageDiv);
});

// Verbindung geschlossen
socket.addEventListener('close', event => {
    console.log('WebSocket getrennt');
});

// Fehler
socket.addEventListener('error', error => {
    console.error('WebSocket Fehler:', error);
});

// Chat-Funktionalität
function sendMessage() {
    const input = document.getElementById('messageInput');
    const message = input.value;
    
    if (message.trim()) {
        socket.send(JSON.stringify({
            type: 'chat',
            message: message,
            timestamp: new Date().toISOString()
        }));
        input.value = '';
    }
}

// Heartbeat für Verbindung aufrechterhalten
setInterval(() => {
    if (socket.readyState === WebSocket.OPEN) {
        socket.send(JSON.stringify({ type: 'ping' }));
    }
}, 30000);
\`\`\`

\`\`\`html
<div id="messages"></div>
<input type="text" id="messageInput" placeholder="Nachricht eingeben...">
<button onclick="sendMessage()">Senden</button>
\`\`\`

## Aufgabe
Erstelle eine einfache Chat-Oberfläche mit WebSocket-Simulation.`,
            exercise_type: 'code_example',
            expected_output: 'js_websockets',
            points: 20
        },
        {
            course_id: 1, level_number: 38, title: 'Performance Optimization', 
            description: 'Optimiere deine Webseite für beste Performance.',
            content: `# Performance Optimization

Mache deine Webseite schnell und effizient:

\`\`\`javascript
// Lazy Loading für Bilder
const images = document.querySelectorAll('img[data-src]');
const imageObserver = new IntersectionObserver((entries, observer) => {
    entries.forEach(entry => {
        if (entry.isIntersecting) {
            const img = entry.target;
            img.src = img.dataset.src;
            img.classList.remove('lazy');
            observer.unobserve(img);
        }
    });
});

images.forEach(img => imageObserver.observe(img));

// Debouncing für Suchfunktionen
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

const searchInput = document.getElementById('search');
const debouncedSearch = debounce(performSearch, 300);
searchInput.addEventListener('input', debouncedSearch);

// Virtual Scrolling für große Listen
class VirtualList {
    constructor(container, items, itemHeight) {
        this.container = container;
        this.items = items;
        this.itemHeight = itemHeight;
        this.visibleItems = Math.ceil(container.clientHeight / itemHeight) + 1;
        this.startIndex = 0;
        
        this.render();
        container.addEventListener('scroll', this.onScroll.bind(this));
    }
    
    render() {
        const visibleItems = this.items.slice(
            this.startIndex, 
            this.startIndex + this.visibleItems
        );
        
        this.container.innerHTML = visibleItems.map((item, index) => 
            \`<div style="height: \${this.itemHeight}px">\${item}</div>\`
        ).join('');
    }
    
    onScroll() {
        const scrollTop = this.container.scrollTop;
        const newStartIndex = Math.floor(scrollTop / this.itemHeight);
        
        if (newStartIndex !== this.startIndex) {
            this.startIndex = newStartIndex;
            this.render();
        }
    }
}

// Web Workers für schwere Berechnungen
const worker = new Worker('worker.js');
worker.postMessage({ numbers: [1, 2, 3, 4, 5] });
worker.onmessage = event => {
    console.log('Ergebnis:', event.data);
};
\`\`\`

## Aufgabe
Implementiere Lazy Loading für Bilder und Debouncing für eine Suchfunktion.`,
            exercise_type: 'code_example',
            expected_output: 'js_performance',
            points: 20
        },
        {
            course_id: 1, level_number: 39, title: 'Testing & Debugging', 
            description: 'Teste und debugge deinen JavaScript-Code professionell.',
            content: `# Testing & Debugging

Professionelle Entwicklung mit Tests und Debugging:

\`\`\`javascript
// Unit Tests mit einfachem Framework
class SimpleTest {
    static assert(condition, message) {
        if (!condition) {
            throw new Error(\`Assertion failed: \${message}\`);
        }
        console.log(\`✅ \${message}\`);
    }
    
    static assertEqual(actual, expected, message) {
        if (actual !== expected) {
            throw new Error(\`\${message}: Expected \${expected}, got \${actual}\`);
        }
        console.log(\`✅ \${message}\`);
    }
}

// Zu testende Funktionen
function add(a, b) {
    return a + b;
}

function multiply(a, b) {
    return a * b;
}

// Tests ausführen
try {
    SimpleTest.assertEqual(add(2, 3), 5, 'Addition test');
    SimpleTest.assertEqual(multiply(4, 5), 20, 'Multiplication test');
    SimpleTest.assert(add(1, 1) > 0, 'Addition returns positive number');
    console.log('🎉 Alle Tests bestanden!');
} catch (error) {
    console.error('❌ Test fehlgeschlagen:', error.message);
}

// Debugging Techniken
function debugExample() {
    const data = [1, 2, 3, 4, 5];
    
    // Console methods
    console.log('Normal log:', data);
    console.warn('Warning message');
    console.error('Error message');
    console.table(data);
    console.group('Grouped logs');
    console.log('Inside group');
    console.groupEnd();
    
    // Performance measuring
    console.time('Operation');
    // ... some operation
    console.timeEnd('Operation');
    
    // Breakpoints (in browser dev tools)
    debugger; // Pausiert hier
    
    return data.map(x => x * 2);
}

// Error Handling
function safeFunction() {
    try {
        // Riskante Operation
        const result = riskyOperation();
        return result;
    } catch (error) {
        console.error('Fehler aufgetreten:', error.message);
        // Fallback oder Recovery
        return null;
    } finally {
        // Cleanup Code
        console.log('Cleanup ausgeführt');
    }
}

// Custom Error Types
class ValidationError extends Error {
    constructor(message) {
        super(message);
        this.name = 'ValidationError';
    }
}

function validateEmail(email) {
    if (!email.includes('@')) {
        throw new ValidationError('Ungültige E-Mail-Adresse');
    }
    return true;
}
\`\`\`

## Aufgabe
Schreibe Tests für eine Funktion und implementiere Error Handling.`,
            exercise_type: 'code_example',
            expected_output: 'js_testing',
            points: 20
        }
    ];
    
    // Insert modern web levels
    for (const level of modernWebLevels) {
        levelStmt.run(level.course_id, level.level_number, level.title, level.description, level.content, level.exercise_type, level.expected_output, level.points);
    }
    
    console.log('✅ Modern Web levels 31-39 created!');
    
    levelStmt.finalize();
    directDb.close();
    
    console.log('🎉 HTML/CSS/JS Course levels 30-39 created successfully!');
});
