const sqlite3 = require('sqlite3').verbose();
const { dbPath } = require('./database/init');

console.log('🚀 Creating Remaining Levels (20-40) for HTML/CSS/JS Course...');

const directDb = new sqlite3.Database(dbPath);

directDb.serialize(() => {
    const levelStmt = directDb.prepare(`INSERT INTO levels (course_id, level_number, title, description, content, exercise_type, expected_output, points) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`);
    
    // Level 20: Boss Level
    const bossLevel20 = {
        course_id: 1, level_number: 20, title: '🏆 BOSS: Interaktive To-Do App', 
        description: 'Erstelle eine vollständige To-Do-Liste mit JavaScript.',
        content: `# 🏆 BOSS LEVEL: Interaktive To-Do App

Erstelle eine funktionsfähige To-Do-Liste mit allen gelernten Techniken!

## Anforderungen:
- ✅ HTML-Struktur mit Eingabefeld und Liste
- ✅ CSS-Styling mit Flexbox
- ✅ JavaScript für Hinzufügen/Entfernen von Aufgaben
- ✅ LocalStorage zum Speichern der Aufgaben
- ✅ Event Handling für Buttons
- ✅ DOM Manipulation

## Beispiel-Funktionen:
- Neue Aufgabe hinzufügen
- Aufgaben als erledigt markieren
- Aufgaben löschen
- Aufgaben bleiben nach Neuladen erhalten

Zeige deine JavaScript-Kenntnisse! 🚀`,
        exercise_type: 'project',
        expected_output: 'boss_todo_app',
        points: 100
    };
    
    levelStmt.run(bossLevel20.course_id, bossLevel20.level_number, bossLevel20.title, bossLevel20.description, bossLevel20.content, bossLevel20.exercise_type, bossLevel20.expected_output, bossLevel20.points);
    
    // Level 21-29: Advanced CSS & Layout
    const advancedCssLevels = [
        {
            course_id: 1, level_number: 21, title: 'CSS Grid Layout', 
            description: 'Erstelle komplexe Layouts mit CSS Grid.',
            content: `# CSS Grid Layout

Grid ist perfekt für 2D-Layouts:

\`\`\`css
.grid-container {
    display: grid;
    grid-template-columns: 1fr 2fr 1fr;
    grid-template-rows: auto 1fr auto;
    grid-gap: 20px;
    height: 100vh;
}

.header { grid-column: 1 / -1; }
.sidebar { grid-row: 2; }
.main { grid-row: 2; }
.aside { grid-row: 2; }
.footer { grid-column: 1 / -1; }
\`\`\`

\`\`\`html
<div class="grid-container">
    <header class="header">Header</header>
    <nav class="sidebar">Sidebar</nav>
    <main class="main">Main Content</main>
    <aside class="aside">Aside</aside>
    <footer class="footer">Footer</footer>
</div>
\`\`\`

## Aufgabe
Erstelle ein Grid-Layout mit Header, Sidebar, Main und Footer.`,
            exercise_type: 'code_example',
            expected_output: 'css_grid',
            points: 15
        },
        {
            course_id: 1, level_number: 22, title: 'Responsive Design', 
            description: 'Mache deine Webseite für alle Geräte optimiert.',
            content: `# Responsive Design

Deine Webseite soll auf allen Geräten gut aussehen:

\`\`\`css
/* Mobile First Approach */
.container {
    width: 100%;
    padding: 10px;
}

/* Tablet */
@media (min-width: 768px) {
    .container {
        max-width: 750px;
        margin: 0 auto;
        padding: 20px;
    }
}

/* Desktop */
@media (min-width: 1024px) {
    .container {
        max-width: 1200px;
        display: grid;
        grid-template-columns: 1fr 3fr;
        gap: 30px;
    }
}

/* Responsive Bilder */
img {
    max-width: 100%;
    height: auto;
}
\`\`\`

## Aufgabe
Erstelle eine responsive Seite mit unterschiedlichen Layouts für Mobile und Desktop.`,
            exercise_type: 'code_example',
            expected_output: 'css_responsive',
            points: 15
        },
        {
            course_id: 1, level_number: 23, title: 'CSS Animationen', 
            description: 'Bringe Leben in deine Webseite mit CSS-Animationen.',
            content: `# CSS Animationen

Animationen machen Webseiten lebendig:

\`\`\`css
/* Transition für sanfte Übergänge */
.button {
    background-color: blue;
    transition: all 0.3s ease;
}

.button:hover {
    background-color: red;
    transform: scale(1.1);
}

/* Keyframe Animation */
@keyframes slideIn {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.slide-element {
    animation: slideIn 1s ease-out;
}

/* Rotation Animation */
@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.spinner {
    animation: rotate 2s linear infinite;
}
\`\`\`

## Aufgabe
Erstelle einen Button mit Hover-Animation und ein Element mit Slide-In-Effekt.`,
            exercise_type: 'code_example',
            expected_output: 'css_animations',
            points: 15
        },
        {
            course_id: 1, level_number: 24, title: 'CSS Variables & Custom Properties', 
            description: 'Verwende CSS-Variablen für wartbaren Code.',
            content: `# CSS Variables

CSS-Variablen machen deinen Code wartbarer:

\`\`\`css
:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --font-size-large: 24px;
    --spacing-medium: 20px;
    --border-radius: 8px;
}

.card {
    background-color: var(--primary-color);
    color: white;
    padding: var(--spacing-medium);
    border-radius: var(--border-radius);
    font-size: var(--font-size-large);
}

.button {
    background-color: var(--secondary-color);
    border: none;
    padding: 10px var(--spacing-medium);
    border-radius: var(--border-radius);
}

/* Dark Theme */
[data-theme="dark"] {
    --primary-color: #2c3e50;
    --secondary-color: #27ae60;
}
\`\`\`

## Aufgabe
Erstelle ein Farbschema mit CSS-Variablen und verwende sie in mehreren Elementen.`,
            exercise_type: 'code_example',
            expected_output: 'css_variables',
            points: 15
        },
        {
            course_id: 1, level_number: 25, title: 'Formulare & Validierung', 
            description: 'Erstelle benutzerfreundliche Formulare mit Validierung.',
            content: `# Formulare & Validierung

Professionelle Formulare mit HTML5 und CSS:

\`\`\`html
<form class="contact-form">
    <div class="form-group">
        <label for="name">Name *</label>
        <input type="text" id="name" name="name" required>
    </div>
    
    <div class="form-group">
        <label for="email">E-Mail *</label>
        <input type="email" id="email" name="email" required>
    </div>
    
    <div class="form-group">
        <label for="phone">Telefon</label>
        <input type="tel" id="phone" name="phone" pattern="[0-9]{10,}">
    </div>
    
    <div class="form-group">
        <label for="message">Nachricht *</label>
        <textarea id="message" name="message" rows="5" required></textarea>
    </div>
    
    <button type="submit">Senden</button>
</form>
\`\`\`

\`\`\`css
.form-group {
    margin-bottom: 20px;
}

input, textarea {
    width: 100%;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 5px;
}

input:focus, textarea:focus {
    border-color: #3498db;
    outline: none;
}

input:invalid {
    border-color: #e74c3c;
}

input:valid {
    border-color: #2ecc71;
}
\`\`\`

## Aufgabe
Erstelle ein Kontaktformular mit Name, E-Mail und Nachricht inklusive Validierung.`,
            exercise_type: 'code_example',
            expected_output: 'html_forms',
            points: 15
        },
        {
            course_id: 1, level_number: 26, title: 'Semantic HTML5', 
            description: 'Verwende semantische HTML5-Elemente für bessere Struktur.',
            content: `# Semantic HTML5

Semantische Elemente verbessern Struktur und Accessibility:

\`\`\`html
<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <title>Semantic HTML5</title>
</head>
<body>
    <header>
        <nav>
            <ul>
                <li><a href="#home">Home</a></li>
                <li><a href="#about">About</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </nav>
    </header>
    
    <main>
        <section id="hero">
            <h1>Willkommen</h1>
            <p>Das ist der Hero-Bereich</p>
        </section>
        
        <section id="content">
            <article>
                <header>
                    <h2>Artikel Titel</h2>
                    <time datetime="2025-01-15">15. Januar 2025</time>
                </header>
                <p>Artikel Inhalt...</p>
                <footer>
                    <p>Von: <address>Max Mustermann</address></p>
                </footer>
            </article>
        </section>
        
        <aside>
            <h3>Sidebar</h3>
            <p>Zusätzliche Informationen</p>
        </aside>
    </main>
    
    <footer>
        <p>&copy; 2025 Meine Website</p>
    </footer>
</body>
</html>
\`\`\`

## Aufgabe
Erstelle eine Seite mit header, nav, main, section, article, aside und footer.`,
            exercise_type: 'code_example',
            expected_output: 'html_semantic',
            points: 15
        },
        {
            course_id: 1, level_number: 27, title: 'CSS Pseudo-Klassen & Pseudo-Elemente', 
            description: 'Verwende erweiterte CSS-Selektoren für präzises Styling.',
            content: `# Pseudo-Klassen & Pseudo-Elemente

Erweiterte CSS-Selektoren für präzises Styling:

\`\`\`css
/* Pseudo-Klassen */
a:hover { color: red; }
a:visited { color: purple; }
a:active { color: orange; }

input:focus { border-color: blue; }
input:invalid { border-color: red; }
input:valid { border-color: green; }

/* Strukturelle Pseudo-Klassen */
li:first-child { font-weight: bold; }
li:last-child { margin-bottom: 0; }
li:nth-child(odd) { background-color: #f0f0f0; }
li:nth-child(even) { background-color: white; }

/* Pseudo-Elemente */
p::first-letter {
    font-size: 2em;
    font-weight: bold;
    color: red;
}

p::first-line {
    font-variant: small-caps;
}

.quote::before {
    content: """;
    font-size: 2em;
}

.quote::after {
    content: """;
    font-size: 2em;
}

/* Tooltip mit Pseudo-Elementen */
.tooltip {
    position: relative;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: black;
    color: white;
    padding: 5px;
    border-radius: 3px;
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip:hover::after {
    opacity: 1;
}
\`\`\`

## Aufgabe
Erstelle eine Liste mit abwechselnden Hintergrundfarben und Hover-Effekten.`,
            exercise_type: 'code_example',
            expected_output: 'css_pseudo',
            points: 15
        },
        {
            course_id: 1, level_number: 28, title: 'CSS Flexbox Advanced', 
            description: 'Meistere erweiterte Flexbox-Techniken.',
            content: `# Advanced Flexbox

Erweiterte Flexbox-Techniken für komplexe Layouts:

\`\`\`css
/* Flex Container Properties */
.container {
    display: flex;
    flex-direction: row; /* row, column, row-reverse, column-reverse */
    flex-wrap: wrap;     /* nowrap, wrap, wrap-reverse */
    justify-content: space-between; /* flex-start, center, space-around, space-evenly */
    align-items: stretch; /* flex-start, center, flex-end, baseline */
    align-content: center; /* für mehrzeilige Flex-Container */
    gap: 20px;
}

/* Flex Item Properties */
.item {
    flex: 1 1 300px; /* flex-grow, flex-shrink, flex-basis */
    align-self: center; /* überschreibt align-items für dieses Item */
}

.item-1 { flex-grow: 2; } /* nimmt doppelt so viel Platz */
.item-2 { flex-shrink: 0; } /* schrumpft nicht */
.item-3 { flex-basis: 200px; } /* Basis-Breite */

/* Responsive Flexbox */
@media (max-width: 768px) {
    .container {
        flex-direction: column;
    }
}

/* Flexbox für Navigation */
.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-links {
    display: flex;
    gap: 20px;
    margin-left: auto;
}
\`\`\`

## Aufgabe
Erstelle eine responsive Navigation mit Flexbox, die auf Mobile vertikal wird.`,
            exercise_type: 'code_example',
            expected_output: 'css_flexbox_advanced',
            points: 15
        },
        {
            course_id: 1, level_number: 29, title: 'CSS Positioning & Z-Index', 
            description: 'Beherrsche CSS-Positionierung für komplexe Layouts.',
            content: `# CSS Positioning & Z-Index

Präzise Positionierung von Elementen:

\`\`\`css
/* Position Types */
.static { position: static; } /* Standard */
.relative { 
    position: relative; 
    top: 10px; 
    left: 20px; 
}
.absolute { 
    position: absolute; 
    top: 0; 
    right: 0; 
}
.fixed { 
    position: fixed; 
    bottom: 20px; 
    right: 20px; 
}
.sticky { 
    position: sticky; 
    top: 0; 
}

/* Z-Index für Layering */
.modal-backdrop { z-index: 1000; }
.modal { z-index: 1001; }
.tooltip { z-index: 1002; }

/* Praktische Beispiele */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 100;
    background: white;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.floating-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 999;
    border-radius: 50%;
    width: 60px;
    height: 60px;
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    z-index: 10;
}
\`\`\`

## Aufgabe
Erstelle einen Fixed Header und einen Floating Action Button.`,
            exercise_type: 'code_example',
            expected_output: 'css_positioning',
            points: 15
        }
    ];
    
    // Insert advanced CSS levels
    for (const level of advancedCssLevels) {
        levelStmt.run(level.course_id, level.level_number, level.title, level.description, level.content, level.exercise_type, level.expected_output, level.points);
    }
    
    console.log('✅ Advanced CSS levels 21-29 created!');
    
    levelStmt.finalize();
    directDb.close();
    
    console.log('🎉 Levels 20-29 created successfully!');
});
