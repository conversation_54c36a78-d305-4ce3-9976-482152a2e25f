const sqlite3 = require('sqlite3').verbose();
const { dbPath } = require('./database/init');

console.log('🚀 Creating Complete 40-Level System for All Courses...');

const directDb = new sqlite3.Database(dbPath);

directDb.serialize(() => {
    console.log('🗑️ Clearing existing levels and achievements...');
    
    // Clear existing data
    directDb.run('DELETE FROM user_progress');
    directDb.run('DELETE FROM achievements'); 
    directDb.run('DELETE FROM levels');
    
    console.log('✅ Existing data cleared!');

    // Create all levels for all courses
    const levelStmt = directDb.prepare(`INSERT INTO levels (course_id, level_number, title, description, content, exercise_type, expected_output, points) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`);
    
    // 🌐 HTML, CSS, JS Course (Course ID: 1) - All 40 Levels
    const htmlCssJsLevels = [
        // Level 1-9: HTML/CSS Grundlagen
        {
            course_id: 1, level_number: 1, title: 'HTML Grundstruktur', 
            description: '<PERSON><PERSON> die Basis-HTML-Struktur mit DOCTYPE, html, head und body Tags.',
            content: `# HTML Grundstruktur

Jede HTML-Seite beginnt mit einer grundlegenden Struktur:

\`\`\`html
<!DOCTYPE html>
<html lang="de">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Meine erste Webseite</title>
</head>
<body>
    <h1>Willkommen!</h1>
    <p>Das ist meine erste HTML-Seite.</p>
</body>
</html>
\`\`\`

## Aufgabe
Erstelle eine HTML-Seite mit der korrekten Grundstruktur und einem Titel "Meine Webseite".`,
            exercise_type: 'code_example',
            expected_output: 'html_structure',
            points: 10
        },
        {
            course_id: 1, level_number: 2, title: 'Texte & Überschriften', 
            description: 'Formatiere Texte mit verschiedenen Überschriften-Tags (h1-h6) und Absätzen.',
            content: `# Texte & Überschriften

HTML bietet verschiedene Überschriften-Ebenen:

\`\`\`html
<h1>Hauptüberschrift</h1>
<h2>Unterüberschrift</h2>
<h3>Weitere Unterüberschrift</h3>
<p>Das ist ein normaler Absatz mit Text.</p>
<p>Hier ist ein <strong>fetter Text</strong> und <em>kursiver Text</em>.</p>
\`\`\`

## Aufgabe
Erstelle eine Seite mit mindestens einer h1-Überschrift und zwei Absätzen.`,
            exercise_type: 'code_example',
            expected_output: 'h1_p',
            points: 10
        },
        {
            course_id: 1, level_number: 3, title: 'Links & Bilder', 
            description: 'Füge Links und Bilder in deine HTML-Seite ein.',
            content: `# Links & Bilder

Links und Bilder machen Webseiten interaktiv:

\`\`\`html
<a href="https://www.example.com">Besuche Example.com</a>
<a href="mailto:<EMAIL>">E-Mail senden</a>

<img src="bild.jpg" alt="Beschreibung des Bildes" width="300">
<img src="https://via.placeholder.com/200" alt="Platzhalter-Bild">
\`\`\`

## Aufgabe
Erstelle einen Link und füge ein Bild hinzu.`,
            exercise_type: 'code_example',
            expected_output: 'a_img',
            points: 10
        },
        {
            course_id: 1, level_number: 4, title: 'Listen & Tabellen', 
            description: 'Erstelle geordnete und ungeordnete Listen sowie einfache Tabellen.',
            content: `# Listen & Tabellen

Listen strukturieren Inhalte:

\`\`\`html
<ul>
    <li>Erstes Element</li>
    <li>Zweites Element</li>
</ul>

<ol>
    <li>Schritt 1</li>
    <li>Schritt 2</li>
</ol>

<table>
    <tr>
        <th>Name</th>
        <th>Alter</th>
    </tr>
    <tr>
        <td>Max</td>
        <td>25</td>
    </tr>
</table>
\`\`\`

## Aufgabe
Erstelle eine ungeordnete Liste mit 3 Elementen.`,
            exercise_type: 'code_example',
            expected_output: 'ul_li',
            points: 10
        },
        {
            course_id: 1, level_number: 5, title: 'CSS einbinden', 
            description: 'Lerne die drei Arten, CSS in HTML einzubinden: Inline, Internal und External.',
            content: `# CSS einbinden

Es gibt drei Wege, CSS zu verwenden:

## 1. Inline CSS
\`\`\`html
<p style="color: red; font-size: 18px;">Roter Text</p>
\`\`\`

## 2. Internal CSS
\`\`\`html
<head>
    <style>
        p { color: blue; }
    </style>
</head>
\`\`\`

## 3. External CSS
\`\`\`html
<head>
    <link rel="stylesheet" href="style.css">
</head>
\`\`\`

## Aufgabe
Verwende Internal CSS, um einen Absatz blau zu färben.`,
            exercise_type: 'code_example',
            expected_output: 'css_style',
            points: 10
        },
        {
            course_id: 1, level_number: 6, title: 'Farben & Hintergründe', 
            description: 'Gestalte deine Webseite mit Farben und Hintergründen.',
            content: `# Farben & Hintergründe

CSS bietet viele Möglichkeiten für Farben:

\`\`\`css
/* Textfarbe */
h1 { color: #ff0000; }
p { color: rgb(0, 128, 255); }

/* Hintergrundfarbe */
body { background-color: #f0f0f0; }
div { background-color: lightblue; }

/* Hintergrundbild */
.hero { 
    background-image: url('bild.jpg');
    background-size: cover;
}
\`\`\`

## Aufgabe
Setze eine Hintergrundfarbe für den body und eine Textfarbe für h1.`,
            exercise_type: 'code_example',
            expected_output: 'css_colors',
            points: 10
        },
        {
            course_id: 1, level_number: 7, title: 'Box-Modell: Margin, Padding, Border', 
            description: 'Verstehe das CSS Box-Modell mit Margin, Padding und Border.',
            content: `# Box-Modell

Jedes HTML-Element ist eine Box:

\`\`\`css
.box {
    width: 200px;
    height: 100px;
    padding: 20px;      /* Innenabstand */
    border: 2px solid black;  /* Rahmen */
    margin: 10px;       /* Außenabstand */
    background-color: lightgray;
}
\`\`\`

## Aufgabe
Erstelle ein div mit Padding, Border und Margin.`,
            exercise_type: 'code_example',
            expected_output: 'css_box_model',
            points: 10
        },
        {
            course_id: 1, level_number: 8, title: 'Flexbox Grundlagen', 
            description: 'Lerne die Grundlagen von CSS Flexbox für moderne Layouts.',
            content: `# Flexbox Grundlagen

Flexbox macht Layouts einfach:

\`\`\`css
.container {
    display: flex;
    justify-content: center;  /* horizontal zentrieren */
    align-items: center;      /* vertikal zentrieren */
    gap: 20px;               /* Abstand zwischen Elementen */
}

.item {
    flex: 1;  /* gleichmäßig verteilen */
}
\`\`\`

\`\`\`html
<div class="container">
    <div class="item">Element 1</div>
    <div class="item">Element 2</div>
    <div class="item">Element 3</div>
</div>
\`\`\`

## Aufgabe
Erstelle einen Flex-Container mit 3 Elementen.`,
            exercise_type: 'code_example',
            expected_output: 'css_flexbox',
            points: 10
        },
        {
            course_id: 1, level_number: 9, title: 'Navigation & Footer', 
            description: 'Erstelle eine Navigation und einen Footer für deine Webseite.',
            content: `# Navigation & Footer

Strukturiere deine Seite mit nav und footer:

\`\`\`html
<nav>
    <ul>
        <li><a href="#home">Home</a></li>
        <li><a href="#about">Über uns</a></li>
        <li><a href="#contact">Kontakt</a></li>
    </ul>
</nav>

<main>
    <h1>Hauptinhalt</h1>
    <p>Hier steht der Inhalt der Seite.</p>
</main>

<footer>
    <p>&copy; 2025 Meine Webseite</p>
</footer>
\`\`\`

## Aufgabe
Erstelle eine Navigation mit 3 Links und einen Footer.`,
            exercise_type: 'code_example',
            expected_output: 'nav_footer',
            points: 10
        }
    ];

    console.log('📝 Creating HTML/CSS/JS levels...');
    
    // Insert first 9 levels
    for (const level of htmlCssJsLevels) {
        levelStmt.run(level.course_id, level.level_number, level.title, level.description, level.content, level.exercise_type, level.expected_output, level.points);
    }
    
    // Level 10: Boss Level
    const bossLevel10 = {
        course_id: 1, level_number: 10, title: '🏆 BOSS: Visitenkarten-Website',
        description: 'Erstelle eine komplette Visitenkarten-Website mit Navigation, Inhalt und Footer.',
        content: `# 🏆 BOSS LEVEL: Visitenkarten-Website

Zeit für dein erstes großes Projekt! Erstelle eine professionelle Visitenkarten-Website.

## Anforderungen:
- ✅ Vollständige HTML-Struktur
- ✅ Navigation mit mindestens 3 Bereichen
- ✅ Hauptbereich mit deinen Informationen
- ✅ Bild (kann Platzhalter sein)
- ✅ Kontaktinformationen
- ✅ Footer mit Copyright
- ✅ CSS-Styling mit Farben und Layout
- ✅ Flexbox für das Layout

## Beispiel-Struktur:
\`\`\`html
<!DOCTYPE html>
<html lang="de">
<head>
    <title>Max Mustermann - Webentwickler</title>
    <style>
        /* Dein CSS hier */
    </style>
</head>
<body>
    <nav><!-- Navigation --></nav>
    <main><!-- Hauptinhalt --></main>
    <footer><!-- Footer --></footer>
</body>
</html>
\`\`\`

Zeige, was du gelernt hast! 🚀`,
        exercise_type: 'project',
        expected_output: 'boss_project',
        points: 50
    };

    levelStmt.run(bossLevel10.course_id, bossLevel10.level_number, bossLevel10.title, bossLevel10.description, bossLevel10.content, bossLevel10.exercise_type, bossLevel10.expected_output, bossLevel10.points);

    // Level 11-19: JavaScript Grundlagen
    const jsLevels = [
        {
            course_id: 1, level_number: 11, title: 'JavaScript einbinden',
            description: 'Lerne, wie du JavaScript in HTML einbindest.',
            content: `# JavaScript einbinden

JavaScript macht Webseiten interaktiv:

\`\`\`html
<!DOCTYPE html>
<html>
<head>
    <title>JavaScript Test</title>
</head>
<body>
    <h1 id="titel">Hallo Welt!</h1>

    <script>
        console.log("JavaScript funktioniert!");
        alert("Willkommen!");
    </script>
</body>
</html>
\`\`\`

## Aufgabe
Erstelle eine HTML-Seite mit JavaScript, das eine Nachricht in der Konsole ausgibt.`,
            exercise_type: 'code_example',
            expected_output: 'js_console',
            points: 10
        },
        {
            course_id: 1, level_number: 12, title: 'Variablen & Datentypen',
            description: 'Arbeite mit JavaScript Variablen und verschiedenen Datentypen.',
            content: `# Variablen & Datentypen

JavaScript hat verschiedene Datentypen:

\`\`\`javascript
// Variablen deklarieren
let name = "Max";           // String
let alter = 25;             // Number
let istStudent = true;      // Boolean
let hobbys = ["Lesen", "Sport"]; // Array
let person = {              // Object
    name: "Anna",
    alter: 30
};

console.log("Name:", name);
console.log("Alter:", alter);
console.log("Ist Student:", istStudent);
\`\`\`

## Aufgabe
Erstelle Variablen für deinen Namen, Alter und ein Hobby. Gib sie in der Konsole aus.`,
            exercise_type: 'code_example',
            expected_output: 'js_variables',
            points: 10
        },
        {
            course_id: 1, level_number: 13, title: 'DOM Manipulation',
            description: 'Verändere HTML-Elemente mit JavaScript.',
            content: `# DOM Manipulation

Mit JavaScript kannst du HTML-Elemente verändern:

\`\`\`html
<h1 id="titel">Alter Titel</h1>
<p id="text">Alter Text</p>
<button onclick="aendern()">Ändern</button>

<script>
function aendern() {
    document.getElementById("titel").innerHTML = "Neuer Titel!";
    document.getElementById("text").style.color = "red";
    document.getElementById("text").innerHTML = "Text wurde geändert!";
}
</script>
\`\`\`

## Aufgabe
Erstelle einen Button, der beim Klick den Text eines h1-Elements ändert.`,
            exercise_type: 'code_example',
            expected_output: 'js_dom',
            points: 10
        },
        {
            course_id: 1, level_number: 14, title: 'Event Handling',
            description: 'Reagiere auf Benutzerinteraktionen mit Events.',
            content: `# Event Handling

Events machen Webseiten interaktiv:

\`\`\`html
<button id="meinButton">Klick mich!</button>
<input type="text" id="eingabe" placeholder="Schreibe etwas...">
<p id="ausgabe"></p>

<script>
document.getElementById("meinButton").addEventListener("click", function() {
    alert("Button wurde geklickt!");
});

document.getElementById("eingabe").addEventListener("input", function() {
    let text = this.value;
    document.getElementById("ausgabe").innerHTML = "Du schreibst: " + text;
});
</script>
\`\`\`

## Aufgabe
Erstelle ein Eingabefeld, das bei jeder Eingabe den Text live anzeigt.`,
            exercise_type: 'code_example',
            expected_output: 'js_events',
            points: 10
        },
        {
            course_id: 1, level_number: 15, title: 'Funktionen',
            description: 'Erstelle wiederverwendbare JavaScript-Funktionen.',
            content: `# Funktionen

Funktionen organisieren deinen Code:

\`\`\`javascript
// Einfache Funktion
function begruessung(name) {
    return "Hallo " + name + "!";
}

// Arrow Function
const addieren = (a, b) => {
    return a + b;
};

// Funktion verwenden
console.log(begruessung("Max"));
console.log(addieren(5, 3));

// Funktion mit mehreren Parametern
function berechneAlter(geburtsjahr) {
    let aktuellesJahr = new Date().getFullYear();
    return aktuellesJahr - geburtsjahr;
}
\`\`\`

## Aufgabe
Erstelle eine Funktion, die zwei Zahlen multipliziert und das Ergebnis zurückgibt.`,
            exercise_type: 'code_example',
            expected_output: 'js_functions',
            points: 10
        },
        {
            course_id: 1, level_number: 16, title: 'Arrays & Schleifen',
            description: 'Arbeite mit Arrays und Schleifen in JavaScript.',
            content: `# Arrays & Schleifen

Arrays speichern mehrere Werte:

\`\`\`javascript
let fruechte = ["Apfel", "Banane", "Orange"];

// For-Schleife
for (let i = 0; i < fruechte.length; i++) {
    console.log(fruechte[i]);
}

// For-of-Schleife
for (let frucht of fruechte) {
    console.log("Ich mag " + frucht);
}

// Array-Methoden
fruechte.push("Erdbeere");    // Hinzufügen
fruechte.pop();               // Letztes entfernen
console.log(fruechte.length); // Anzahl

// forEach-Methode
fruechte.forEach(function(frucht, index) {
    console.log(index + ": " + frucht);
});
\`\`\`

## Aufgabe
Erstelle ein Array mit 5 Farben und gib jede Farbe mit einer Schleife aus.`,
            exercise_type: 'code_example',
            expected_output: 'js_arrays',
            points: 10
        },
        {
            course_id: 1, level_number: 17, title: 'Objekte',
            description: 'Arbeite mit JavaScript-Objekten und ihren Eigenschaften.',
            content: `# Objekte

Objekte gruppieren zusammengehörige Daten:

\`\`\`javascript
// Objekt erstellen
let person = {
    name: "Anna",
    alter: 28,
    beruf: "Entwicklerin",
    hobbys: ["Lesen", "Wandern"],

    // Methode im Objekt
    vorstellen: function() {
        return "Hallo, ich bin " + this.name + " und " + this.alter + " Jahre alt.";
    }
};

// Eigenschaften zugreifen
console.log(person.name);
console.log(person["alter"]);
console.log(person.vorstellen());

// Eigenschaften ändern
person.alter = 29;
person.stadt = "Berlin";  // Neue Eigenschaft hinzufügen
\`\`\`

## Aufgabe
Erstelle ein Objekt für ein Auto mit Marke, Modell und Baujahr.`,
            exercise_type: 'code_example',
            expected_output: 'js_objects',
            points: 10
        },
        {
            course_id: 1, level_number: 18, title: 'Bedingungen & Vergleiche',
            description: 'Verwende if-else Bedingungen und Vergleichsoperatoren.',
            content: `# Bedingungen & Vergleiche

Treffe Entscheidungen in deinem Code:

\`\`\`javascript
let alter = 18;
let name = "Max";

// If-else Bedingung
if (alter >= 18) {
    console.log("Du bist volljährig!");
} else {
    console.log("Du bist noch minderjährig.");
}

// Mehrere Bedingungen
if (alter < 13) {
    console.log("Kind");
} else if (alter < 18) {
    console.log("Jugendlicher");
} else if (alter < 65) {
    console.log("Erwachsener");
} else {
    console.log("Senior");
}

// Vergleichsoperatoren
console.log(5 == "5");   // true (Wert gleich)
console.log(5 === "5");  // false (Wert und Typ gleich)
console.log(10 > 5);     // true
console.log(3 <= 3);     // true
\`\`\`

## Aufgabe
Erstelle eine Bedingung, die prüft, ob eine Zahl positiv, negativ oder null ist.`,
            exercise_type: 'code_example',
            expected_output: 'js_conditions',
            points: 10
        },
        {
            course_id: 1, level_number: 19, title: 'LocalStorage',
            description: 'Speichere Daten im Browser mit LocalStorage.',
            content: `# LocalStorage

Speichere Daten dauerhaft im Browser:

\`\`\`html
<input type="text" id="name" placeholder="Dein Name">
<button onclick="speichern()">Speichern</button>
<button onclick="laden()">Laden</button>
<p id="ausgabe"></p>

<script>
function speichern() {
    let name = document.getElementById("name").value;
    localStorage.setItem("benutzername", name);
    alert("Name gespeichert!");
}

function laden() {
    let gespeicherterName = localStorage.getItem("benutzername");
    if (gespeicherterName) {
        document.getElementById("ausgabe").innerHTML = "Gespeicherter Name: " + gespeicherterName;
    } else {
        document.getElementById("ausgabe").innerHTML = "Kein Name gespeichert.";
    }
}

// Beim Laden der Seite automatisch laden
window.onload = function() {
    laden();
};
</script>
\`\`\`

## Aufgabe
Erstelle eine Eingabe, die einen Text speichert und beim nächsten Besuch wieder lädt.`,
            exercise_type: 'code_example',
            expected_output: 'js_localstorage',
            points: 10
        }
    ];

    // Insert JavaScript levels
    for (const level of jsLevels) {
        levelStmt.run(level.course_id, level.level_number, level.title, level.description, level.content, level.exercise_type, level.expected_output, level.points);
    }

    console.log('✅ JavaScript levels 11-19 created!');

    levelStmt.finalize();
    directDb.close();

    console.log('🎉 HTML/CSS/JS levels 1-19 created successfully!');
});
