/**
 * Comprehensive Test Suite for Enhanced Features
 * Tests both internationalization and enhanced code validation
 */

const Database = require('./database/db');
const CodeValidator = require('./services/codeValidator');

const db = new Database();
const validator = new CodeValidator();

console.log('🧪 Starting Comprehensive Enhancement Tests\n');

// Test 1: Internationalization - Check translation keys exist
console.log('📝 Test 1: Internationalization - Translation Keys');
console.log('================================================');

db.getTranslations('en', (err, enTranslations) => {
    if (err) {
        console.error('❌ Error loading English translations:', err);
        return;
    }
    
    db.getTranslations('de', (err, deTranslations) => {
        if (err) {
            console.error('❌ Error loading German translations:', err);
            return;
        }
        
        console.log(`✅ English translations loaded: ${Object.keys(enTranslations).length} keys`);
        console.log(`✅ German translations loaded: ${Object.keys(deTranslations).length} keys`);

        // Check for key auth translation keys
        const requiredAuthKeys = [
            'auth.login_title',
            'auth.register_title',
            'auth.username',
            'auth.password',
            'auth.email_verified',
            'auth.password_reset_success',
            'auth.registration_successful',
            'auth.forgot_password_title'
        ];

        let missingKeys = [];
        requiredAuthKeys.forEach(key => {
            const hasEn = enTranslations.hasOwnProperty(key);
            const hasDe = deTranslations.hasOwnProperty(key);

            if (!hasEn || !hasDe) {
                missingKeys.push(key);
            }
        });
        
        if (missingKeys.length === 0) {
            console.log('✅ All required auth translation keys are present');
        } else {
            console.log('❌ Missing translation keys:', missingKeys);
        }
        
        console.log('\n');
        runCodeValidationTests();
    });
});

// Test 2: Enhanced Code Validation
function runCodeValidationTests() {
    console.log('🔍 Test 2: Enhanced Code Validation');
    console.log('===================================');
    
    const testCases = [
        {
            name: 'HTML Level 1 - Perfect Solution',
            code: '<h1>My Website</h1><p>Welcome to my website!</p>',
            level: { level_number: 1 },
            type: 'html',
            expectedPassed: true,
            expectedScore: 100
        },
        {
            name: 'HTML Level 1 - Missing Paragraph',
            code: '<h1>My Website</h1>',
            level: { level_number: 1 },
            type: 'html',
            expectedPassed: false,
            expectedScore: 50
        },
        {
            name: 'HTML Level 1 - Empty Elements',
            code: '<h1></h1><p></p>',
            level: { level_number: 1 },
            type: 'html',
            expectedPassed: false,
            expectedScore: 70
        },
        {
            name: 'HTML Level 2 - Complete List with Links',
            code: '<ul><li><a href="https://example.com">Link 1</a></li><li><a href="https://test.com">Link 2</a></li></ul>',
            level: { level_number: 2 },
            type: 'html',
            expectedPassed: true,
            expectedScore: 100
        },
        {
            name: 'HTML Level 2 - List without Links',
            code: '<ul><li>Item 1</li><li>Item 2</li></ul>',
            level: { level_number: 2 },
            type: 'html',
            expectedPassed: false,
            expectedScore: 70
        },
        {
            name: 'HTML Level 3 - Complete Table',
            code: '<table><tr><th>Name</th><th>Age</th></tr><tr><td>John</td><td>25</td></tr></table>',
            level: { level_number: 3 },
            type: 'html',
            expectedPassed: true,
            expectedScore: 100
        },
        {
            name: 'CSS Level 4 - Valid Color Property',
            code: 'h1 { color: blue; font-size: 24px; }',
            level: { level_number: 4 },
            type: 'css',
            expectedPassed: true,
            expectedScore: 100
        },
        {
            name: 'CSS Level 4 - Invalid Color',
            code: 'h1 { color: invalidcolor; }',
            level: { level_number: 4 },
            type: 'css',
            expectedPassed: false,
            expectedScore: 60
        },
        {
            name: 'JavaScript Level 8 - Working Console.log',
            code: 'console.log("Hello, World!");',
            level: { level_number: 8 },
            type: 'javascript',
            expectedPassed: true,
            expectedScore: 100
        },
        {
            name: 'JavaScript Level 8 - Missing Console.log',
            code: 'var message = "Hello";',
            level: { level_number: 8 },
            type: 'javascript',
            expectedPassed: false,
            expectedScore: 30
        },
        {
            name: 'JavaScript Syntax Error',
            code: 'console.log("Hello World"',
            level: { level_number: 8 },
            type: 'javascript',
            expectedPassed: false,
            expectedScore: 10
        },
        {
            name: 'Empty Code Submission',
            code: '',
            level: { level_number: 1 },
            type: 'html',
            expectedPassed: false,
            expectedScore: 0
        }
    ];
    
    let passedTests = 0;
    let totalTests = testCases.length;
    
    testCases.forEach((testCase, index) => {
        console.log(`\n🧪 Test ${index + 1}: ${testCase.name}`);
        console.log('─'.repeat(50));
        
        try {
            const result = validator.validateCode(testCase.code, testCase.level, testCase.type);
            
            console.log(`Code: ${testCase.code.substring(0, 50)}${testCase.code.length > 50 ? '...' : ''}`);
            console.log(`Result: ${result.passed ? '✅ PASSED' : '❌ FAILED'} | Score: ${result.score}/100`);
            console.log(`Message: ${result.message}`);
            
            if (result.errors.length > 0) {
                console.log(`Errors: ${result.errors.join(', ')}`);
            }
            
            if (result.suggestions.length > 0) {
                console.log(`Suggestions: ${result.suggestions.join(', ')}`);
            }
            
            // Validate test expectations
            const passedExpectation = result.passed === testCase.expectedPassed;
            const scoreInRange = Math.abs(result.score - testCase.expectedScore) <= 10; // Allow 10 point variance
            
            if (passedExpectation && scoreInRange) {
                console.log('✅ Test expectations met');
                passedTests++;
            } else {
                console.log(`❌ Test expectations not met:`);
                console.log(`   Expected passed: ${testCase.expectedPassed}, got: ${result.passed}`);
                console.log(`   Expected score: ~${testCase.expectedScore}, got: ${result.score}`);
            }
            
        } catch (error) {
            console.log(`❌ Test failed with error: ${error.message}`);
        }
    });
    
    console.log('\n📊 Test Summary');
    console.log('===============');
    console.log(`Passed: ${passedTests}/${totalTests} tests`);
    console.log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
    
    if (passedTests === totalTests) {
        console.log('🎉 All tests passed! Enhanced code validation is working perfectly.');
    } else {
        console.log('⚠️  Some tests failed. Review the validation logic.');
    }
    
    console.log('\n🏁 Testing Complete!');
    process.exit(0);
}
