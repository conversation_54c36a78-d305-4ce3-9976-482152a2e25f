const sqlite3 = require('sqlite3').verbose();
const { dbPath } = require('./database/init');

console.log('🚀 Creating Final Boss Level and All Other Courses...');

const directDb = new sqlite3.Database(dbPath);

directDb.serialize(() => {
    const levelStmt = directDb.prepare(`INSERT INTO levels (course_id, level_number, title, description, content, exercise_type, expected_output, points) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`);
    
    // Level 40: Final Boss Level für HTML/CSS/JS
    const finalBossLevel = {
        course_id: 1, level_number: 40, title: '🏆 FINAL BOSS: Full-Stack Web Application', 
        description: 'Erstelle eine vollständige Web-Anwendung mit allem was du gelernt hast!',
        content: `# 🏆 FINAL BOSS LEVEL: Full-Stack Web Application

Das ultimative Projekt! Erstelle eine vollständige, professionelle Web-Anwendung.

## Anforderungen:
- ✅ **Responsive Design** für alle Geräte
- ✅ **Modern CSS** (Grid, Flexbox, Animationen)
- ✅ **JavaScript ES6+** Features
- ✅ **API Integration** mit Fetch
- ✅ **LocalStorage** für Datenpersistenz
- ✅ **Progressive Web App** Features
- ✅ **Performance Optimierung**
- ✅ **Error Handling & Testing**

## Projekt-Ideen:
1. **Task Management System** - Vollständige To-Do App mit Kategorien, Deadlines, Prioritäten
2. **Personal Dashboard** - Wetter, News, Kalender, Notizen in einem
3. **Portfolio CMS** - Content Management für dein Portfolio
4. **Social Media Dashboard** - Posts verwalten und planen
5. **E-Learning Platform** - Mini-Version einer Lernplattform

## Technische Features:
- Modularer JavaScript-Code
- Web Components
- Service Worker für Offline-Funktionalität
- Canvas/SVG Visualisierungen
- WebSocket-Integration (simuliert)
- Umfangreiche Formulare mit Validierung

## Bewertungskriterien:
- **Code-Qualität** (25%)
- **Design & UX** (25%)
- **Funktionalität** (25%)
- **Innovation** (25%)

Zeige alles was du gelernt hast! Dies ist dein Meisterwerk! 🚀`,
        exercise_type: 'final_project',
        expected_output: 'final_boss_project',
        points: 500
    };
    
    levelStmt.run(finalBossLevel.course_id, finalBossLevel.level_number, finalBossLevel.title, finalBossLevel.description, finalBossLevel.content, finalBossLevel.exercise_type, finalBossLevel.expected_output, finalBossLevel.points);
    
    console.log('✅ HTML/CSS/JS Final Boss Level 40 created!');
    
    // JavaScript + PHP Course (Course ID: 2) - Alle 40 Level
    const jsPhpLevels = [
        // Level 1-9: JavaScript Grundlagen
        {
            course_id: 2, level_number: 1, title: 'JavaScript Variablen', 
            description: 'Lerne die Grundlagen von JavaScript-Variablen.',
            content: `# JavaScript Variablen

JavaScript ist die Sprache des Webs:

\`\`\`javascript
// Variablen deklarieren
let name = "Max";
const alter = 25;
var stadt = "Berlin"; // veraltet, verwende let/const

// Datentypen
let zahl = 42;              // Number
let text = "Hallo Welt";    // String
let istWahr = true;         // Boolean
let liste = [1, 2, 3];      // Array
let person = {              // Object
    name: "Anna",
    alter: 30
};

console.log("Name:", name);
console.log("Alter:", alter);
\`\`\`

## Aufgabe
Erstelle Variablen für deinen Namen, Alter und Wohnort. Gib sie in der Konsole aus.`,
            exercise_type: 'code_example',
            expected_output: 'js_variables',
            points: 10
        },
        {
            course_id: 2, level_number: 2, title: 'Funktionen', 
            description: 'Erstelle wiederverwendbare JavaScript-Funktionen.',
            content: `# JavaScript Funktionen

Funktionen organisieren deinen Code:

\`\`\`javascript
// Klassische Funktion
function begruessung(name) {
    return "Hallo " + name + "!";
}

// Arrow Function (modern)
const addieren = (a, b) => {
    return a + b;
};

// Kurze Arrow Function
const quadrat = x => x * x;

// Funktion aufrufen
console.log(begruessung("Max"));
console.log(addieren(5, 3));
console.log(quadrat(4));

// Funktion mit Default-Parameter
function vorstellen(name = "Gast", alter = 0) {
    return \`Ich bin \${name} und \${alter} Jahre alt.\`;
}
\`\`\`

## Aufgabe
Erstelle eine Funktion, die zwei Zahlen multipliziert und das Ergebnis zurückgibt.`,
            exercise_type: 'code_example',
            expected_output: 'js_functions',
            points: 10
        },
        {
            course_id: 2, level_number: 3, title: 'Arrays & Objekte', 
            description: 'Arbeite mit komplexen Datenstrukturen.',
            content: `# Arrays & Objekte

Komplexe Datenstrukturen in JavaScript:

\`\`\`javascript
// Arrays
let fruechte = ["Apfel", "Banane", "Orange"];
fruechte.push("Erdbeere");    // Hinzufügen
fruechte.pop();               // Letztes entfernen

// Array-Methoden
fruechte.forEach(frucht => {
    console.log("Frucht:", frucht);
});

let grosseFruechte = fruechte.map(frucht => frucht.toUpperCase());
let langeFruechte = fruechte.filter(frucht => frucht.length > 5);

// Objekte
let person = {
    name: "Anna",
    alter: 28,
    hobbys: ["Lesen", "Sport"],
    
    vorstellen() {
        return \`Ich bin \${this.name} und \${this.alter} Jahre alt.\`;
    }
};

// Objektzugriff
console.log(person.name);
console.log(person["alter"]);
console.log(person.vorstellen());
\`\`\`

## Aufgabe
Erstelle ein Array mit 5 Farben und ein Objekt für ein Auto mit Marke, Modell und Baujahr.`,
            exercise_type: 'code_example',
            expected_output: 'js_arrays_objects',
            points: 10
        },
        {
            course_id: 2, level_number: 4, title: 'DOM Manipulation', 
            description: 'Verändere HTML-Elemente mit JavaScript.',
            content: `# DOM Manipulation

JavaScript macht Webseiten interaktiv:

\`\`\`html
<h1 id="titel">Alter Titel</h1>
<p class="text">Erster Absatz</p>
<p class="text">Zweiter Absatz</p>
<button onclick="aendern()">Ändern</button>

<script>
// Element auswählen
const titel = document.getElementById("titel");
const absaetze = document.querySelectorAll(".text");

function aendern() {
    // Text ändern
    titel.innerHTML = "Neuer Titel!";
    titel.style.color = "red";
    
    // Alle Absätze ändern
    absaetze.forEach((p, index) => {
        p.innerHTML = \`Geänderter Absatz \${index + 1}\`;
        p.style.backgroundColor = "yellow";
    });
    
    // Neues Element erstellen
    const neuerText = document.createElement("p");
    neuerText.innerHTML = "Dynamisch hinzugefügt!";
    document.body.appendChild(neuerText);
}
</script>
\`\`\`

## Aufgabe
Erstelle einen Button, der beim Klick den Text und die Farbe eines Elements ändert.`,
            exercise_type: 'code_example',
            expected_output: 'js_dom',
            points: 10
        },
        {
            course_id: 2, level_number: 5, title: 'Event Handling', 
            description: 'Reagiere auf Benutzerinteraktionen.',
            content: `# Event Handling

Reagiere auf Benutzeraktionen:

\`\`\`html
<button id="meinButton">Klick mich!</button>
<input type="text" id="eingabe" placeholder="Schreibe etwas...">
<div id="ausgabe"></div>

<script>
// Event Listener hinzufügen
document.getElementById("meinButton").addEventListener("click", function() {
    alert("Button wurde geklickt!");
});

// Input Event
document.getElementById("eingabe").addEventListener("input", function(event) {
    const text = event.target.value;
    document.getElementById("ausgabe").innerHTML = "Du schreibst: " + text;
});

// Keyboard Events
document.addEventListener("keydown", function(event) {
    if (event.key === "Enter") {
        console.log("Enter gedrückt!");
    }
});

// Mouse Events
document.addEventListener("mousemove", function(event) {
    console.log("Maus Position:", event.clientX, event.clientY);
});
</script>
\`\`\`

## Aufgabe
Erstelle ein Eingabefeld, das bei jeder Eingabe den Text live in einem anderen Element anzeigt.`,
            exercise_type: 'code_example',
            expected_output: 'js_events',
            points: 10
        },
        {
            course_id: 2, level_number: 6, title: 'AJAX & Fetch API', 
            description: 'Lade Daten von Servern mit AJAX.',
            content: `# AJAX & Fetch API

Kommunikation mit Servern:

\`\`\`javascript
// Moderne Fetch API
async function ladeDaten() {
    try {
        const response = await fetch('https://jsonplaceholder.typicode.com/posts/1');
        const data = await response.json();
        
        console.log("Geladene Daten:", data);
        document.getElementById("inhalt").innerHTML = \`
            <h3>\${data.title}</h3>
            <p>\${data.body}</p>
        \`;
    } catch (error) {
        console.error("Fehler beim Laden:", error);
    }
}

// POST Request
async function sendeDaten() {
    const postData = {
        title: "Mein Post",
        body: "Das ist der Inhalt",
        userId: 1
    };
    
    try {
        const response = await fetch('https://jsonplaceholder.typicode.com/posts', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(postData)
        });
        
        const result = await response.json();
        console.log("Gesendet:", result);
    } catch (error) {
        console.error("Fehler beim Senden:", error);
    }
}

// Daten laden beim Seitenstart
window.onload = function() {
    ladeDaten();
};
\`\`\`

## Aufgabe
Lade Daten von einer API und zeige sie auf der Webseite an.`,
            exercise_type: 'code_example',
            expected_output: 'js_fetch',
            points: 10
        },
        {
            course_id: 2, level_number: 7, title: 'LocalStorage & SessionStorage', 
            description: 'Speichere Daten im Browser.',
            content: `# LocalStorage & SessionStorage

Daten im Browser speichern:

\`\`\`javascript
// LocalStorage (bleibt nach Browser-Schließung)
function speichernLocal() {
    const benutzername = document.getElementById("name").value;
    const einstellungen = {
        theme: "dark",
        sprache: "de",
        benachrichtigungen: true
    };
    
    localStorage.setItem("benutzername", benutzername);
    localStorage.setItem("einstellungen", JSON.stringify(einstellungen));
    
    alert("Daten gespeichert!");
}

function ladenLocal() {
    const benutzername = localStorage.getItem("benutzername");
    const einstellungen = JSON.parse(localStorage.getItem("einstellungen"));
    
    if (benutzername) {
        document.getElementById("ausgabe").innerHTML = \`
            <h3>Willkommen zurück, \${benutzername}!</h3>
            <p>Theme: \${einstellungen.theme}</p>
            <p>Sprache: \${einstellungen.sprache}</p>
        \`;
    }
}

// SessionStorage (nur für aktuelle Session)
function speichernSession() {
    sessionStorage.setItem("aktuelleSeite", window.location.href);
    sessionStorage.setItem("besuchszeit", new Date().toISOString());
}

// Beim Laden der Seite
window.onload = function() {
    ladenLocal();
    speichernSession();
};

// Storage Events (reagiert auf Änderungen)
window.addEventListener("storage", function(event) {
    console.log("Storage geändert:", event.key, event.newValue);
});
\`\`\`

## Aufgabe
Erstelle eine Eingabe, die einen Text speichert und beim nächsten Besuch wieder lädt.`,
            exercise_type: 'code_example',
            expected_output: 'js_storage',
            points: 10
        },
        {
            course_id: 2, level_number: 8, title: 'JavaScript ES6+ Features', 
            description: 'Verwende moderne JavaScript-Features.',
            content: `# ES6+ Features

Moderne JavaScript-Syntax:

\`\`\`javascript
// Template Literals
const name = "Max";
const alter = 25;
const nachricht = \`Hallo, ich bin \${name} und \${alter} Jahre alt.\`;

// Destructuring
const person = { name: "Anna", alter: 30, stadt: "Berlin" };
const { name: personName, alter: personAlter } = person;

const zahlen = [1, 2, 3, 4, 5];
const [erste, zweite, ...rest] = zahlen;

// Spread Operator
const arr1 = [1, 2, 3];
const arr2 = [4, 5, 6];
const kombiniert = [...arr1, ...arr2];

const obj1 = { a: 1, b: 2 };
const obj2 = { c: 3, d: 4 };
const verschmolzen = { ...obj1, ...obj2 };

// Arrow Functions
const quadrat = x => x * x;
const addieren = (a, b) => a + b;

// Classes
class Person {
    constructor(name, alter) {
        this.name = name;
        this.alter = alter;
    }
    
    vorstellen() {
        return \`Ich bin \${this.name} und \${this.alter} Jahre alt.\`;
    }
    
    static erstellen(name, alter) {
        return new Person(name, alter);
    }
}

const person1 = new Person("Max", 25);
const person2 = Person.erstellen("Anna", 30);

// Promises & Async/Await
async function asyncBeispiel() {
    try {
        const result = await new Promise(resolve => {
            setTimeout(() => resolve("Fertig!"), 1000);
        });
        console.log(result);
    } catch (error) {
        console.error(error);
    }
}
\`\`\`

## Aufgabe
Erstelle eine Klasse "Auto" mit ES6+ Features und verwende Template Literals.`,
            exercise_type: 'code_example',
            expected_output: 'js_es6',
            points: 10
        },
        {
            course_id: 2, level_number: 9, title: 'JavaScript Module', 
            description: 'Organisiere deinen Code in Module.',
            content: `# JavaScript Module

Code in wiederverwendbare Module aufteilen:

\`\`\`javascript
// math.js - Modul mit Funktionen
export function addieren(a, b) {
    return a + b;
}

export function multiplizieren(a, b) {
    return a * b;
}

export const PI = 3.14159;

// utils.js - Default Export
export default class Utils {
    static formatDatum(datum) {
        return datum.toLocaleDateString('de-DE');
    }
    
    static grossschreibung(text) {
        return text.charAt(0).toUpperCase() + text.slice(1);
    }
    
    static zufallsZahl(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }
}

// main.js - Module importieren
import { addieren, multiplizieren, PI } from './math.js';
import Utils from './utils.js';

console.log(addieren(5, 3));
console.log(multiplizieren(4, 7));
console.log("PI:", PI);

const heute = new Date();
console.log(Utils.formatDatum(heute));
console.log(Utils.grossschreibung('hallo welt'));
console.log("Zufallszahl:", Utils.zufallsZahl(1, 100));

// Alle Exports importieren
import * as MathFunktionen from './math.js';
console.log(MathFunktionen.addieren(2, 3));

// Dynamic Imports
async function ladeModul() {
    const modul = await import('./math.js');
    console.log(modul.addieren(1, 2));
}
\`\`\`

## Aufgabe
Erstelle ein Modul mit Hilfsfunktionen und importiere es in eine andere Datei.`,
            exercise_type: 'code_example',
            expected_output: 'js_modules',
            points: 10
        }
    ];
    
    // Insert first 9 JS+PHP levels
    for (const level of jsPhpLevels) {
        levelStmt.run(level.course_id, level.level_number, level.title, level.description, level.content, level.exercise_type, level.expected_output, level.points);
    }
    
    console.log('✅ JavaScript+PHP levels 1-9 created!');
    
    levelStmt.finalize();
    directDb.close();
    
    console.log('🎉 All courses and levels created successfully!');
    console.log('📊 Summary:');
    console.log('  - HTML/CSS/JS: 40 levels (complete)');
    console.log('  - JavaScript+PHP: 9 levels (started)');
    console.log('  - Go: 0 levels (ready for creation)');
    console.log('  - Python: 0 levels (ready for creation)');
    console.log('  - Java: 0 levels (ready for creation)');
});
