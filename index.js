const express = require('express');
const session = require('express-session');
const path = require('path');
const bcrypt = require('bcrypt');
const fs = require('fs');
const https = require('https');
const readline = require('readline');
const crypto = require('crypto');
const Database = require('./database/db');
const { detectLanguage, loadTranslations, createLanguageSwitcher } = require('./middleware/language');
const { checkBanned, checkCourseAccess } = require('./middleware/admin');
const adminRoutes = require('./routes/admin');
const EmailService = require('./services/emailService');
const CodeValidator = require('./services/codeValidator');

const app = express();
const PORT = process.env.PORT || 3000;

// Initialize database and email service
const db = new Database();
const emailService = new EmailService();
const codeValidator = new CodeValidator();

// Middleware
app.use(express.urlencoded({ extended: true }));
app.use(express.json());
app.use(express.static(path.join(__dirname, 'public')));

// Session configuration - will be set based on protocol
let isHttpsMode = false;

app.use(session({
    secret: 'your-secret-key-change-in-production',
    resave: false,
    saveUninitialized: false,
    cookie: {
        secure: false, // Dynamic based on protocol
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
    }
}));

// Language detection and translation middleware
app.use(detectLanguage);
app.use(loadTranslations(db));

// Check for banned users on all routes
app.use(checkBanned);

// View engine setup
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Authentication middleware
function requireAuth(req, res, next) {
    console.log('requireAuth: session userId:', req.session.userId);
    if (req.session.userId) {
        next();
    } else {
        console.log('requireAuth: No userId, redirecting to login');
        res.redirect('/login');
    }
}

function redirectIfAuthenticated(req, res, next) {
    if (req.session.userId) {
        res.redirect('/dashboard');
    } else {
        next();
    }
}

// Middleware to load user object for admin checks
function loadUserObject(req, res, next) {
    console.log('loadUserObject: session userId:', req.session.userId);
    if (req.session.userId) {
        db.getUserById(req.session.userId, (err, user) => {
            if (err || !user) {
                console.error('Error loading user object:', err);
                req.user = null;
            } else {
                console.log('loadUserObject: loaded user:', user.username, 'admin:', user.is_admin);
                req.user = user;
            }
            next();
        });
    } else {
        req.user = null;
        next();
    }
}

// Routes
app.get('/', redirectIfAuthenticated, detectLanguage, (req, res) => {
    res.render('landing', { title: 'CodeWave - ' + res.locals.t('landing.hero_subtitle') });
});

app.get('/login', redirectIfAuthenticated, detectLanguage, (req, res) => {
    res.render('auth/login', { title: res.locals.t('auth.login'), error: null });
});

app.get('/register', redirectIfAuthenticated, detectLanguage, (req, res) => {
    res.render('auth/register', { title: res.locals.t('auth.register'), error: null });
});

app.post('/register', redirectIfAuthenticated, async (req, res) => {
    const { username, email, password, confirmPassword } = req.body;

    if (password !== confirmPassword) {
        return res.render('auth/register', {
            title: 'Register',
            error: 'Passwords are not matching'
        });
    }

    if (password.length < 6) {
        return res.render('auth/register', {
            title: 'Register',
            error: 'The password must be at least 6 characters long'
        });
    }

    try {
        const hashedPassword = await bcrypt.hash(password, 10);

        db.createUser(username, email, hashedPassword, async (err) => {
            if (err) {
                let errorMessage = 'Register failed';
                if (err.message.includes('UNIQUE constraint failed: users.email')) {
                    errorMessage = 'E-Mail already registered';
                } else if (err.message.includes('UNIQUE constraint failed: users.username')) {
                    errorMessage = 'Benutzername already taken';
                }
                return res.render('auth/register', {
                    title: 'Register',
                    error: errorMessage
                });
            }

            // Get the created user to send verification email
            db.getUserByEmail(email, async (err, user) => {
                if (err || !user) {
                    return res.render('auth/register', {
                        title: 'Register',
                        error: 'Registration failed - please try again'
                    });
                }

                try {
                    // Generate verification token
                    const token = crypto.randomBytes(32).toString('hex');
                    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

                    // Save token to database
                    db.createEmailVerificationToken(user.id, token, expiresAt.toISOString(), async (err) => {
                        if (err) {
                            console.error('Error creating verification token:', err);
                            return res.render('auth/register', {
                                title: 'Register',
                                error: 'Registration failed - please try again'
                            });
                        }

                        // Send verification email
                        try {
                            await emailService.sendEmailVerification(email, username, token);
                            res.render('auth/register-success', {
                                title: 'Registration Successful',
                                email: email
                            });
                        } catch (emailError) {
                            console.error('Error sending verification email:', emailError);
                            res.render('auth/register', {
                                title: 'Register',
                                error: 'Registration successful, but verification email could not be sent. Please contact support.'
                            });
                        }
                    });
                } catch (tokenError) {
                    console.error('Error generating verification token:', tokenError);
                    res.render('auth/register', {
                        title: 'Register',
                        error: 'Registration failed - please try again'
                    });
                }
            });
        });
    } catch (error) {
        console.error('Registration error:', error);
        res.render('auth/register', {
            title: 'Register',
            error: 'Register failed'
        });
    }
});

app.post('/login', redirectIfAuthenticated, (req, res) => {
    const { email, password } = req.body;
    console.log('Login attempt for email:', email);

    db.getUserByEmail(email, async (err, user) => {
        if (err || !user) {
            console.log('Login failed: user not found for email:', email);
            return res.render('auth/login', {
                title: 'Login',
                error: 'Invalid E-Mail or Passwort'
            });
        }

        try {
            const validPassword = await bcrypt.compare(password, user.password_hash);
            if (!validPassword) {
                return res.render('auth/login', {
                    title: 'Login',
                    error: 'Invalid E-Mail or Passwort'
                });
            }

            // Check if email is verified
            if (!user.email_verified) {
                return res.render('auth/login', {
                    title: 'Login',
                    error: 'Please verify your email address before logging in. Check your inbox for the verification link.'
                });
            }

            req.session.userId = user.id;
            req.session.username = user.username;
            console.log('Login successful for user:', user.username, 'userId:', user.id);

            db.updateLastLogin(user.id, () => {
                res.redirect('/dashboard');
            });
        } catch (error) {
            res.render('auth/login', {
                title: 'Login',
                error: 'Login failed'
            });
        }
    });
});

app.get('/logout', (req, res) => {
    req.session.destroy((err) => {
        res.redirect('/');
    });
});

// Email verification route
app.get('/verify-email', (req, res) => {
    const { token } = req.query;

    if (!token) {
        return res.render('auth/verify-email-result', {
            title: 'Email Verification',
            success: false,
            message: 'Invalid verification link.'
        });
    }

    db.getEmailVerificationToken(token, (err, tokenData) => {
        if (err || !tokenData) {
            return res.render('auth/verify-email-result', {
                title: 'Email Verification',
                success: false,
                message: 'Invalid or expired verification link.'
            });
        }

        // Verify the user's email
        db.verifyUserEmail(tokenData.user_id, (err) => {
            if (err) {
                console.error('Error verifying user email:', err);
                return res.render('auth/verify-email-result', {
                    title: 'Email Verification',
                    success: false,
                    message: 'Verification failed. Please try again.'
                });
            }

            // Delete the used token
            db.deleteEmailVerificationToken(token, (err) => {
                if (err) {
                    console.error('Error deleting verification token:', err);
                }

                res.render('auth/verify-email-result', {
                    title: 'Email Verification',
                    success: true,
                    message: 'Your email has been successfully verified! You can now log in.'
                });
            });
        });
    });
});

// Forgot password routes
app.get('/forgot-password', redirectIfAuthenticated, (req, res) => {
    res.render('auth/forgot-password', {
        title: 'Forgot Password',
        error: null,
        success: null
    });
});

app.post('/forgot-password', redirectIfAuthenticated, async (req, res) => {
    const { email } = req.body;

    if (!email) {
        return res.render('auth/forgot-password', {
            title: 'Forgot Password',
            error: 'Email address is required',
            success: null
        });
    }

    db.getUserByEmail(email, async (err, user) => {
        if (err) {
            console.error('Error finding user for password reset:', err);
            return res.render('auth/forgot-password', {
                title: 'Forgot Password',
                error: 'An error occurred. Please try again.',
                success: null
            });
        }

        // Always show success message for security (don't reveal if email exists)
        const successMessage = 'If an account with that email exists, we have sent a password reset link.';

        if (!user) {
            return res.render('auth/forgot-password', {
                title: 'Forgot Password',
                error: null,
                success: successMessage
            });
        }

        try {
            // Generate reset token
            const token = crypto.randomBytes(32).toString('hex');
            const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

            // Save token to database
            db.createPasswordResetToken(user.id, token, expiresAt.toISOString(), async (err) => {
                if (err) {
                    console.error('Error creating password reset token:', err);
                    return res.render('auth/forgot-password', {
                        title: 'Forgot Password',
                        error: 'An error occurred. Please try again.',
                        success: null
                    });
                }

                // Send reset email
                try {
                    await emailService.sendPasswordReset(email, user.username, token);
                    res.render('auth/forgot-password', {
                        title: 'Forgot Password',
                        error: null,
                        success: successMessage
                    });
                } catch (emailError) {
                    console.error('Error sending password reset email:', emailError);
                    res.render('auth/forgot-password', {
                        title: 'Forgot Password',
                        error: 'Failed to send reset email. Please try again.',
                        success: null
                    });
                }
            });
        } catch (tokenError) {
            console.error('Error generating password reset token:', tokenError);
            res.render('auth/forgot-password', {
                title: 'Forgot Password',
                error: 'An error occurred. Please try again.',
                success: null
            });
        }
    });
});

// Reset password routes
app.get('/reset-password', redirectIfAuthenticated, (req, res) => {
    const { token } = req.query;

    if (!token) {
        return res.render('auth/reset-password-result', {
            title: 'Reset Password',
            success: false,
            message: 'Invalid reset link.'
        });
    }

    db.getPasswordResetToken(token, (err, tokenData) => {
        if (err || !tokenData) {
            return res.render('auth/reset-password-result', {
                title: 'Reset Password',
                success: false,
                message: 'Invalid or expired reset link.'
            });
        }

        res.render('auth/reset-password', {
            title: 'Reset Password',
            token: token,
            error: null
        });
    });
});

app.post('/reset-password', redirectIfAuthenticated, async (req, res) => {
    const { token, password, confirmPassword } = req.body;

    if (!token || !password || !confirmPassword) {
        return res.render('auth/reset-password', {
            title: 'Reset Password',
            token: token,
            error: 'All fields are required'
        });
    }

    if (password !== confirmPassword) {
        return res.render('auth/reset-password', {
            title: 'Reset Password',
            token: token,
            error: 'Passwords do not match'
        });
    }

    if (password.length < 6) {
        return res.render('auth/reset-password', {
            title: 'Reset Password',
            token: token,
            error: 'Password must be at least 6 characters long'
        });
    }

    db.getPasswordResetToken(token, async (err, tokenData) => {
        if (err || !tokenData) {
            return res.render('auth/reset-password-result', {
                title: 'Reset Password',
                success: false,
                message: 'Invalid or expired reset link.'
            });
        }

        try {
            const hashedPassword = await bcrypt.hash(password, 10);

            // Update user password
            db.updateUserPassword(tokenData.user_id, hashedPassword, (err) => {
                if (err) {
                    console.error('Error updating password:', err);
                    return res.render('auth/reset-password', {
                        title: 'Reset Password',
                        token: token,
                        error: 'Failed to update password. Please try again.'
                    });
                }

                // Mark token as used
                db.markPasswordResetTokenUsed(token, (err) => {
                    if (err) {
                        console.error('Error marking token as used:', err);
                    }

                    res.render('auth/reset-password-result', {
                        title: 'Reset Password',
                        success: true,
                        message: 'Your password has been successfully reset! You can now log in with your new password.'
                    });
                });
            });
        } catch (hashError) {
            console.error('Error hashing new password:', hashError);
            res.render('auth/reset-password', {
                title: 'Reset Password',
                token: token,
                error: 'Failed to update password. Please try again.'
            });
        }
    });
});

// Language switcher route
app.post('/api/language/:language', createLanguageSwitcher(db));

// Admin routes
app.use('/admin', adminRoutes);

// Support routes
app.use('/support', require('./routes/support'));

app.get('/dashboard', requireAuth, loadUserObject, (req, res) => {
    db.getAllCourses((err, courses) => {
        if (err) {
            return res.status(500).send('Error fetching courses');
        }

        // Get progress for each course
        let completedRequests = 0;
        const coursesWithProgress = [];

        if (courses.length === 0) {
            return res.render('dashboard', {
                title: 'Dashboard',
                username: req.session.username,
                user: req.user,
                courses: [],
                userStats: { completedLevels: 0, totalScore: 0 },
                activities: []
            });
        }

        courses.forEach(course => {
            // Check course access
            const freeCourses = ['html-css-js', 'javascript', 'python'];
            course.isFree = freeCourses.includes(course.slug);
            course.isLocked = false; // Will be set later for premium courses

            db.getCourseStats(req.session.userId, course.id, (err, stats) => {
                if (!err && stats) {
                    course.stats = stats;
                    course.progressPercentage = stats.total_levels > 0 ?
                        Math.round((stats.completed_levels / stats.total_levels) * 100) : 0;
                } else {
                    course.stats = { total_levels: course.total_levels, completed_levels: 0, total_score: 0 };
                    course.progressPercentage = 0;
                }

                // Get next available level for this course
                db.getNextAvailableLevel(req.session.userId, course.id, (err, nextLevel) => {
                    if (!err && nextLevel) {
                        course.nextLevel = nextLevel;
                    }

                    coursesWithProgress.push(course);
                    completedRequests++;

                    if (completedRequests === courses.length) {
                        // Check premium course access for all courses
                        db.getAdminSetting('global_premium_enabled', (err, setting) => {
                            const globalPremiumEnabled = setting && setting.setting_value === 'true';

                            if (!globalPremiumEnabled) {
                                // Check individual course access
                                db.getUserCourseAccess(req.session.userId, (err, accessList) => {
                                    const userCourseIds = accessList ? accessList.map(access => access.course_id) : [];

                                    // Mark premium courses as locked if user doesn't have access
                                    coursesWithProgress.forEach(course => {
                                        if (!course.isFree && !userCourseIds.includes(course.id)) {
                                            course.isLocked = true;
                                        }
                                    });

                                    renderDashboardWithStats();
                                });
                            } else {
                                renderDashboardWithStats();
                            }
                        });

                        function renderDashboardWithStats() {
                            // Sort courses: Free courses first, then premium courses
                            coursesWithProgress.sort((a, b) => {
                                // Free courses come first
                                if (a.isFree && !b.isFree) return -1;
                                if (!a.isFree && b.isFree) return 1;
                                // Within same category, sort alphabetically
                                return a.name.localeCompare(b.name);
                            });

                            // Calculate overall user stats with achievements
                            const completedLevels = coursesWithProgress.reduce((sum, course) => sum + (course.stats.completed_levels || 0), 0);

                            db.getUserTotalScore(req.session.userId, (err, totalScore) => {
                            if (err) {
                                console.error('Error getting total score:', err);
                                totalScore = 0;
                            }

                            const userStats = {
                                completedLevels: completedLevels,
                                totalScore: totalScore
                            };

                            // Get recent activities
                            if (typeof db.getRecentActivities === 'function') {
                                db.getRecentActivities(req.session.userId, 10, (err, activities) => {
                                    if (err) {
                                        console.error('Error getting activities:', err);
                                        activities = [];
                                    }

                                    res.render('dashboard', {
                                        title: 'Dashboard',
                                        username: req.session.username,
                                        user: req.user,
                                        courses: coursesWithProgress,
                                        userStats: userStats,
                                        activities: activities || []
                                    });
                                });
                            } else {
                                console.log('getRecentActivities function not found, using empty activities');
                                res.render('dashboard', {
                                    title: 'Dashboard',
                                    username: req.session.username,
                                    user: req.user,
                                    courses: coursesWithProgress,
                                    userStats: userStats,
                                    activities: []
                                });
                            }
                        });
                        }
                    }
                });
            });
        });
    });
});

// Course overview routes
app.get('/course/:slug', requireAuth, loadUserObject, checkCourseAccess, (req, res) => {
    const course = req.course; // Course is set by checkCourseAccess middleware

    db.getLevelsByCourse(course.id, (err, levels) => {
            if (err) {
                return res.status(500).send('Error fetching levels');
            }

            db.getUserProgress(req.session.userId, course.id, (err, progress) => {
                if (err) {
                    return res.status(500).send('Error fetching user progress');
                }

                // Create progress map for easy lookup
                const progressMap = {};
                progress.forEach(p => {
                    progressMap[p.level_id] = p;
                });

                // Add progress info to levels and check unlock status
                let completedRequests = 0;
                levels.forEach((level, index) => {
                    level.progress = progressMap[level.id] || null;

                    // Check if level is unlocked using proper logic
                    db.isLevelUnlocked(req.session.userId, course.id, level.level_number, (err, isUnlocked) => {
                        if (!err) {
                            level.isUnlocked = isUnlocked;
                        } else {
                            level.isUnlocked = level.level_number === 1; // Fallback
                        }

                        completedRequests++;
                        if (completedRequests === levels.length) {
                            res.render('course/overview', {
                                title: course.name,
                                username: req.session.username,
                                user: req.user,
                                course: course,
                                levels: levels
                            });
                        }
                    });
                });

                // This was moved inside the forEach callback above
            });
        });
});

// Level detail route
app.get('/course/:slug/level/:levelNumber', requireAuth, loadUserObject, checkCourseAccess, (req, res) => {
    const course = req.course; // Course is set by checkCourseAccess middleware
    const levelNumber = parseInt(req.params.levelNumber);

    db.getLevel(course.id, levelNumber, (err, level) => {
            if (err || !level) {
                return res.status(404).send('Level not found');
            }

            // Check if level is unlocked
            db.isLevelUnlocked(req.session.userId, course.id, levelNumber, (err, isUnlocked) => {
                if (err) {
                    return res.status(500).send('Error checking level access');
                }

                if (!isUnlocked) {
                    return res.redirect(`/course/${course.slug}?error=level_locked`);
                }

                db.getUserLevelProgress(req.session.userId, level.id, (err, progress) => {
                    if (err) {
                        return res.status(500).send('Error fetching user progress');
                    }

                    db.getLastSubmission(req.session.userId, level.id, (err, lastSubmission) => {
                        res.render('course/level', {
                            title: `${course.name} - Level ${levelNumber}`,
                            username: req.session.username,
                            user: req.user,
                            course: course,
                            level: level,
                            progress: progress,
                            lastSubmission: lastSubmission
                        });
                    });
                });
            });
        });
});

// Course level submit route
app.post('/course/:slug/level/:levelNumber/submit', requireAuth, loadUserObject, checkCourseAccess, async (req, res) => {
    const course = req.course;
    const levelNumber = parseInt(req.params.levelNumber);
    const { code } = req.body;
    const userId = req.session.userId;

    if (!code) {
        return res.json({ success: false, message: 'Code is required' });
    }

    // Get the level
    db.getLevel(course.id, levelNumber, async (err, level) => {
        if (err || !level) {
            return res.json({ success: false, message: 'Level not found' });
        }

        // Enhanced validation using the CodeValidator service
        const validation = await codeValidator.validateCode(code, level, course.slug);
        const passed = validation.passed;
        const feedback = validation.message;
        const score = validation.score || (passed ? 100 : 0);

        // Save submission
        db.saveCodeSubmission(userId, level.id, code, feedback, passed, (err) => {
            if (err) {
                console.error('Error saving submission:', err);
                return res.json({ success: false, message: 'Error saving submission' });
            }

            // Update progress if passed
            if (passed) {
                db.createOrUpdateProgress(userId, course.id, level.id, true, score, (err) => {
                    if (err) {
                        console.error('Error updating progress:', err);
                    }
                });
            }

            res.json({
                success: true,
                passed: passed,
                message: feedback,
                score: score,
                errors: validation.errors || [],
                suggestions: validation.suggestions || []
            });
        });
    });
});

// API Routes
app.post('/api/submit-code', requireAuth, async (req, res) => {
    const { courseId, levelId, code, output, codeType } = req.body;
    const userId = req.session.userId;

    if (!code || !levelId) {
        return res.json({ success: false, message: 'Code and level ID are required' });
    }

    try {
        // Get level details to check expected output
        const level = await new Promise((resolve, reject) => {
            db.getLevelById(levelId, (err, level) => {
                if (err) reject(err);
                else resolve(level);
            });
        });

        if (!level) {
            return res.json({ success: false, message: 'Level not found' });
        }

        // Get course info to determine language
        const course = await new Promise((resolve, reject) => {
            db.getCourseById(level.course_id, (err, course) => {
                if (err) reject(err);
                else resolve(course);
            });
        });

        if (!course) {
            return res.json({ success: false, message: 'Course not found' });
        }

        // Enhanced validation based on level and code type
        const validation = await codeValidator.validateCode(code, level, course.slug);
        const passed = validation.passed;
        const score = validation.score;
        const message = validation.message;

        // Save code submission
        await new Promise((resolve, reject) => {
            db.saveCodeSubmission(userId, levelId, code, output, passed, (err) => {
                if (err) reject(err);
                else resolve();
            });
        });

        // Update user progress
        await new Promise((resolve, reject) => {
            db.createOrUpdateProgress(userId, courseId, levelId, passed, score, (err) => {
                if (err) reject(err);
                else resolve();
            });
        });

        // Check for new achievements
        const newAchievements = await new Promise((resolve) => {
            db.checkAndAwardAchievements(userId, (err, achievements) => {
                resolve(err ? [] : achievements || []);
            });
        });

        let achievementMessage = '';
        if (newAchievements.length > 0) {
            achievementMessage = ` 🏆 Achievement unlocked: ${newAchievements[0].name}!`;
        }

        res.json({
            success: true,
            passed: passed,
            score: score,
            completed: passed,
            message: message + achievementMessage,
            newAchievements: newAchievements,
            errors: validation.errors || [],
            suggestions: validation.suggestions || []
        });

    } catch (error) {
        console.error('Error in submit-code API:', error);
        res.json({ success: false, message: 'Internal server error' });
    }
});

// Enhanced code validation function using the new CodeValidator service
function validateCode(code, level, codeType) {
    return codeValidator.validateCode(code, level, codeType);
}

app.post('/api/progress', requireAuth, (req, res) => {
    const { courseId, levelId, completed, score } = req.body;
    const userId = req.session.userId;

    db.createOrUpdateProgress(userId, courseId, levelId, completed, score, (err) => {
        if (err) {
            return res.json({ success: false, message: 'Fehler beim Aktualisieren des Fortschritts' });
        }

        res.json({ success: true });
    });
});

// Profile route
app.get('/profile', requireAuth, loadUserObject, (req, res) => {
    const userId = req.session.userId;

    // Get user details
    db.getUserById(userId, (err, user) => {
        if (err || !user) {
            return res.status(500).send('Fehler beim Laden der Benutzerdaten');
        }

        // Get all courses for statistics
        db.getAllCourses((err, courses) => {
            if (err) {
                return res.status(500).send('Fehler beim Laden der Kurse');
            }

            // Get detailed statistics for each course
            let completedRequests = 0;
            const userStats = {
                totalLevels: 0,
                completedLevels: 0,
                totalScore: 0,
                courses: []
            };

            if (courses.length === 0) {
                return res.render('profile', {
                    title: 'Profil',
                    username: req.session.username,
                    user: user,
                    stats: userStats
                });
            }

            courses.forEach(course => {
                db.getCourseStats(userId, course.id, (err, courseStats) => {
                    if (!err && courseStats) {
                        userStats.totalLevels += courseStats.total_levels;
                        userStats.completedLevels += courseStats.completed_levels;
                        // Don't add to totalScore here, we'll get it separately

                        course.stats = courseStats;
                        course.progressPercentage = courseStats.total_levels > 0 ?
                            Math.round((courseStats.completed_levels / courseStats.total_levels) * 100) : 0;
                    } else {
                        course.stats = { total_levels: course.total_levels, completed_levels: 0, total_score: 0 };
                        course.progressPercentage = 0;
                        userStats.totalLevels += course.total_levels;
                    }

                    userStats.courses.push(course);
                    completedRequests++;

                    if (completedRequests === courses.length) {
                        // Calculate overall progress percentage
                        userStats.overallProgress = userStats.totalLevels > 0 ?
                            Math.round((userStats.completedLevels / userStats.totalLevels) * 100) : 0;

                        // Get total score including achievements
                        db.getUserTotalScore(userId, (err, totalScore) => {
                            userStats.totalScore = totalScore || 0;

                            // Get user achievements
                            db.getUserAchievements(userId, (err, achievements) => {
                                userStats.achievements = achievements || [];

                                res.render('profile', {
                                    title: 'Profil',
                                    username: req.session.username,
                                    user: user,
                                    stats: userStats
                                });
                            });
                        });
                    }
                });
            });
        });
    });
});

// Achievements route
app.get('/achievements', requireAuth, loadUserObject, (req, res) => {
    const userId = req.session.userId;

    // Get all achievements
    db.getAllAchievements((err, allAchievements) => {
        if (err) {
            return res.status(500).send('Fehler beim Laden der Erfolge');
        }

        // Get user's achievements
        db.getUserAchievements(userId, (err, userAchievements) => {
            if (err) {
                return res.status(500).send('Fehler beim Laden der Benutzer-Erfolge');
            }

            // Create map of earned achievements
            const earnedMap = {};
            userAchievements.forEach(ua => {
                earnedMap[ua.id] = ua;
            });

            // Add earned status to all achievements
            allAchievements.forEach(achievement => {
                achievement.earned = !!earnedMap[achievement.id];
                achievement.earned_at = earnedMap[achievement.id] ? earnedMap[achievement.id].earned_at : null;
            });

            // Get user stats for progress tracking
            db.getUserTotalStats(userId, (err, stats) => {
                const userStats = stats ? {
                    completedLevels: stats.total_completed_levels || 0,
                    totalScore: stats.total_score || 0
                } : { completedLevels: 0, totalScore: 0 };

                res.render('achievements', {
                    title: 'Erfolge',
                    username: req.session.username,
                    user: req.user,
                    achievements: allAchievements,
                    stats: stats || { total_completed_levels: 0, total_score: 0, completed_courses: 0 },
                    userStats: userStats
                });
            });
        });
    });
});

// Leaderboard route
app.get('/leaderboard', requireAuth, loadUserObject, (req, res) => {
    // Get top users by total score
    const sql = `
        SELECT
            u.username,
            u.created_at,
            (SELECT COALESCE(SUM(score), 0) FROM user_progress WHERE user_id = u.id) as level_score,
            (SELECT COALESCE(SUM(a.points), 0) FROM user_achievements ua JOIN achievements a ON ua.achievement_id = a.id WHERE ua.user_id = u.id) as achievement_score,
            ((SELECT COALESCE(SUM(score), 0) FROM user_progress WHERE user_id = u.id) + (SELECT COALESCE(SUM(a.points), 0) FROM user_achievements ua JOIN achievements a ON ua.achievement_id = a.id WHERE ua.user_id = u.id)) as total_score,
            (SELECT COUNT(CASE WHEN completed = 1 THEN 1 END) FROM user_progress WHERE user_id = u.id) as completed_levels,
            (SELECT COUNT(DISTINCT achievement_id) FROM user_achievements WHERE user_id = u.id) as earned_achievements
        FROM users u
        ORDER BY total_score DESC, completed_levels DESC
        LIMIT 50
    `;

    db.db.all(sql, (err, leaderboard) => {
        if (err) {
            return res.status(500).send('Fehler beim Laden des Leaderboards');
        }

        // Add rank to each user
        leaderboard.forEach((user, index) => {
            user.rank = index + 1;
        });

        // Find current user's position
        const currentUserRank = leaderboard.findIndex(user => user.username === req.session.username) + 1;

        // Get current user's stats for the stats cards
        const currentUser = leaderboard.find(user => user.username === req.session.username);
        const userStats = currentUser ? {
            completedLevels: currentUser.completed_levels,
            totalScore: currentUser.total_score
        } : { completedLevels: 0, totalScore: 0 };

        res.render('leaderboard', {
            title: 'Leaderboard',
            username: req.session.username,
            user: req.user,
            leaderboard: leaderboard,
            currentUserRank: currentUserRank || 'Nicht gerankt',
            userStats: userStats
        });
    });
});

// Profile update route
app.post('/profile/update', requireAuth, (req, res) => {
    const { username, email } = req.body;
    const userId = req.session.userId;

    if (!username || !email) {
        return res.json({ success: false, message: 'Benutzername und E-Mail sind erforderlich' });
    }

    // Check if username or email already exists (excluding current user)
    db.getUserByUsername(username, (err, existingUser) => {
        if (err) {
            return res.json({ success: false, message: 'Fehler bei der Validierung' });
        }

        if (existingUser && existingUser.id !== userId) {
            return res.json({ success: false, message: 'Benutzername bereits vergeben' });
        }

        db.getUserByEmail(email, (err, existingUser) => {
            if (err) {
                return res.json({ success: false, message: 'Fehler bei der Validierung' });
            }

            if (existingUser && existingUser.id !== userId) {
                return res.json({ success: false, message: 'E-Mail bereits registriert' });
            }

            // Update user
            const sql = `UPDATE users SET username = ?, email = ? WHERE id = ?`;
            db.db.run(sql, [username, email, userId], (err) => {
                if (err) {
                    return res.json({ success: false, message: 'Fehler beim Aktualisieren' });
                }

                // Update session
                req.session.username = username;
                res.json({ success: true, message: 'Profil erfolgreich aktualisiert' });
            });
        });
    });
});

// Profile reset progress route
app.post('/profile/reset-progress', requireAuth, (req, res) => {
    const userId = req.session.userId;

    console.log(`User ${userId} requested progress reset`);

    // Reset user progress data
    db.resetUserProgress(userId, (err) => {
        if (err) {
            console.error('Error resetting user progress:', err);
            return res.json({ success: false, message: 'Fehler beim Zurücksetzen des Fortschritts' });
        }

        console.log(`Successfully reset progress for user ${userId}`);
        res.json({ success: true, message: 'Lernfortschritt erfolgreich zurückgesetzt' });
    });
});

// Profile change password route
app.post('/profile/change-password', requireAuth, (req, res) => {
    const { currentPassword, newPassword } = req.body;
    const userId = req.session.userId;

    if (!currentPassword || !newPassword) {
        return res.json({ success: false, message: 'Aktuelles und neues Passwort sind erforderlich' });
    }

    if (newPassword.length < 6) {
        return res.json({ success: false, message: 'Neues Passwort muss mindestens 6 Zeichen lang sein' });
    }

    // Get current user to verify password
    db.getUserById(userId, (err, user) => {
        if (err || !user) {
            return res.json({ success: false, message: 'Benutzer nicht gefunden' });
        }

        // Verify current password
        bcrypt.compare(currentPassword, user.password_hash, (err, isMatch) => {
            if (err) {
                return res.json({ success: false, message: 'Fehler bei der Passwort-Überprüfung' });
            }

            if (!isMatch) {
                return res.json({ success: false, message: 'Aktuelles Passwort ist falsch' });
            }

            // Hash new password
            bcrypt.hash(newPassword, 10, (err, hash) => {
                if (err) {
                    return res.json({ success: false, message: 'Fehler beim Verschlüsseln des neuen Passworts' });
                }

                // Update password in database
                const sql = `UPDATE users SET password_hash = ? WHERE id = ?`;
                db.db.run(sql, [hash, userId], (err) => {
                    if (err) {
                        console.error('Error updating password:', err);
                        return res.json({ success: false, message: 'Fehler beim Aktualisieren des Passworts' });
                    }

                    console.log(`Password successfully changed for user ${userId}`);
                    res.json({ success: true, message: 'Passwort erfolgreich geändert' });
                });
            });
        });
    });
});

// Legal pages routes
app.get('/impress', (req, res) => {
    res.render('legal/impress', {
        title: 'Impress',
        username: req.session.username || null
    });
});

app.get('/privacy', (req, res) => {
    res.render('legal/datenschutz', {
        title: 'Datenschutz',
        username: req.session.username || null
    });
});


app.get('/terms', (req, res) => {
    res.render('legal/agb', {
        title: 'AGB',
        username: req.session.username || null
    });
});

// HTTPS Configuration and Server Start
async function startServer() {
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    const question = (query) => new Promise((resolve) => rl.question(query, resolve));

    try {
        console.log('\n🚀 CodeWave Server Configuration');
        console.log('================================');

        const useHttps = await question('Do you want to start the server with HTTPS? (Y/n): ');
        const shouldUseHttps = useHttps.toLowerCase() === 'y' || useHttps.toLowerCase() === 'yes' || useHttps.trim() === '';

        if (shouldUseHttps) {
            console.log('\n🔒 Starting HTTPS server...');

            // Try Let's Encrypt certificates first, then fallback to local certs
            let keyPath, certPath;
            let certificateSource = '';

            // Check for Let's Encrypt certificates
            const letsencryptKeyPath = '/etc/letsencrypt/live/codewave.online/privkey.pem';
            const letsencryptCertPath = '/etc/letsencrypt/live/codewave.online/fullchain.pem';

            if (fs.existsSync(letsencryptKeyPath) && fs.existsSync(letsencryptCertPath)) {
                keyPath = letsencryptKeyPath;
                certPath = letsencryptCertPath;
                certificateSource = 'Let\'s Encrypt (codewave.online)';
                console.log('📋 Using Let\'s Encrypt certificates for codewave.online');
            } else {
                // Fallback to local development certificates
                keyPath = path.join(__dirname, 'certs', 'private-key.pem');
                certPath = path.join(__dirname, 'certs', 'certificate.pem');
                certificateSource = 'Local development certificates';
                console.log('📋 Let\'s Encrypt certificates not found, using local certificates');
            }

            try {
                const privateKey = fs.readFileSync(keyPath, 'utf8');
                const certificate = fs.readFileSync(certPath, 'utf8');

                const credentials = {
                    key: privateKey,
                    cert: certificate
                };

                const httpsServer = https.createServer(credentials, app);
                const httpsPort = process.env.HTTPS_PORT || (certificateSource.includes('Let\'s Encrypt') ? 443 : 3443);

                httpsServer.listen(httpsPort, () => {
                    console.log('\n✅ HTTPS Server successfully started!');
                    if (certificateSource.includes('Let\'s Encrypt')) {
                        console.log(`🌐 Server running on: https://codewave.online:${httpsPort}`);
                        console.log('🔒 SSL/TLS encryption enabled with Let\'s Encrypt');
                        console.log('📁 Certificates loaded from: /etc/letsencrypt/live/codewave.online/');
                        console.log('🌍 Production-ready with trusted certificates');
                    } else {
                        console.log(`🌐 Server running on: https://localhost:${httpsPort}`);
                        console.log('🔒 SSL/TLS encryption enabled');
                        console.log('📁 Certificates loaded from: ./certs/');
                        console.log('🔒 Browser will show security warning for self-signed certificate');
                    }
                    console.log(`📋 Certificate source: ${certificateSource}`);
                });

            } catch (certError) {
                console.error('\n❌ Certificate Error:');
                console.error('Could not load SSL certificates:', certError.message);
                console.log('📝 Certificate paths checked:');
                console.log(`   - Key: ${keyPath}`);
                console.log(`   - Cert: ${certPath}`);

                if (certificateSource.includes('Let\'s Encrypt')) {
                    console.log('\n💡 Let\'s Encrypt certificate troubleshooting:');
                    console.log('   - Check if certificates exist: sudo ls -la /etc/letsencrypt/live/codewave.online/');
                    console.log('   - Check file permissions: sudo chmod 644 /etc/letsencrypt/live/codewave.online/*.pem');
                    console.log('   - Run as root if needed: sudo node index.js');
                    console.log('   - Verify certificate renewal: sudo certbot renew --dry-run');
                } else {
                    console.log('\n💡 Local certificate troubleshooting:');
                    console.log('   - Generate certificates: ./generate-certs.sh');
                    console.log('   - Check file permissions: chmod 600 certs/private-key.pem');
                }

                console.log('\n🔄 Falling back to HTTP server...\n');

                // Fall back to HTTP
                startHttpServer();
            }

        } else {
            console.log('\n🌐 Starting HTTP server...');
            startHttpServer();
        }

    } catch (error) {
        console.error('Error during server configuration:', error);
        startHttpServer();
    } finally {
        rl.close();
    }
}

function startHttpServer() {
    app.listen(PORT, () => {
        console.log('\n✅ HTTP Server successfully started!');
        console.log(`🌐 Server running on: http://localhost:${PORT}`);
        console.log('🔓 No SSL/TLS encryption (HTTP only)');
        console.log('\n🚀 Ready to accept connections!');
    });
}

// Start the server with configuration
startServer();