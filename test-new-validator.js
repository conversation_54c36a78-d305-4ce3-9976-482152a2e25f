const CodeValidator = require('./services/codeValidator');
const Database = require('./database/db');

console.log('🧪 Testing new level-specific code validator...\n');

const db = new Database();
const validator = new CodeValidator();

// Test HTML validation for different levels
async function testHTMLValidation() {
    console.log('🌐 Testing HTML validation...');
    
    // Get HTML course levels
    db.getLevelsByCourse('html-css-js', (err, levels) => {
        if (err) {
            console.error('❌ Error fetching HTML levels:', err);
            return;
        }

        console.log(`📊 Found ${levels.length} HTML levels`);

        // Test Level 1: HTML Structure
        const level1 = levels.find(l => l.level_number === 1);
        if (level1) {
            console.log(`\n📝 Testing Level 1: ${level1.title}`);
            console.log(`Expected output: ${level1.expected_output}`);
            
            const testCode1 = `
<!DOCTYPE html>
<html>
<head>
    <title>Test</title>
</head>
<body>
    <h1>Hello World</h1>
    <p>This is a paragraph.</p>
</body>
</html>`;

            validator.validateHTML(testCode1, 1, level1).then(result => {
                console.log(`✅ Result: ${result.passed ? 'PASSED' : 'FAILED'} (Score: ${result.score})`);
                console.log(`Message: ${result.message}`);
                if (result.errors.length > 0) {
                    console.log(`Errors: ${result.errors.join(', ')}`);
                }
            }).catch(err => {
                console.error('❌ Validation error:', err);
            });
        }

        // Test Level 3: Links & Images
        const level3 = levels.find(l => l.level_number === 3);
        if (level3) {
            console.log(`\n📝 Testing Level 3: ${level3.title}`);
            console.log(`Expected output: ${level3.expected_output}`);
            
            const testCode3 = `
<a href="https://example.com">Click here</a>
<img src="https://via.placeholder.com/200" alt="Test image" width="200">`;

            validator.validateHTML(testCode3, 3, level3).then(result => {
                console.log(`✅ Result: ${result.passed ? 'PASSED' : 'FAILED'} (Score: ${result.score})`);
                console.log(`Message: ${result.message}`);
                if (result.errors.length > 0) {
                    console.log(`Errors: ${result.errors.join(', ')}`);
                }
            }).catch(err => {
                console.error('❌ Validation error:', err);
            });
        }

        // Test Boss Level 10
        const level10 = levels.find(l => l.level_number === 10);
        if (level10) {
            console.log(`\n🏆 Testing Boss Level 10: ${level10.title}`);
            console.log(`Expected output: ${level10.expected_output}`);
            
            const testCode10 = `
<!DOCTYPE html>
<html>
<head>
    <title>My Business Card</title>
    <style>
        body { font-family: Arial; }
        .card { border: 1px solid #ccc; padding: 20px; }
    </style>
</head>
<body>
    <nav>
        <a href="#home">Home</a>
        <a href="#about">About</a>
    </nav>
    <div class="card">
        <h1>John Doe</h1>
        <p>Web Developer</p>
        <img src="profile.jpg" alt="Profile">
    </div>
    <footer>
        <p>&copy; 2024 John Doe</p>
    </footer>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Page loaded');
        });
    </script>
</body>
</html>`;

            validator.validateHTML(testCode10, 10, level10).then(result => {
                console.log(`✅ Result: ${result.passed ? 'PASSED' : 'FAILED'} (Score: ${result.score})`);
                console.log(`Message: ${result.message}`);
                if (result.errors.length > 0) {
                    console.log(`Errors: ${result.errors.join(', ')}`);
                }
            }).catch(err => {
                console.error('❌ Validation error:', err);
            });
        }
    });
}

// Test JavaScript validation
async function testJavaScriptValidation() {
    console.log('\n⚡ Testing JavaScript validation...');
    
    // Get JavaScript course levels
    db.getLevelsByCourse('javascript-php', (err, levels) => {
        if (err) {
            console.error('❌ Error fetching JS levels:', err);
            return;
        }

        // Test Level 11: Variables (first JS level in html-css-js course)
        const jsLevel = levels.find(l => l.expected_output === 'js_variables');
        if (jsLevel) {
            console.log(`\n📝 Testing JS Variables: ${jsLevel.title}`);
            console.log(`Expected output: ${jsLevel.expected_output}`);
            
            const testCodeJS = `
let name = "John";
const age = 25;
var city = "Berlin";
console.log("Name:", name);
console.log("Age:", age);
console.log("City:", city);`;

            validator.validateJavaScript(testCodeJS, jsLevel.level_number, jsLevel).then(result => {
                console.log(`✅ Result: ${result.passed ? 'PASSED' : 'FAILED'} (Score: ${result.score})`);
                console.log(`Message: ${result.message}`);
                if (result.errors.length > 0) {
                    console.log(`Errors: ${result.errors.join(', ')}`);
                }
            }).catch(err => {
                console.error('❌ Validation error:', err);
            });
        }
    });
}

// Test PHP validation
async function testPHPValidation() {
    console.log('\n🐘 Testing PHP validation...');
    
    // Get PHP course levels
    db.getLevelsByCourse('javascript-php', (err, levels) => {
        if (err) {
            console.error('❌ Error fetching PHP levels:', err);
            return;
        }

        // Test Level 1: PHP Hello World
        const phpLevel = levels.find(l => l.expected_output === 'php_hello');
        if (phpLevel) {
            console.log(`\n📝 Testing PHP Hello: ${phpLevel.title}`);
            console.log(`Expected output: ${phpLevel.expected_output}`);
            
            const testCodePHP = `<?php
echo "Hello, World!";
echo "Welcome to PHP!";
?>`;

            validator.validatePHP(testCodePHP, phpLevel.level_number, phpLevel).then(result => {
                console.log(`✅ Result: ${result.passed ? 'PASSED' : 'FAILED'} (Score: ${result.score})`);
                console.log(`Message: ${result.message}`);
                if (result.errors.length > 0) {
                    console.log(`Errors: ${result.errors.join(', ')}`);
                }
            }).catch(err => {
                console.error('❌ Validation error:', err);
            });
        }
    });
}

// Run all tests
setTimeout(() => {
    testHTMLValidation();
    setTimeout(() => {
        testJavaScriptValidation();
        setTimeout(() => {
            testPHPValidation();
            setTimeout(() => {
                console.log('\n🎉 All validator tests completed!');
                process.exit(0);
            }, 3000);
        }, 2000);
    }, 2000);
}, 1000);
